@extends('layouts.app')

@section('title', 'Pricing Plans')

@section('content')
<div class="min-h-screen bg-gray-50 py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="text-center mb-12">
            <h1 class="text-4xl font-bold text-gray-900 mb-4">Choose Your Plan</h1>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Find the perfect plan for your hiring needs. Start with our free plan or upgrade for advanced features.
            </p>
        </div>

        <!-- Pricing Cards -->
        <div class="grid grid-cols-1 md:grid-cols-{{ min(count($plans), 3) }} gap-8 max-w-5xl mx-auto">
            @forelse($plans as $index => $plan)
                <div class="bg-white rounded-lg shadow-md p-8 border-2 {{ $index === 1 ? 'border-blue-500 relative' : 'border-gray-200' }}">
                    @if($index === 1)
                        <div class="absolute -top-4 left-1/2 transform -translate-x-1/2">
                            <span class="bg-blue-500 text-white px-4 py-1 rounded-full text-sm font-semibold">Most Popular</span>
                        </div>
                    @endif
                    
                    <div class="text-center">
                        <h3 class="text-2xl font-bold text-gray-900 mb-2">{{ $plan->name }}</h3>
                        <div class="text-4xl font-bold text-gray-900 mb-1">${{ number_format($plan->price, 0) }}</div>
                        <div class="text-gray-600 mb-6">per {{ $plan->billing_period === 'yearly' ? 'year' : 'month' }}</div>
                    </div>
                    
                    @if($plan->description)
                        <p class="text-gray-600 text-center mb-6">{{ $plan->description }}</p>
                    @endif
                    
                    <ul class="space-y-4 mb-8">
                        @foreach($plan->features as $feature)
                            <li class="flex items-center">
                                <svg class="w-5 h-5 text-green-500 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                <span class="text-gray-700">{{ $feature }}</span>
                            </li>
                        @endforeach
                        
                        @if($plan->job_posting_limit)
                            <li class="flex items-center">
                                <svg class="w-5 h-5 text-green-500 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                <span class="text-gray-700">
                                    {{ $plan->hasUnlimitedJobPostings() ? 'Unlimited job postings' : 'Up to ' . $plan->job_posting_limit . ' job postings' }}
                                </span>
                            </li>
                        @endif
                        
                        @if($plan->featured_jobs)
                            <li class="flex items-center">
                                <svg class="w-5 h-5 text-green-500 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                <span class="text-gray-700">Featured job listings</span>
                            </li>
                        @endif
                        
                        @if($plan->resume_database_access)
                            <li class="flex items-center">
                                <svg class="w-5 h-5 text-green-500 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                <span class="text-gray-700">Resume database access</span>
                            </li>
                        @endif
                        
                        @if($plan->advanced_analytics)
                            <li class="flex items-center">
                                <svg class="w-5 h-5 text-green-500 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                <span class="text-gray-700">Advanced analytics</span>
                            </li>
                        @endif
                    </ul>
                    
                    @auth
                        @if($plan->isFree())
                            <button class="w-full bg-gray-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-gray-700 transition-colors">
                                Current Plan
                            </button>
                        @else
                            <a href="{{ route('subscription.checkout', $plan->slug) }}" class="block w-full text-center {{ $index === 1 ? 'bg-blue-600 hover:bg-blue-700' : 'bg-gray-600 hover:bg-gray-700' }} text-white py-3 px-6 rounded-lg font-semibold transition-colors">
                                {{ $plan->isFree() ? 'Get Started' : 'Subscribe Now' }}
                            </a>
                        @endif
                    @else
                        <a href="{{ route('register') }}" class="block w-full text-center {{ $index === 1 ? 'bg-blue-600 hover:bg-blue-700' : 'bg-gray-600 hover:bg-gray-700' }} text-white py-3 px-6 rounded-lg font-semibold transition-colors">
                            Get Started
                        </a>
                    @endauth
                </div>
            @empty
                <div class="col-span-full text-center py-12">
                    <p class="text-gray-500 text-lg">No subscription plans available at the moment.</p>
                </div>
            @endforelse
        </div>

        <!-- FAQ Section -->
        <div class="mt-16 max-w-4xl mx-auto">
            <h2 class="text-3xl font-bold text-gray-900 text-center mb-8">Frequently Asked Questions</h2>
            <div class="space-y-6">
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Can I change my plan at any time?</h3>
                    <p class="text-gray-600">Yes, you can upgrade or downgrade your plan at any time. Changes will be reflected in your next billing cycle.</p>
                </div>
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Is there a free trial available?</h3>
                    <p class="text-gray-600">Yes, we offer a 14-day free trial for our Professional plan. No credit card required to start.</p>
                </div>
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">What payment methods do you accept?</h3>
                    <p class="text-gray-600">We accept all major credit cards, PayPal, and bank transfers for Enterprise customers.</p>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection