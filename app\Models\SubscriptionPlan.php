<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\HasMany;

class SubscriptionPlan extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'stripe_price_id',
        'stripe_product_id',
        'price',
        'billing_period',
        'description',
        'features',
        'job_posting_limit',
        'featured_jobs',
        'resume_database_access',
        'advanced_analytics',
        'is_active',
        'sort_order',
    ];

    protected $casts = [
        'features' => 'array',
        'price' => 'decimal:2',
        'job_posting_limit' => 'integer',
        'featured_jobs' => 'integer',
        'resume_database_access' => 'boolean',
        'advanced_analytics' => 'boolean',
        'is_active' => 'boolean',
        'sort_order' => 'integer',
    ];

    /**
     * Get the subscriptions for this plan.
     */
    public function subscriptions(): HasMany
    {
        return $this->hasMany(Subscription::class);
    }

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName(): string
    {
        return 'slug';
    }

    /**
     * Scope a query to only include active plans.
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to order by sort order.
     */
    public function scopeOrdered(Builder $query): Builder
    {
        return $query->orderBy('sort_order')->orderBy('price');
    }

    /**
     * Scope a query to filter by billing period.
     */
    public function scopeBillingPeriod(Builder $query, string $period): Builder
    {
        return $query->where('billing_period', $period);
    }

    /**
     * Check if this is a free plan.
     */
    public function isFree(): bool
    {
        return $this->price == 0;
    }

    /**
     * Check if this plan allows unlimited job postings.
     */
    public function hasUnlimitedJobPostings(): bool
    {
        return $this->job_posting_limit === null || $this->job_posting_limit === -1;
    }

    /**
     * Check if this plan allows featured jobs.
     */
    public function allowsFeaturedJobs(): bool
    {
        return $this->featured_jobs > 0;
    }

    /**
     * Check if this plan has resume database access.
     */
    public function hasResumeDatabaseAccess(): bool
    {
        return $this->resume_database_access;
    }

    /**
     * Check if this plan has advanced analytics.
     */
    public function hasAdvancedAnalytics(): bool
    {
        return $this->advanced_analytics;
    }

    /**
     * Get the formatted price.
     */
    public function getFormattedPriceAttribute(): string
    {
        if ($this->isFree()) {
            return 'Free';
        }

        return '$' . number_format($this->price, 2);
    }

    /**
     * Get the price per month (for annual plans).
     */
    public function getMonthlyPriceAttribute(): float
    {
        if ($this->billing_period === 'yearly') {
            return $this->price / 12;
        }

        return $this->price;
    }

    /**
     * Get the formatted monthly price.
     */
    public function getFormattedMonthlyPriceAttribute(): string
    {
        if ($this->isFree()) {
            return 'Free';
        }

        return '$' . number_format($this->monthly_price, 2);
    }

    /**
     * Get the billing period label.
     */
    public function getBillingPeriodLabelAttribute(): string
    {
        return match($this->billing_period) {
            'monthly' => 'per month',
            'yearly' => 'per year',
            'lifetime' => 'one-time',
            default => $this->billing_period
        };
    }

    /**
     * Get the savings percentage for yearly plans.
     */
    public function getYearlySavingsAttribute(): ?int
    {
        if ($this->billing_period !== 'yearly') {
            return null;
        }

        // Find the monthly equivalent plan
        $monthlyPlan = static::where('name', $this->name)
            ->where('billing_period', 'monthly')
            ->first();

        if (!$monthlyPlan) {
            return null;
        }

        $yearlyTotal = $monthlyPlan->price * 12;
        $savings = (($yearlyTotal - $this->price) / $yearlyTotal) * 100;

        return round($savings);
    }

    /**
     * Get the plan badge color.
     */
    public function getBadgeColorAttribute(): string
    {
        if ($this->isFree()) {
            return 'bg-gray-100 text-gray-800';
        }

        if ($this->price < 50) {
            return 'bg-blue-100 text-blue-800';
        }

        if ($this->price < 100) {
            return 'bg-purple-100 text-purple-800';
        }

        return 'bg-gold-100 text-gold-800';
    }

    /**
     * Check if this plan is popular (middle tier).
     */
    public function isPopular(): bool
    {
        $activePlans = static::active()->ordered()->get();
        $middleIndex = floor($activePlans->count() / 2);
        
        return $activePlans->search(function ($plan) {
            return $plan->id === $this->id;
        }) === $middleIndex;
    }

    /**
     * Get the feature list as HTML.
     */
    public function getFeaturesHtmlAttribute(): string
    {
        if (empty($this->features)) {
            return '';
        }

        $html = '<ul class="space-y-2">';
        foreach ($this->features as $feature) {
            $html .= '<li class="flex items-center"><svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg>' . htmlspecialchars($feature) . '</li>';
        }
        $html .= '</ul>';

        return $html;
    }
}