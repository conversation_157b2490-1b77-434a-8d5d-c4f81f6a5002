<?php

namespace Database\Seeders;

use App\Models\JobApplication;
use App\Models\Job;
use App\Models\JobSeekerProfile;
use App\Models\Resume;
use Illuminate\Database\Seeder;
use Carbon\Carbon;

class JobApplicationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $jobs = Job::where('status', 'active')->get();
        $jobSeekerProfiles = JobSeekerProfile::all();

        if ($jobs->isEmpty() || $jobSeekerProfiles->isEmpty()) {
            $this->command->warn('No active jobs or job seeker profiles found. Please run JobSeeder and UserSeeder first.');
            return;
        }

        $applicationStatuses = ['submitted', 'viewed', 'in_review', 'shortlisted', 'interviewed', 'rejected', 'hired'];

        $coverLetterTemplates = [
            "Dear Hiring Manager,\n\nI am writing to express my strong interest in the {job_title} position at {company_name}. With my background in {field}, I believe I would be a valuable addition to your team.\n\nI am particularly excited about this opportunity because of {company_name}'s reputation for innovation and excellence. My experience in {skills} aligns well with the requirements outlined in the job posting.\n\nI would welcome the opportunity to discuss how my skills and enthusiasm can contribute to your team's success.\n\nBest regards,\n{applicant_name}",

            "Hello,\n\nI am excited to apply for the {job_title} role at {company_name}. Your company's mission resonates with my professional values, and I am eager to contribute to your continued success.\n\nIn my previous roles, I have developed strong skills in {skills} which I believe make me an ideal candidate for this position. I am particularly drawn to the collaborative culture at {company_name} and the opportunity to work on innovative projects.\n\nThank you for considering my application. I look forward to the possibility of discussing this opportunity further.\n\nSincerely,\n{applicant_name}",

            "Dear Team,\n\nI am thrilled to submit my application for the {job_title} position. As someone passionate about {field}, I am excited about the possibility of joining {company_name} and contributing to your team's objectives.\n\nMy experience includes {skills}, and I am confident that my background aligns well with your needs. I am particularly impressed by {company_name}'s commitment to {value} and would love to be part of such a forward-thinking organization.\n\nI appreciate your time and consideration.\n\nWarm regards,\n{applicant_name}"
        ];

        // Create applications for each job
        foreach ($jobs as $job) {
            $applicationCount = fake()->numberBetween(0, 15);
            $applicants = $jobSeekerProfiles->random(min($applicationCount, $jobSeekerProfiles->count()));

            foreach ($applicants as $applicant) {
                // Check if this applicant already applied to this job
                $existingApplication = JobApplication::where('job_id', $job->id)
                    ->where('job_seeker_profile_id', $applicant->id)
                    ->first();

                if ($existingApplication) {
                    continue; // Skip if already applied
                }

                // Get or create a resume for this applicant
                $resume = Resume::where('job_seeker_profile_id', $applicant->id)->first();
                if (!$resume) {
                    $resume = Resume::create([
                        'job_seeker_profile_id' => $applicant->id,
                        'title' => $applicant->user->name . "'s Resume",
                        'content' => $this->generateResumeContent($applicant),
                        'is_default' => true,
                    ]);
                }

                $status = fake()->randomElement($applicationStatuses);
                $createdAt = fake()->dateTimeBetween($job->created_at, 'now');

                // Generate personalized cover letter
                $coverLetter = $this->generateCoverLetter(
                    fake()->randomElement($coverLetterTemplates),
                    $job,
                    $applicant
                );

                $application = JobApplication::create([
                    'job_id' => $job->id,
                    'job_seeker_profile_id' => $applicant->id,
                    'resume_id' => $resume->id,
                    'cover_letter' => $coverLetter,
                    'status' => $status,
                    'employer_notes' => $this->generateEmployerNotes($status),
                    'viewed_at' => $status !== 'submitted' ? fake()->dateTimeBetween($createdAt, 'now') : null,
                    'status_updated_at' => $status !== 'submitted' ? fake()->dateTimeBetween($createdAt, 'now') : null,
                    'created_at' => $createdAt,
                    'updated_at' => $createdAt,
                ]);
            }

            // Update job applications count
            $job->update([
                'applications_count' => $job->applications()->count()
            ]);
        }
    }

    private function generateCoverLetter($template, $job, $applicant)
    {
        $skills = is_array($applicant->skills) ? implode(', ', array_slice($applicant->skills, 0, 3)) : 'relevant technologies';
        $field = $job->industry ?? 'technology';
        $value = fake()->randomElement(['innovation', 'excellence', 'customer satisfaction', 'quality', 'growth']);

        return str_replace(
            ['{job_title}', '{company_name}', '{field}', '{skills}', '{applicant_name}', '{value}'],
            [$job->title, $job->employerProfile->company_name, $field, $skills, $applicant->user->name, $value],
            $template
        );
    }

    private function generateEmployerNotes($status)
    {
        $notes = [
            'submitted' => null,
            'viewed' => 'Application reviewed by hiring team.',
            'in_review' => 'Application under detailed review by hiring team.',
            'shortlisted' => 'Candidate meets initial requirements. Moving to next round.',
            'interviewed' => 'Completed initial interview. Positive feedback from team.',
            'hired' => 'Strong candidate. Offer extended and accepted.',
            'rejected' => fake()->randomElement([
                'Thank you for your interest. We have decided to move forward with other candidates.',
                'While your background is impressive, we found a better fit for this role.',
                'We appreciate your application but have chosen to pursue other candidates.'
            ])
        ];

        return $notes[$status];
    }

    private function generateResumeContent($applicant)
    {
        $skills = is_array($applicant->skills) ? implode(', ', $applicant->skills) : 'Various technical skills';

        return [
            'personal_info' => [
                'name' => $applicant->user->name,
                'email' => $applicant->user->email,
                'phone' => $applicant->phone_number,
                'location' => $applicant->location,
                'linkedin' => $applicant->linkedin_url,
                'github' => $applicant->github_url,
            ],
            'summary' => $applicant->summary,
            'skills' => $skills,
            'experience' => [
                [
                    'title' => fake()->jobTitle(),
                    'company' => fake()->company(),
                    'duration' => fake()->numberBetween(1, 5) . ' years',
                    'description' => 'Responsible for various technical and professional duties in a dynamic environment.'
                ]
            ],
            'education' => [
                [
                    'degree' => fake()->randomElement(['Bachelor of Science', 'Bachelor of Arts', 'Master of Science']),
                    'field' => fake()->randomElement(['Computer Science', 'Engineering', 'Business', 'Marketing']),
                    'school' => fake()->randomElement(['State University', 'Tech Institute', 'Community College']),
                    'year' => fake()->numberBetween(2015, 2023)
                ]
            ]
        ];
    }
}
