<div class="max-w-4xl mx-auto p-6">
    <div class="bg-white rounded-lg shadow-lg">
        <!-- Header -->
        <div class="border-b border-gray-200 px-6 py-4">
            <h2 class="text-2xl font-bold text-gray-900">Resume Builder</h2>
            <p class="text-gray-600 mt-1">Create a professional resume in minutes</p>
        </div>

        <!-- Progress Steps -->
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                @foreach(['Personal Info', 'Experience', 'Education', 'Skills', 'Review'] as $index => $stepName)
                    <div class="flex items-center {{ $index < count($steps) - 1 ? 'flex-1' : '' }}">
                        <div class="flex items-center justify-center w-8 h-8 rounded-full {{ $currentStep >= $index + 1 ? 'bg-blue-600 text-white' : 'bg-gray-300 text-gray-600' }}">
                            {{ $index + 1 }}
                        </div>
                        <span class="ml-2 text-sm {{ $currentStep >= $index + 1 ? 'text-blue-600 font-medium' : 'text-gray-500' }}">{{ $stepName }}</span>
                        @if($index < count($steps) - 1)
                            <div class="flex-1 h-0.5 mx-4 {{ $currentStep > $index + 1 ? 'bg-blue-600' : 'bg-gray-300' }}"></div>
                        @endif
                    </div>
                @endforeach
            </div>
        </div>

        <!-- Form Content -->
        <div class="p-6">
            @if($currentStep === 1)
                <!-- Personal Information -->
                <div class="space-y-6">
                    <h3 class="text-lg font-semibold text-gray-900">Personal Information</h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Full Name</label>
                            <input type="text" wire:model="personalInfo.full_name" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            @error('personalInfo.full_name') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                            <input type="email" wire:model="personalInfo.email" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            @error('personalInfo.email') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Phone</label>
                            <input type="text" wire:model="personalInfo.phone" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            @error('personalInfo.phone') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Location</label>
                            <input type="text" wire:model="personalInfo.location" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            @error('personalInfo.location') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                        </div>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Professional Summary</label>
                        <textarea wire:model="personalInfo.summary" rows="4" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                        @error('personalInfo.summary') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                    </div>
                </div>
            @elseif($currentStep === 2)
                <!-- Experience -->
                <div class="space-y-6">
                    <div class="flex justify-between items-center">
                        <h3 class="text-lg font-semibold text-gray-900">Work Experience</h3>
                        <button wire:click="addExperience" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                            Add Experience
                        </button>
                    </div>
                    
                    @foreach($experience as $index => $exp)
                        <div class="border border-gray-200 rounded-lg p-4">
                            <div class="flex justify-between items-start mb-4">
                                <h4 class="font-medium text-gray-900">Experience {{ $index + 1 }}</h4>
                                @if(count($experience) > 1)
                                    <button wire:click="removeExperience({{ $index }})" class="text-red-600 hover:text-red-800">
                                        Remove
                                    </button>
                                @endif
                            </div>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Job Title</label>
                                    <input type="text" wire:model="experience.{{ $index }}.title" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Company</label>
                                    <input type="text" wire:model="experience.{{ $index }}.company" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
                                    <input type="date" wire:model="experience.{{ $index }}.start_date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">End Date</label>
                                    <input type="date" wire:model="experience.{{ $index }}.end_date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                            </div>
                            
                            <div class="mt-4">
                                <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                                <textarea wire:model="experience.{{ $index }}.description" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                            </div>
                        </div>
                    @endforeach
                </div>
            @elseif($currentStep === 3)
                <!-- Education -->
                <div class="space-y-6">
                    <div class="flex justify-between items-center">
                        <h3 class="text-lg font-semibold text-gray-900">Education</h3>
                        <button wire:click="addEducation" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                            Add Education
                        </button>
                    </div>
                    
                    @foreach($education as $index => $edu)
                        <div class="border border-gray-200 rounded-lg p-4">
                            <div class="flex justify-between items-start mb-4">
                                <h4 class="font-medium text-gray-900">Education {{ $index + 1 }}</h4>
                                @if(count($education) > 1)
                                    <button wire:click="removeEducation({{ $index }})" class="text-red-600 hover:text-red-800">
                                        Remove
                                    </button>
                                @endif
                            </div>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Degree</label>
                                    <input type="text" wire:model="education.{{ $index }}.degree" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Institution</label>
                                    <input type="text" wire:model="education.{{ $index }}.institution" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Start Year</label>
                                    <input type="number" wire:model="education.{{ $index }}.start_year" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">End Year</label>
                                    <input type="number" wire:model="education.{{ $index }}.end_year" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            @elseif($currentStep === 4)
                <!-- Skills -->
                <div class="space-y-6">
                    <h3 class="text-lg font-semibold text-gray-900">Skills & Certifications</h3>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Skills (comma-separated)</label>
                        <textarea wire:model="skills" rows="3" placeholder="e.g., JavaScript, React, Node.js, Python" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Certifications (comma-separated)</label>
                        <textarea wire:model="certifications" rows="3" placeholder="e.g., AWS Certified Developer, Google Analytics Certified" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Languages (comma-separated)</label>
                        <textarea wire:model="languages" rows="2" placeholder="e.g., English (Native), Spanish (Fluent)" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                    </div>
                </div>
            @elseif($currentStep === 5)
                <!-- Review -->
                <div class="space-y-6">
                    <h3 class="text-lg font-semibold text-gray-900">Review & Save</h3>
                    
                    <div class="bg-gray-50 rounded-lg p-6">
                        <h4 class="font-medium text-gray-900 mb-4">Resume Preview</h4>
                        
                        <!-- Personal Info Preview -->
                        <div class="mb-6">
                            <h5 class="font-semibold text-lg">{{ $personalInfo['full_name'] ?? 'Your Name' }}</h5>
                            <p class="text-gray-600">{{ $personalInfo['email'] ?? '<EMAIL>' }} | {{ $personalInfo['phone'] ?? 'Phone' }} | {{ $personalInfo['location'] ?? 'Location' }}</p>
                            @if(!empty($personalInfo['summary']))
                                <p class="mt-2 text-gray-700">{{ $personalInfo['summary'] }}</p>
                            @endif
                        </div>
                        
                        <!-- Experience Preview -->
                        @if(!empty($experience))
                            <div class="mb-6">
                                <h5 class="font-semibold text-gray-900 mb-3">Experience</h5>
                                @foreach($experience as $exp)
                                    @if(!empty($exp['title']) || !empty($exp['company']))
                                        <div class="mb-3">
                                            <h6 class="font-medium">{{ $exp['title'] ?? 'Job Title' }} - {{ $exp['company'] ?? 'Company' }}</h6>
                                            <p class="text-sm text-gray-600">{{ $exp['start_date'] ?? 'Start' }} - {{ $exp['end_date'] ?? 'End' }}</p>
                                            @if(!empty($exp['description']))
                                                <p class="text-sm text-gray-700 mt-1">{{ $exp['description'] }}</p>
                                            @endif
                                        </div>
                                    @endif
                                @endforeach
                            </div>
                        @endif
                        
                        <!-- Education Preview -->
                        @if(!empty($education))
                            <div class="mb-6">
                                <h5 class="font-semibold text-gray-900 mb-3">Education</h5>
                                @foreach($education as $edu)
                                    @if(!empty($edu['degree']) || !empty($edu['institution']))
                                        <div class="mb-2">
                                            <h6 class="font-medium">{{ $edu['degree'] ?? 'Degree' }} - {{ $edu['institution'] ?? 'Institution' }}</h6>
                                            <p class="text-sm text-gray-600">{{ $edu['start_year'] ?? 'Start' }} - {{ $edu['end_year'] ?? 'End' }}</p>
                                        </div>
                                    @endif
                                @endforeach
                            </div>
                        @endif
                        
                        <!-- Skills Preview -->
                        @if(!empty($skills))
                            <div class="mb-4">
                                <h5 class="font-semibold text-gray-900 mb-2">Skills</h5>
                                <p class="text-gray-700">{{ $skills }}</p>
                            </div>
                        @endif
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Resume Title</label>
                        <input type="text" wire:model="resumeTitle" placeholder="e.g., Software Developer Resume" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        @error('resumeTitle') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                    </div>
                </div>
            @endif
        </div>

        <!-- Navigation Buttons -->
        <div class="border-t border-gray-200 px-6 py-4 flex justify-between">
            <button 
                wire:click="previousStep" 
                @if($currentStep === 1) disabled @endif
                class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
                Previous
            </button>
            
            <div class="flex space-x-3">
                @if($currentStep < 5)
                    <button 
                        wire:click="nextStep" 
                        class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                    >
                        Next
                    </button>
                @else
                    <button 
                        wire:click="saveResume" 
                        class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
                        wire:loading.attr="disabled"
                    >
                        <span wire:loading.remove>Save Resume</span>
                        <span wire:loading>Saving...</span>
                    </button>
                @endif
            </div>
        </div>
    </div>
    
    @if (session()->has('message'))
        <div class="mt-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
            {{ session('message') }}
        </div>
    @endif
</div>
