<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// API Version 1 Routes
Route::prefix('v1')->group(function () {
    // Public routes
    Route::get('/jobs', 'App\Http\Controllers\Api\V1\JobController@index');
    Route::get('/jobs/{job}', 'App\Http\Controllers\Api\V1\JobController@show');
    Route::get('/courses', 'App\Http\Controllers\Api\V1\CourseController@index');
    Route::get('/courses/{course}', 'App\Http\Controllers\Api\V1\CourseController@show');
    Route::get('/lessons/{lesson}', 'App\Http\Controllers\Api\V1\LessonController@show');
    
    // Authentication routes
    Route::post('/auth/register', 'App\Http\Controllers\Api\V1\AuthController@register');
    Route::post('/auth/login', 'App\Http\Controllers\Api\V1\AuthController@login');
    
    // Protected routes
    Route::middleware('auth:sanctum')->group(function () {
        // User profile
        Route::get('/user', 'App\Http\Controllers\Api\V1\UserController@profile');
        Route::put('/user', 'App\Http\Controllers\Api\V1\UserController@update');
        
        // Job seeker routes
        Route::get('/user/applications', 'App\Http\Controllers\Api\V1\JobApplicationController@index');
        Route::post('/jobs/{job}/apply', 'App\Http\Controllers\Api\V1\JobApplicationController@store');
        Route::get('/user/saved-jobs', 'App\Http\Controllers\Api\V1\SavedJobController@index');
        Route::post('/jobs/{job}/save', 'App\Http\Controllers\Api\V1\SavedJobController@store');
        Route::delete('/jobs/{job}/unsave', 'App\Http\Controllers\Api\V1\SavedJobController@destroy');
        Route::get('/user/saved-searches', 'App\Http\Controllers\Api\V1\SavedSearchController@index');
        Route::post('/saved-searches', 'App\Http\Controllers\Api\V1\SavedSearchController@store');
        Route::delete('/saved-searches/{savedSearch}', 'App\Http\Controllers\Api\V1\SavedSearchController@destroy');
        
        // Course enrollment routes
        Route::get('/user/enrollments', 'App\Http\Controllers\Api\V1\CourseEnrollmentController@index');
        Route::post('/courses/{course}/enroll', 'App\Http\Controllers\Api\V1\CourseEnrollmentController@store');
        Route::put('/enrollments/{enrollment}/progress', 'App\Http\Controllers\Api\V1\CourseEnrollmentController@updateProgress');
        
        // Employer routes
        Route::middleware('role:employer')->group(function () {
            Route::get('/employer/jobs', 'App\Http\Controllers\Api\V1\EmployerJobController@index');
            Route::post('/employer/jobs', 'App\Http\Controllers\Api\V1\EmployerJobController@store');
            Route::put('/employer/jobs/{job}', 'App\Http\Controllers\Api\V1\EmployerJobController@update');
            Route::delete('/employer/jobs/{job}', 'App\Http\Controllers\Api\V1\EmployerJobController@destroy');
            Route::get('/employer/jobs/{job}/applications', 'App\Http\Controllers\Api\V1\EmployerJobApplicationController@index');
            Route::put('/employer/applications/{application}/status', 'App\Http\Controllers\Api\V1\EmployerJobApplicationController@updateStatus');
        });
        
        // Admin routes
        Route::middleware('role:admin')->prefix('admin')->group(function () {
            Route::get('/users', 'App\Http\Controllers\Api\V1\Admin\UserController@index');
            Route::put('/users/{user}/role', 'App\Http\Controllers\Api\V1\Admin\UserController@updateRole');
            Route::put('/jobs/{job}/status', 'App\Http\Controllers\Api\V1\Admin\JobController@updateStatus');
            Route::get('/subscription-plans', 'App\Http\Controllers\Api\V1\Admin\SubscriptionPlanController@index');
            Route::post('/subscription-plans', 'App\Http\Controllers\Api\V1\Admin\SubscriptionPlanController@store');
            Route::put('/subscription-plans/{plan}', 'App\Http\Controllers\Api\V1\Admin\SubscriptionPlanController@update');
        });
    });
});