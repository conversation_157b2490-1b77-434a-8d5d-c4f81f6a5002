<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Facades\Storage;

class CourseEnrollment extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'course_id',
        'progress',
        'completed_at',
        'certificate_issued_at',
        'certificate_path',
        'completed_lessons',
        'last_accessed_at',
    ];

    protected $casts = [
        'completed_at' => 'datetime',
        'certificate_issued_at' => 'datetime',
        'completed_lessons' => 'array',
        'last_accessed_at' => 'datetime',
    ];

    /**
     * Get the user that owns the enrollment.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the course for this enrollment.
     */
    public function course()
    {
        return $this->belongsTo(Course::class);
    }

    /**
     * Check if the course is completed.
     */
    public function isCompleted(): bool
    {
        return $this->progress === 100 && !is_null($this->completed_at);
    }

    /**
     * Check if certificate has been issued.
     */
    public function hasCertificate(): bool
    {
        return !is_null($this->certificate_issued_at) && !is_null($this->certificate_path);
    }

    /**
     * Generate and issue certificate.
     */
    public function issueCertificate(): string
    {
        if (!$this->isCompleted()) {
            throw new \Exception('Course must be completed before issuing certificate.');
        }

        if ($this->hasCertificate()) {
            return $this->certificate_path;
        }

        $certificateData = [
            'user_name' => $this->user->name,
            'course_title' => $this->course->title,
            'completion_date' => $this->completed_at->format('F j, Y'),
            'certificate_id' => 'CERT-' . strtoupper(uniqid()),
            'course_duration' => $this->course->total_duration . ' minutes',
        ];

        $pdf = Pdf::loadView('certificates.template', $certificateData);
        
        $fileName = 'certificates/' . $this->user->id . '/' . $this->course->slug . '-' . time() . '.pdf';
        
        Storage::disk('public')->put($fileName, $pdf->output());

        $this->update([
            'certificate_issued_at' => now(),
            'certificate_path' => $fileName,
        ]);

        return $fileName;
    }

    /**
     * Get the certificate download URL.
     */
    public function getCertificateUrl(): ?string
    {
        if (!$this->hasCertificate()) {
            return null;
        }

        return Storage::disk('public')->url($this->certificate_path);
    }

    /**
     * Get the public certificate verification URL.
     */
    public function getCertificateVerificationUrl(): ?string
    {
        if (!$this->hasCertificate()) {
            return null;
        }

        return route('certificates.verify', $this->id);
    }

    /**
     * Update progress based on completed lessons.
     */
    public function updateProgress(): void
    {
        $totalLessons = $this->course->lessons->count();
        $completedLessonsCount = count($this->completed_lessons ?? []);
        
        if ($totalLessons === 0) {
            return;
        }

        $progress = round(($completedLessonsCount / $totalLessons) * 100);
        
        $updateData = [
            'progress' => $progress,
            'last_accessed_at' => now(),
        ];

        // Mark as completed if 100% progress
        if ($progress === 100 && !$this->completed_at) {
            $updateData['completed_at'] = now();
        }

        $this->update($updateData);
    }

    /**
     * Get the next lesson to complete.
     */
    public function getNextLesson(): ?Lesson
    {
        $completedLessonIds = $this->completed_lessons ?? [];
        
        return $this->course->lessons()
            ->whereNotIn('id', $completedLessonIds)
            ->orderBy('order')
            ->first();
    }

    /**
     * Get the current lesson (last accessed or next to complete).
     */
    public function getCurrentLesson(): ?Lesson
    {
        return $this->getNextLesson() ?? $this->course->lessons()->orderBy('order')->first();
    }

    /**
     * Get progress percentage as a formatted string.
     */
    public function getProgressPercentageAttribute(): string
    {
        return $this->progress . '%';
    }
}