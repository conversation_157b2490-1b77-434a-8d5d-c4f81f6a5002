<?php

namespace App\Http\Controllers;

use App\Models\Job;
use App\Models\JobApplication;
use App\Models\Resume;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;

class JobApplicationController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('role:job_seeker');
    }

    /**
     * Display a listing of the user's job applications.
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $jobSeekerProfile = $user->jobSeekerProfile;

        if (!$jobSeekerProfile) {
            return redirect()->route('profile.edit')
                ->with('error', 'Please complete your job seeker profile first.');
        }

        $query = JobApplication::with(['job.employerProfile.user', 'resume'])
            ->where('job_seeker_profile_id', $jobSeekerProfile->id);

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        // Search by job title or company
        if ($request->filled('search')) {
            $searchTerm = $request->search;
            $query->whereHas('job', function ($q) use ($searchTerm) {
                $q->where('title', 'like', "%{$searchTerm}%")
                  ->orWhereHas('employerProfile', function ($eq) use ($searchTerm) {
                      $eq->where('company_name', 'like', "%{$searchTerm}%");
                  });
            });
        }

        // Sort options
        $sortBy = $request->get('sort', 'created_at');
        $sortOrder = $request->get('order', 'desc');

        switch ($sortBy) {
            case 'job_title':
                $query->join('job_listings', 'job_applications.job_id', '=', 'job_listings.id')
                      ->orderBy('job_listings.title', $sortOrder)
                      ->select('job_applications.*');
                break;
            case 'company':
                $query->join('job_listings', 'job_applications.job_id', '=', 'job_listings.id')
                      ->join('employer_profiles', 'job_listings.employer_profile_id', '=', 'employer_profiles.id')
                      ->orderBy('employer_profiles.company_name', $sortOrder)
                      ->select('job_applications.*');
                break;
            case 'status':
                $query->orderBy('status', $sortOrder);
                break;
            default:
                $query->orderBy('created_at', $sortOrder);
        }

        $applications = $query->paginate(10)->withQueryString();

        // Get status counts for filter tabs
        $statusCounts = [
            'all' => JobApplication::where('job_seeker_profile_id', $jobSeekerProfile->id)->count(),
            'pending' => JobApplication::where('job_seeker_profile_id', $jobSeekerProfile->id)
                ->where('status', 'pending')->count(),
            'reviewed' => JobApplication::where('job_seeker_profile_id', $jobSeekerProfile->id)
                ->where('status', 'reviewed')->count(),
            'shortlisted' => JobApplication::where('job_seeker_profile_id', $jobSeekerProfile->id)
                ->where('status', 'shortlisted')->count(),
            'interviewed' => JobApplication::where('job_seeker_profile_id', $jobSeekerProfile->id)
                ->where('status', 'interviewed')->count(),
            'rejected' => JobApplication::where('job_seeker_profile_id', $jobSeekerProfile->id)
                ->where('status', 'rejected')->count(),
            'hired' => JobApplication::where('job_seeker_profile_id', $jobSeekerProfile->id)
                ->where('status', 'hired')->count(),
        ];

        return view('applications.index', compact('applications', 'statusCounts'));
    }

    /**
     * Show the form for creating a new job application.
     */
    public function create(Job $job)
    {
        $user = Auth::user();
        $jobSeekerProfile = $user->jobSeekerProfile;

        if (!$jobSeekerProfile) {
            return redirect()->route('profile.edit')
                ->with('error', 'Please complete your job seeker profile first.');
        }

        // Check if job is active and not expired
        if ($job->status !== 'active' || $job->expires_at <= now()) {
            return redirect()->route('jobs.show', $job)
                ->with('error', 'This job is no longer available for applications.');
        }

        // Check if user has already applied
        $existingApplication = JobApplication::where('job_seeker_profile_id', $jobSeekerProfile->id)
            ->where('job_id', $job->id)
            ->first();

        if ($existingApplication) {
            return redirect()->route('jobs.show', $job)
                ->with('info', 'You have already applied for this job.');
        }

        // Get user's resumes
        $resumes = Resume::where('job_seeker_profile_id', $jobSeekerProfile->id)
            ->orderBy('created_at', 'desc')
            ->get();

        return view('applications.create', compact('job', 'resumes'));
    }

    /**
     * Store a newly created job application.
     */
    public function store(Request $request, Job $job)
    {
        $user = Auth::user();
        $jobSeekerProfile = $user->jobSeekerProfile;

        if (!$jobSeekerProfile) {
            return redirect()->route('profile.edit')
                ->with('error', 'Please complete your job seeker profile first.');
        }

        // Check if job is active and not expired
        if ($job->status !== 'active' || $job->expires_at <= now()) {
            return back()->with('error', 'This job is no longer available for applications.');
        }

        // Check if user has already applied
        $existingApplication = JobApplication::where('job_seeker_profile_id', $jobSeekerProfile->id)
            ->where('job_id', $job->id)
            ->first();

        if ($existingApplication) {
            return back()->with('info', 'You have already applied for this job.');
        }

        $request->validate([
            'cover_letter' => 'required|string|max:2000',
            'resume_id' => [
                'nullable',
                Rule::exists('resumes', 'id')->where(function ($query) use ($jobSeekerProfile) {
                    $query->where('job_seeker_profile_id', $jobSeekerProfile->id);
                })
            ],
            'resume_file' => 'nullable|file|mimes:pdf,doc,docx|max:5120', // 5MB max
            'expected_salary' => 'nullable|numeric|min:0',
            'available_from' => 'nullable|date|after_or_equal:today',
            'additional_info' => 'nullable|string|max:1000'
        ]);

        $resumeId = $request->resume_id;

        // Handle resume file upload if provided
        if ($request->hasFile('resume_file')) {
            $resumeFile = $request->file('resume_file');
            $resumePath = $resumeFile->store('resumes', 'public');
            
            // Create a new resume record
            $resume = Resume::create([
                'job_seeker_profile_id' => $jobSeekerProfile->id,
                'title' => 'Resume for ' . $job->title,
                'file_path' => $resumePath,
                'file_name' => $resumeFile->getClientOriginalName(),
                'file_size' => $resumeFile->getSize(),
                'is_default' => false
            ]);
            
            $resumeId = $resume->id;
        }

        // Create the job application
        $application = JobApplication::create([
            'job_id' => $job->id,
            'job_seeker_profile_id' => $jobSeekerProfile->id,
            'resume_id' => $resumeId,
            'cover_letter' => $request->cover_letter,
            'expected_salary' => $request->expected_salary,
            'available_from' => $request->available_from,
            'additional_info' => $request->additional_info,
            'status' => 'pending',
            'applied_at' => now()
        ]);

        // Increment job applications count
        $job->increment('applications_count');

        // Send notification to employer (you can implement this later)
        // event(new JobApplicationSubmitted($application));

        return redirect()->route('applications.show', $application)
            ->with('success', 'Your application has been submitted successfully!');
    }

    /**
     * Display the specified job application.
     */
    public function show(JobApplication $application)
    {
        $user = Auth::user();
        $jobSeekerProfile = $user->jobSeekerProfile;

        // Check if application belongs to the authenticated user
        if ($application->job_seeker_profile_id !== $jobSeekerProfile->id) {
            abort(403);
        }

        // Load relationships
        $application->load([
            'job.employerProfile.user',
            'resume',
            'notes' => function ($query) {
                $query->orderBy('created_at', 'desc');
            }
        ]);

        return view('applications.show', compact('application'));
    }

    /**
     * Show the form for editing the specified job application.
     */
    public function edit(JobApplication $application)
    {
        $user = Auth::user();
        $jobSeekerProfile = $user->jobSeekerProfile;

        // Check if application belongs to the authenticated user
        if ($application->job_seeker_profile_id !== $jobSeekerProfile->id) {
            abort(403);
        }

        // Check if application can be edited (only pending applications)
        if ($application->status !== 'pending') {
            return back()->with('error', 'You can only edit pending applications.');
        }

        // Get user's resumes
        $resumes = Resume::where('job_seeker_profile_id', $jobSeekerProfile->id)
            ->orderBy('created_at', 'desc')
            ->get();

        $application->load('job');

        return view('applications.edit', compact('application', 'resumes'));
    }

    /**
     * Update the specified job application.
     */
    public function update(Request $request, JobApplication $application)
    {
        $user = Auth::user();
        $jobSeekerProfile = $user->jobSeekerProfile;

        // Check if application belongs to the authenticated user
        if ($application->job_seeker_profile_id !== $jobSeekerProfile->id) {
            abort(403);
        }

        // Check if application can be updated (only pending applications)
        if ($application->status !== 'pending') {
            return back()->with('error', 'You can only edit pending applications.');
        }

        $request->validate([
            'cover_letter' => 'required|string|max:2000',
            'resume_id' => [
                'nullable',
                Rule::exists('resumes', 'id')->where(function ($query) use ($jobSeekerProfile) {
                    $query->where('job_seeker_profile_id', $jobSeekerProfile->id);
                })
            ],
            'resume_file' => 'nullable|file|mimes:pdf,doc,docx|max:5120',
            'expected_salary' => 'nullable|numeric|min:0',
            'available_from' => 'nullable|date|after_or_equal:today',
            'additional_info' => 'nullable|string|max:1000'
        ]);

        $resumeId = $request->resume_id;

        // Handle resume file upload if provided
        if ($request->hasFile('resume_file')) {
            $resumeFile = $request->file('resume_file');
            $resumePath = $resumeFile->store('resumes', 'public');
            
            // Create a new resume record
            $resume = Resume::create([
                'job_seeker_profile_id' => $jobSeekerProfile->id,
                'title' => 'Updated Resume for ' . $application->job->title,
                'file_path' => $resumePath,
                'file_name' => $resumeFile->getClientOriginalName(),
                'file_size' => $resumeFile->getSize(),
                'is_default' => false
            ]);
            
            $resumeId = $resume->id;
        }

        // Update the application
        $application->update([
            'resume_id' => $resumeId,
            'cover_letter' => $request->cover_letter,
            'expected_salary' => $request->expected_salary,
            'available_from' => $request->available_from,
            'additional_info' => $request->additional_info
        ]);

        return redirect()->route('applications.show', $application)
            ->with('success', 'Your application has been updated successfully!');
    }

    /**
     * Remove the specified job application.
     */
    public function destroy(JobApplication $application)
    {
        $user = Auth::user();
        $jobSeekerProfile = $user->jobSeekerProfile;

        // Check if application belongs to the authenticated user
        if ($application->job_seeker_profile_id !== $jobSeekerProfile->id) {
            abort(403);
        }

        // Check if application can be withdrawn (only pending and reviewed applications)
        if (!in_array($application->status, ['pending', 'reviewed'])) {
            return back()->with('error', 'You can only withdraw pending or reviewed applications.');
        }

        // Update status to withdrawn instead of deleting
        $application->update([
            'status' => 'withdrawn',
            'withdrawn_at' => now()
        ]);

        // Decrement job applications count
        $application->job->decrement('applications_count');

        return redirect()->route('applications.index')
            ->with('success', 'Your application has been withdrawn successfully.');
    }
}