<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
        xmlns:image="http://www.google.com/schemas/sitemap-image/1.1">
    
    <!-- Homepage -->
    <url>
        <loc>{{ url('/') }}</loc>
        <lastmod>{{ now()->toISOString() }}</lastmod>
        <changefreq>daily</changefreq>
        <priority>1.0</priority>
    </url>
    
    <!-- Static Pages -->
    <url>
        <loc>{{ route('jobs.index') }}</loc>
        <lastmod>{{ now()->toISOString() }}</lastmod>
        <changefreq>daily</changefreq>
        <priority>0.9</priority>
    </url>
    
    <url>
        <loc>{{ route('courses.index') }}</loc>
        <lastmod>{{ now()->toISOString() }}</lastmod>
        <changefreq>daily</changefreq>
        <priority>0.9</priority>
    </url>
    
    <url>
        <loc>{{ route('companies.index') }}</loc>
        <lastmod>{{ now()->toISOString() }}</lastmod>
        <changefreq>weekly</changefreq>
        <priority>0.8</priority>
    </url>
    
    <url>
        <loc>{{ route('pricing') }}</loc>
        <lastmod>{{ now()->toISOString() }}</lastmod>
        <changefreq>monthly</changefreq>
        <priority>0.7</priority>
    </url>
    
    <!-- Dynamic Job Listings -->
    @php
        $jobs = \App\Models\Job::where('status', 'active')
            ->where('expires_at', '>', now())
            ->orderBy('updated_at', 'desc')
            ->limit(1000)
            ->get();
    @endphp
    
    @foreach($jobs as $job)
    <url>
        <loc>{{ route('jobs.show', $job) }}</loc>
        <lastmod>{{ $job->updated_at->toISOString() }}</lastmod>
        <changefreq>weekly</changefreq>
        <priority>0.8</priority>
    </url>
    @endforeach
    
    <!-- Dynamic Course Listings -->
    @php
        $courses = \App\Models\Course::where('is_published', true)
            ->orderBy('updated_at', 'desc')
            ->limit(1000)
            ->get();
    @endphp
    
    @foreach($courses as $course)
    <url>
        <loc>{{ route('courses.show', $course) }}</loc>
        <lastmod>{{ $course->updated_at->toISOString() }}</lastmod>
        <changefreq>weekly</changefreq>
        <priority>0.8</priority>
    </url>
    @endforeach
    
    <!-- Company Pages -->
    @php
        $companies = \App\Models\EmployerProfile::whereHas('user')
            ->with('user')
            ->orderBy('updated_at', 'desc')
            ->limit(500)
            ->get();
    @endphp
    
    @foreach($companies as $company)
    <url>
        <loc>{{ route('companies.show', $company) }}</loc>
        <lastmod>{{ $company->updated_at->toISOString() }}</lastmod>
        <changefreq>monthly</changefreq>
        <priority>0.6</priority>
    </url>
    @endforeach
    
</urlset>