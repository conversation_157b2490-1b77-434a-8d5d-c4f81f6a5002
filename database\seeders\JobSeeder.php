<?php

namespace Database\Seeders;

use App\Models\Job;
use App\Models\EmployerProfile;
use Illuminate\Database\Seeder;
use Carbon\Carbon;

class JobSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $employerProfiles = EmployerProfile::all();
        
        if ($employerProfiles->isEmpty()) {
            $this->command->warn('No employer profiles found. Please run UserSeeder first.');
            return;
        }

        $jobTitles = [
            'Software Engineer',
            'Frontend Developer',
            'Backend Developer',
            'Full Stack Developer',
            'DevOps Engineer',
            'Data Scientist',
            'Product Manager',
            'UX/UI Designer',
            'Digital Marketing Specialist',
            'Sales Representative',
            'Customer Success Manager',
            'Business Analyst',
            'Project Manager',
            'Quality Assurance Engineer',
            'Mobile App Developer',
            'Cybersecurity Specialist',
            'Database Administrator',
            'System Administrator',
            'Content Writer',
            'Graphic Designer',
            'HR Specialist',
            'Financial Analyst',
            'Operations Manager',
            'Marketing Manager',
            'Social Media Manager'
        ];

        $industries = [
            'Technology', 'Healthcare', 'Finance', 'Education', 'Manufacturing',
            'Retail', 'Marketing', 'Consulting', 'Real Estate', 'Transportation',
            'Media', 'Gaming', 'E-commerce', 'Telecommunications', 'Energy'
        ];

        $locations = [
            'New York, NY', 'San Francisco, CA', 'Los Angeles, CA', 'Chicago, IL',
            'Boston, MA', 'Seattle, WA', 'Austin, TX', 'Denver, CO', 'Atlanta, GA',
            'Miami, FL', 'Dallas, TX', 'Phoenix, AZ', 'Philadelphia, PA', 'Detroit, MI',
            'Portland, OR', 'Nashville, TN', 'Charlotte, NC', 'Minneapolis, MN'
        ];

        $skills = [
            'PHP', 'Laravel', 'JavaScript', 'React', 'Vue.js', 'Node.js', 'Python',
            'Java', 'C#', 'Ruby', 'Go', 'Swift', 'Kotlin', 'TypeScript', 'Angular',
            'Docker', 'Kubernetes', 'AWS', 'Azure', 'GCP', 'MySQL', 'PostgreSQL',
            'MongoDB', 'Redis', 'Git', 'CI/CD', 'Agile', 'Scrum', 'REST API',
            'GraphQL', 'Machine Learning', 'Data Analysis', 'Project Management',
            'Communication', 'Leadership', 'Problem Solving', 'Teamwork'
        ];

        foreach ($employerProfiles as $employer) {
            $jobCount = fake()->numberBetween(1, 8);
            
            for ($i = 0; $i < $jobCount; $i++) {
                $title = fake()->randomElement($jobTitles);
                $isRemote = fake()->boolean(40);
                $location = $isRemote ? 'Remote' : fake()->randomElement($locations);
                $salaryMin = fake()->numberBetween(40000, 120000);
                $salaryMax = $salaryMin + fake()->numberBetween(20000, 80000);
                $requiredSkills = fake()->randomElements($skills, fake()->numberBetween(3, 8));
                $preferredSkills = fake()->randomElements($skills, fake()->numberBetween(2, 5));
                
                Job::create([
                    'employer_profile_id' => $employer->id,
                    'title' => $title,
                    'description' => $this->generateJobDescription($title, $employer->company_name),
                    'location' => $location,
                    'job_type' => fake()->randomElement(['Full-time', 'Part-time', 'Contract', 'Internship']),
                    'experience_level' => fake()->randomElement(['Entry-level', 'Mid-level', 'Senior', 'Manager']),
                    'industry' => fake()->randomElement($industries),
                    'salary_min' => $salaryMin,
                    'salary_max' => $salaryMax,
                    'salary_negotiable' => fake()->boolean(30),
                    'required_skills' => $requiredSkills,
                    'preferred_skills' => $preferredSkills,
                    'requirements' => implode("\n• ", array_merge([''], $this->generateRequirements($title))),
                    'benefits' => implode("\n• ", array_merge([''], $this->generateBenefits())),
                    'is_remote' => $isRemote,
                    'is_featured' => fake()->boolean(20),
                    'status' => fake()->randomElement(['active', 'paused', 'closed']),
                    'expires_at' => Carbon::now()->addDays(fake()->numberBetween(7, 90)),
                    'views_count' => fake()->numberBetween(0, 500),
                    'applications_count' => fake()->numberBetween(0, 50),
                    'created_at' => fake()->dateTimeBetween('-30 days', 'now'),
                ]);
            }
        }
    }

    private function generateJobDescription($title, $companyName)
    {
        $descriptions = [
            'Software Engineer' => "We are seeking a talented Software Engineer to join our dynamic team at {$companyName}. You will be responsible for developing high-quality software solutions, collaborating with cross-functional teams, and contributing to our innovative projects.",
            'Frontend Developer' => "Join {$companyName} as a Frontend Developer and help create amazing user experiences. You'll work with modern frameworks and technologies to build responsive, interactive web applications.",
            'Backend Developer' => "We're looking for a skilled Backend Developer to join {$companyName}. You'll design and implement server-side logic, APIs, and database systems that power our applications.",
            'Product Manager' => "As a Product Manager at {$companyName}, you'll drive product strategy, work with engineering teams, and ensure we're building products that delight our customers.",
            'UX/UI Designer' => "Join our design team at {$companyName} as a UX/UI Designer. You'll create intuitive, beautiful interfaces and conduct user research to improve our products.",
            'Data Scientist' => "We're seeking a Data Scientist to join {$companyName} and help us make data-driven decisions. You'll analyze complex datasets and build predictive models."
        ];

        return $descriptions[$title] ?? "We are looking for a talented {$title} to join our team at {$companyName}. This is an exciting opportunity to work on challenging projects and grow your career with us.";
    }

    private function generateRequirements($title)
    {
        $baseRequirements = [
            "Bachelor's degree in relevant field or equivalent experience",
            "Strong problem-solving and analytical skills",
            "Excellent communication and teamwork abilities",
            "Ability to work in a fast-paced environment"
        ];

        $techRequirements = [
            'Software Engineer' => ["3+ years of software development experience", "Proficiency in multiple programming languages", "Experience with version control systems"],
            'Frontend Developer' => ["2+ years of frontend development experience", "Proficiency in HTML, CSS, and JavaScript", "Experience with modern frontend frameworks"],
            'Backend Developer' => ["3+ years of backend development experience", "Experience with databases and API development", "Knowledge of server-side technologies"],
            'Data Scientist' => ["2+ years of data analysis experience", "Proficiency in Python or R", "Experience with machine learning frameworks"]
        ];

        $specific = $techRequirements[$title] ?? ["2+ years of relevant experience", "Strong technical skills"];
        
        return array_merge($baseRequirements, $specific);
    }

    private function generateBenefits()
    {
        $allBenefits = [
            "Competitive salary and equity package",
            "Comprehensive health, dental, and vision insurance",
            "401(k) retirement plan with company matching",
            "Flexible work hours and remote work options",
            "Professional development opportunities",
            "Generous paid time off and holidays",
            "Modern office space with free snacks and drinks",
            "Team building events and company outings",
            "Learning and development budget",
            "Gym membership or wellness stipend",
            "Commuter benefits",
            "Parental leave policy",
            "Stock options",
            "Performance bonuses"
        ];

        return fake()->randomElements($allBenefits, fake()->numberBetween(5, 10));
    }
}