<?php $__env->startSection('title', 'Job Analytics'); ?>

<?php $__env->startSection('content'); ?>
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Job Analytics</h1>
            <p class="text-gray-600">Comprehensive insights into job postings and applications</p>
        </div>
        <div class="flex items-center space-x-4">
            <!-- Period Filter -->
            <form method="GET" class="flex items-center space-x-2">
                <select name="period" onchange="this.form.submit()" class="rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500">
                    <option value="7" <?php echo e($period == '7' ? 'selected' : ''); ?>>Last 7 days</option>
                    <option value="30" <?php echo e($period == '30' ? 'selected' : ''); ?>>Last 30 days</option>
                    <option value="90" <?php echo e($period == '90' ? 'selected' : ''); ?>>Last 90 days</option>
                    <option value="365" <?php echo e($period == '365' ? 'selected' : ''); ?>>Last year</option>
                </select>
            </form>
            
            <!-- Export Button -->
            <a href="<?php echo e(route('admin.analytics.export', 'jobs')); ?>?period=<?php echo e($period); ?>" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                Export CSV
            </a>
        </div>
    </div>

    <!-- Key Metrics -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6a2 2 0 01-2 2H8a2 2 0 01-2-2V8a2 2 0 012-2V6"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Total Jobs</dt>
                        <dd class="text-lg font-medium text-gray-900"><?php echo e(number_format($jobStats['total_jobs'])); ?></dd>
                    </dl>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">New Jobs</dt>
                        <dd class="text-lg font-medium text-gray-900"><?php echo e(number_format($jobStats['new_jobs'])); ?></dd>
                    </dl>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Active Jobs</dt>
                        <dd class="text-lg font-medium text-gray-900"><?php echo e(number_format($jobStats['active_jobs'])); ?></dd>
                    </dl>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Total Applications</dt>
                        <dd class="text-lg font-medium text-gray-900"><?php echo e(number_format($jobStats['application_stats']['total_applications'])); ?></dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Job Growth Chart -->
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Job Posting Trend</h3>
            <div class="h-64">
                <canvas id="jobGrowthChart"></canvas>
            </div>
        </div>

        <!-- Jobs by Type Chart -->
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Jobs by Type</h3>
            <div class="h-64">
                <canvas id="jobTypeChart"></canvas>
            </div>
        </div>
    </div>

    <!-- Additional Charts -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Jobs by Status Chart -->
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Jobs by Status</h3>
            <div class="h-64">
                <canvas id="jobStatusChart"></canvas>
            </div>
        </div>

        <!-- Application Status Chart -->
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Applications by Status</h3>
            <div class="h-64">
                <canvas id="applicationStatusChart"></canvas>
            </div>
        </div>
    </div>

    <!-- Detailed Statistics -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Top Industries -->
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Top Industries</h3>
            <div class="space-y-3">
                <?php $__currentLoopData = $jobStats['jobs_by_industry']->take(10); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $industry => $count): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-gray-900"><?php echo e($industry); ?></span>
                    <span class="text-sm text-gray-500"><?php echo e(number_format($count)); ?> jobs</span>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>

        <!-- Application Statistics -->
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Application Statistics</h3>
            <div class="space-y-4">
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-gray-900">Total Applications</span>
                    <span class="text-sm text-gray-500"><?php echo e(number_format($jobStats['application_stats']['total_applications'])); ?></span>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-gray-900">New Applications</span>
                    <span class="text-sm text-gray-500"><?php echo e(number_format($jobStats['application_stats']['new_applications'])); ?></span>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-gray-900">Average per Job</span>
                    <span class="text-sm text-gray-500">
                        <?php echo e($jobStats['total_jobs'] > 0 ? number_format($jobStats['application_stats']['total_applications'] / $jobStats['total_jobs'], 1) : 0); ?>

                    </span>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Job Growth Chart
const jobGrowthCtx = document.getElementById('jobGrowthChart').getContext('2d');
const jobGrowthChart = new Chart(jobGrowthCtx, {
    type: 'line',
    data: {
        labels: <?php echo json_encode($jobStats['job_growth']->pluck('date')); ?>,
        datasets: [{
            label: 'New Jobs',
            data: <?php echo json_encode($jobStats['job_growth']->pluck('count')); ?>,
            borderColor: 'rgb(59, 130, 246)',
            backgroundColor: 'rgba(59, 130, 246, 0.1)',
            tension: 0.1
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// Job Type Chart
const jobTypeCtx = document.getElementById('jobTypeChart').getContext('2d');
const jobTypeChart = new Chart(jobTypeCtx, {
    type: 'doughnut',
    data: {
        labels: <?php echo json_encode($jobStats['jobs_by_type']->keys()); ?>,
        datasets: [{
            data: <?php echo json_encode($jobStats['jobs_by_type']->values()); ?>,
            backgroundColor: [
                'rgb(59, 130, 246)',
                'rgb(16, 185, 129)',
                'rgb(139, 92, 246)',
                'rgb(245, 158, 11)',
                'rgb(239, 68, 68)'
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false
    }
});

// Job Status Chart
const jobStatusCtx = document.getElementById('jobStatusChart').getContext('2d');
const jobStatusChart = new Chart(jobStatusCtx, {
    type: 'bar',
    data: {
        labels: <?php echo json_encode($jobStats['jobs_by_status']->keys()); ?>,
        datasets: [{
            label: 'Jobs',
            data: <?php echo json_encode($jobStats['jobs_by_status']->values()); ?>,
            backgroundColor: 'rgba(59, 130, 246, 0.8)'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// Application Status Chart
const applicationStatusCtx = document.getElementById('applicationStatusChart').getContext('2d');
const applicationStatusChart = new Chart(applicationStatusCtx, {
    type: 'doughnut',
    data: {
        labels: <?php echo json_encode($jobStats['application_stats']['applications_by_status']->keys()); ?>,
        datasets: [{
            data: <?php echo json_encode($jobStats['application_stats']['applications_by_status']->values()); ?>,
            backgroundColor: [
                'rgb(59, 130, 246)',
                'rgb(16, 185, 129)',
                'rgb(139, 92, 246)',
                'rgb(245, 158, 11)',
                'rgb(239, 68, 68)',
                'rgb(107, 114, 128)'
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false
    }
});
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\laravel\rectra\resources\views/admin/analytics/jobs.blade.php ENDPATH**/ ?>