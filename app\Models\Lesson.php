<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;

class Lesson extends Model
{
    use HasFactory;

    protected $fillable = [
        'course_id',
        'title',
        'slug',
        'description',
        'content_type',
        'content',
        'video_url',
        'duration',
        'order',
        'is_preview',
        'resources',
    ];

    protected $casts = [
        'is_preview' => 'boolean',
        'resources' => 'array',
    ];

    /**
     * Get the course that owns the lesson.
     */
    public function course()
    {
        return $this->belongsTo(Course::class);
    }

    /**
     * Scope a query to only include preview lessons.
     */
    public function scopePreview(Builder $query): Builder
    {
        return $query->where('is_preview', true);
    }

    /**
     * Scope a query to order by lesson order.
     */
    public function scopeOrdered(Builder $query): Builder
    {
        return $query->orderBy('order');
    }

    /**
     * Check if the lesson is a video lesson.
     */
    public function isVideo(): bool
    {
        return $this->content_type === 'video';
    }

    /**
     * Check if the lesson is a text lesson.
     */
    public function isText(): bool
    {
        return $this->content_type === 'text';
    }

    /**
     * Check if the lesson is a quiz.
     */
    public function isQuiz(): bool
    {
        return $this->content_type === 'quiz';
    }

    /**
     * Get the duration in human readable format.
     */
    public function getDurationHumanAttribute(): string
    {
        if (!$this->duration) {
            return 'N/A';
        }

        $hours = floor($this->duration / 60);
        $minutes = $this->duration % 60;

        if ($hours > 0) {
            return $hours . 'h ' . $minutes . 'm';
        }

        return $minutes . 'm';
    }

    /**
     * Get the next lesson in the course.
     */
    public function getNextLesson(): ?self
    {
        return $this->course->lessons()
            ->where('order', '>', $this->order)
            ->orderBy('order')
            ->first();
    }

    /**
     * Get the previous lesson in the course.
     */
    public function getPreviousLesson(): ?self
    {
        return $this->course->lessons()
            ->where('order', '<', $this->order)
            ->orderBy('order', 'desc')
            ->first();
    }

    /**
     * Check if a user can access this lesson.
     */
    public function canUserAccess(User $user): bool
    {
        // Preview lessons are always accessible
        if ($this->is_preview) {
            return true;
        }

        // Check if user is enrolled in the course
        return $this->course->isUserEnrolled($user);
    }

    /**
     * Mark lesson as completed for a user.
     */
    public function markAsCompleted(User $user): void
    {
        $enrollment = $this->course->enrollments()->where('user_id', $user->id)->first();
        
        if (!$enrollment) {
            return;
        }

        $completedLessons = $enrollment->completed_lessons ?? [];
        
        if (!in_array($this->id, $completedLessons)) {
            $completedLessons[] = $this->id;
            
            $totalLessons = $this->course->lessons->count();
            $progress = round((count($completedLessons) / $totalLessons) * 100);
            
            $enrollment->update([
                'completed_lessons' => $completedLessons,
                'progress' => $progress,
                'completed_at' => $progress === 100 ? now() : null,
                'last_accessed_at' => now(),
            ]);
        }
    }

    /**
     * Check if lesson is completed by a user.
     */
    public function isCompletedBy(User $user): bool
    {
        $enrollment = $this->course->enrollments()->where('user_id', $user->id)->first();
        
        if (!$enrollment) {
            return false;
        }

        return in_array($this->id, $enrollment->completed_lessons ?? []);
    }

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName(): string
    {
        return 'slug';
    }
}