<?php

namespace App\Http\Controllers;

use App\Models\Course;
use App\Models\CourseEnrollment;
use App\Models\Lesson;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class CourseController extends Controller
{
    /**
     * Display a listing of courses.
     */
    public function index(Request $request)
    {
        $query = Course::with(['media', 'lessons'])
            ->where('is_published', true);

        // Search by keywords
        if ($request->filled('search')) {
            $searchTerm = $request->search;
            $query->where(function ($q) use ($searchTerm) {
                $q->where('title', 'like', "%{$searchTerm}%")
                  ->orWhere('description', 'like', "%{$searchTerm}%")
                  ->orWhere('learning_objectives', 'like', "%{$searchTerm}%");
            });
        }

        // Filter by category
        if ($request->filled('category')) {
            $query->where('category', $request->category);
        }

        // Filter by level
        if ($request->filled('level')) {
            $query->where('level', $request->level);
        }

        // Filter by price
        if ($request->filled('price_filter')) {
            switch ($request->price_filter) {
                case 'free':
                    $query->where('price', 0);
                    break;
                case 'paid':
                    $query->where('price', '>', 0);
                    break;
            }
        }

        // Filter by duration
        if ($request->filled('duration')) {
            switch ($request->duration) {
                case 'short':
                    $query->where('duration_hours', '<=', 5);
                    break;
                case 'medium':
                    $query->whereBetween('duration_hours', [6, 20]);
                    break;
                case 'long':
                    $query->where('duration_hours', '>', 20);
                    break;
            }
        }

        // Filter by rating
        if ($request->filled('min_rating')) {
            $query->where('rating', '>=', $request->min_rating);
        }

        // Filter by certificate
        if ($request->filled('certificate')) {
            if ($request->certificate === 'true') {
                $query->where('has_certificate', true);
            }
        }

        // Filter by featured
        if ($request->filled('featured')) {
            if ($request->featured === 'true') {
                $query->where('is_featured', true);
            }
        }

        // Sort options
        $sortBy = $request->get('sort', 'created_at');
        $sortOrder = $request->get('order', 'desc');

        switch ($sortBy) {
            case 'title':
                $query->orderBy('title', $sortOrder);
                break;
            case 'price':
                $query->orderBy('price', $sortOrder);
                break;
            case 'rating':
                $query->orderBy('rating', $sortOrder);
                break;
            case 'students':
                $query->orderBy('enrolled_count', $sortOrder);
                break;
            case 'duration':
                $query->orderBy('duration_hours', $sortOrder);
                break;
            default:
                $query->orderBy('created_at', $sortOrder);
        }

        // Get courses with pagination
        $courses = $query->paginate(12)->withQueryString();

        // Get filter options
        $categories = Course::select('category')
            ->where('is_published', true)
            ->distinct()
            ->whereNotNull('category')
            ->pluck('category')
            ->sort();

        $levels = ['beginner', 'intermediate', 'advanced'];

        // Get featured categories for the bottom section
        $featuredCategories = Course::select('category', DB::raw('COUNT(*) as course_count'))
            ->where('is_published', true)
            ->whereNotNull('category')
            ->groupBy('category')
            ->orderBy('course_count', 'desc')
            ->limit(8)
            ->get();

        // SEO Optimization for courses listing
        $title = 'Online Courses';
        $description = 'Discover professional training courses to advance your career and learn new skills.';
        
        if ($request->filled('search')) {
            $title = 'Courses for "' . $request->search . '"';
            $description = 'Learn ' . $request->search . ' with our professional training courses.';
        }
        
        if ($request->filled('category')) {
            $title = ucfirst($request->category) . ' Courses';
            $description = 'Master ' . $request->category . ' skills with our comprehensive training courses.';
        }
        
        \App\Helpers\SeoHelper::setTitle($title . ' | Rectra')
            ->setDescription($description)
            ->setKeywords(['courses', 'training', 'education', 'learning', 'skills', 'professional development'])
            ->setUrl(request()->fullUrl())
            ->addStructuredData(\App\Helpers\SeoHelper::generateWebsiteStructuredData());

        return view('courses.index', compact(
            'courses',
            'categories',
            'levels',
            'featuredCategories'
        ));
    }

    /**
     * Display the specified course.
     */
    public function show(Course $course)
    {
        // Check if course is published
        if (!$course->is_published) {
            abort(404);
        }

        // Load relationships
        $course->load([
            'lessons' => function ($query) {
                $query->orderBy('order');
            },
            'media',
            'enrollments' => function ($query) {
                if (Auth::check()) {
                    $query->where('user_id', Auth::id());
                }
            }
        ]);

        // SEO Optimization
        \App\Helpers\SeoHelper::setTitle($course->title . ' - Online Course | Rectra')
            ->setDescription(strip_tags(substr($course->description, 0, 160)) . '...')
            ->setKeywords([
                $course->title,
                $course->category,
                $course->level,
                'course',
                'training',
                'education',
                'learning'
            ])
            ->setUrl(route('courses.show', $course))
            ->setType('article')
            ->addStructuredData(\App\Helpers\SeoHelper::generateCourseStructuredData($course));

        // Check if user is enrolled
        $isEnrolled = false;
        $enrollment = null;
        
        if (Auth::check()) {
            $enrollment = $course->enrollments->first();
            $isEnrolled = $enrollment !== null;
        }

        // Get preview lessons
        $previewLessons = $course->lessons->where('is_preview', true);

        // Get related courses
        $relatedCourses = Course::with(['media'])
            ->where('id', '!=', $course->id)
            ->where('is_published', true)
            ->where('category', $course->category)
            ->limit(4)
            ->get();

        // Get course tags
        $tags = $course->tags ? explode(',', $course->tags) : [];

        return view('courses.show', compact(
            'course',
            'isEnrolled',
            'enrollment',
            'previewLessons',
            'relatedCourses',
            'tags'
        ));
    }

    /**
     * Display courses by category.
     */
    public function category(Request $request, $category)
    {
        $query = Course::with(['media', 'lessons'])
            ->where('is_published', true)
            ->where('category', $category);

        // Apply other filters if present
        if ($request->filled('level')) {
            $query->where('level', $request->level);
        }

        if ($request->filled('price_filter')) {
            switch ($request->price_filter) {
                case 'free':
                    $query->where('price', 0);
                    break;
                case 'paid':
                    $query->where('price', '>', 0);
                    break;
            }
        }

        $courses = $query->orderBy('created_at', 'desc')->paginate(12)->withQueryString();

        $levels = ['beginner', 'intermediate', 'advanced'];

        return view('courses.category', compact('courses', 'category', 'levels'));
    }

    /**
     * Display user's enrolled courses.
     */
    public function myLearning()
    {
        $this->middleware('auth');

        $enrollments = CourseEnrollment::with(['course.media', 'course.lessons'])
            ->where('user_id', Auth::id())
            ->orderBy('created_at', 'desc')
            ->paginate(12);

        return view('courses.my-learning', compact('enrollments'));
    }

    /**
     * Enroll user in a course.
     */
    public function enroll(Course $course)
    {
        $this->middleware('auth');

        // Check if course is published
        if (!$course->is_published) {
            return back()->with('error', 'This course is not available for enrollment.');
        }

        // Check if user is already enrolled
        $existingEnrollment = CourseEnrollment::where('user_id', Auth::id())
            ->where('course_id', $course->id)
            ->first();

        if ($existingEnrollment) {
            return redirect()->route('courses.learn', $course)
                ->with('info', 'You are already enrolled in this course.');
        }

        // Create enrollment
        $enrollment = CourseEnrollment::create([
            'user_id' => Auth::id(),
            'course_id' => $course->id,
            'enrolled_at' => now(),
            'status' => 'active'
        ]);

        // Increment course enrollment count
        $course->increment('enrolled_count');

        return redirect()->route('courses.learn', $course)
            ->with('success', 'Successfully enrolled in the course!');
    }

    /**
     * Display course learning interface.
     */
    public function learn(Course $course)
    {
        $this->middleware('auth');

        // Check if user is enrolled
        $enrollment = CourseEnrollment::where('user_id', Auth::id())
            ->where('course_id', $course->id)
            ->first();

        if (!$enrollment) {
            return redirect()->route('courses.show', $course)
                ->with('error', 'You need to enroll in this course first.');
        }

        // Load course with lessons
        $course->load([
            'lessons' => function ($query) {
                $query->orderBy('order');
            }
        ]);

        return view('courses.learn', compact('course', 'enrollment'));
    }

    /**
     * Mark a lesson as completed.
     */
    public function completeLesson(Course $course, Lesson $lesson)
    {
        $this->middleware('auth');

        // Check if user is enrolled
        $enrollment = CourseEnrollment::where('user_id', Auth::id())
            ->where('course_id', $course->id)
            ->first();

        if (!$enrollment) {
            return response()->json(['error' => 'Not enrolled in this course'], 403);
        }

        // Check if lesson belongs to the course
        if ($lesson->course_id !== $course->id) {
            return response()->json(['error' => 'Lesson does not belong to this course'], 400);
        }

        // Update completed lessons
        $completedLessons = $enrollment->completed_lessons ? json_decode($enrollment->completed_lessons, true) : [];
        
        if (!in_array($lesson->id, $completedLessons)) {
            $completedLessons[] = $lesson->id;
            $enrollment->completed_lessons = json_encode($completedLessons);
            
            // Calculate progress
            $totalLessons = $course->lessons()->count();
            $completedCount = count($completedLessons);
            $progress = $totalLessons > 0 ? ($completedCount / $totalLessons) * 100 : 0;
            
            $enrollment->progress = $progress;
            
            // Check if course is completed
            if ($progress >= 100) {
                $enrollment->status = 'completed';
                $enrollment->completed_at = now();
            }
            
            $enrollment->save();
        }

        return response()->json([
            'success' => true,
            'progress' => $enrollment->progress,
            'completed' => $enrollment->status === 'completed'
        ]);
    }

    /**
     * Display user's certificates.
     */
    public function certificates()
    {
        $this->middleware('auth');

        $completedEnrollments = CourseEnrollment::with(['course'])
            ->where('user_id', Auth::id())
            ->where('status', 'completed')
            ->whereHas('course', function ($query) {
                $query->where('has_certificate', true);
            })
            ->orderBy('completed_at', 'desc')
            ->get();

        return view('courses.certificates', compact('completedEnrollments'));
    }

    /**
     * Download course certificate.
     */
    public function downloadCertificate(CourseEnrollment $enrollment)
    {
        $this->middleware('auth');

        // Check if enrollment belongs to the authenticated user
        if ($enrollment->user_id !== Auth::id()) {
            abort(403);
        }

        // Check if course is completed and has certificate
        if ($enrollment->status !== 'completed' || !$enrollment->course->has_certificate) {
            return back()->with('error', 'Certificate not available for this course.');
        }

        // Generate and download certificate (implementation depends on your certificate generation logic)
        // This is a placeholder - you would implement actual certificate generation here
        
        return response()->download(
            storage_path('certificates/sample-certificate.pdf'),
            "certificate-{$enrollment->course->slug}.pdf"
        );
    }
}