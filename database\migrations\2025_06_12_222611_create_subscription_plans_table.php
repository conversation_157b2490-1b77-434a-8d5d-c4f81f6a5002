<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('subscription_plans', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('slug')->unique();
            $table->string('stripe_price_id')->nullable()->unique();
            $table->string('stripe_product_id')->nullable();
            $table->decimal('price', 10, 2);
            $table->enum('billing_period', ['monthly', 'yearly'])->default('monthly');
            $table->text('description')->nullable();
            $table->json('features'); // Array of features included in this plan
            $table->integer('job_posting_limit')->nullable(); // Number of active job postings allowed
            $table->boolean('featured_jobs')->default(false); // Can post featured jobs
            $table->boolean('resume_database_access')->default(false); // Can search resumes
            $table->boolean('advanced_analytics')->default(false); // Access to advanced analytics
            $table->boolean('is_active')->default(true);
            $table->integer('sort_order')->default(0);
            $table->timestamps();
            
            $table->index('is_active');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('subscription_plans');
    }
};