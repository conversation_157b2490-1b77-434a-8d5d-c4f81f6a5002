<?php

namespace Database\Seeders;

use App\Models\Subscription;
use App\Models\User;
use App\Models\SubscriptionPlan;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class SubscriptionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get existing users and subscription plans
        $users = User::where('role', 'employer')->get();
        $plans = SubscriptionPlan::all();
        
        if ($users->isEmpty() || $plans->isEmpty()) {
            $this->command->info('No users or subscription plans found. Skipping subscription seeding.');
            return;
        }

        // Create active subscriptions for some employers
        $activeSubscriptions = min(15, $users->count());
        $users->take($activeSubscriptions)->each(function ($user) use ($plans) {
            Subscription::factory()
                ->active()
                ->create([
                    'user_id' => $user->id,
                    'subscription_plan_id' => $plans->random()->id,
                ]);
        });

        // Create some cancelled subscriptions
        $users->skip($activeSubscriptions)->take(5)->each(function ($user) use ($plans) {
            Subscription::factory()
                ->cancelled()
                ->create([
                    'user_id' => $user->id,
                    'subscription_plan_id' => $plans->random()->id,
                ]);
        });

        // Create some expired subscriptions
        $users->skip($activeSubscriptions + 5)->take(3)->each(function ($user) use ($plans) {
            Subscription::factory()
                ->expired()
                ->create([
                    'user_id' => $user->id,
                    'subscription_plan_id' => $plans->random()->id,
                ]);
        });

        $this->command->info('Subscriptions seeded successfully!');
    }
}