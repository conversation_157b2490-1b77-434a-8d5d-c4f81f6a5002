<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class EmployerProfile extends Model implements HasMedia
{
    use HasFactory, InteractsWithMedia;

    protected $fillable = [
        'user_id',
        'company_name',
        'description',
        'logo_path',
        'banner_path',
        'website',
        'industry',
        'company_size',
        'location',
        'phone_number',
        'linkedin_url',
        'is_verified',
    ];

    protected $casts = [
        'is_verified' => 'boolean',
    ];

    /**
     * Get the user that owns the profile.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the jobs posted by this employer.
     */
    public function jobs()
    {
        return $this->hasMany(Job::class);
    }

    /**
     * Get the active jobs posted by this employer.
     */
    public function activeJobs()
    {
        return $this->hasMany(Job::class)->where('status', 'active');
    }

    /**
     * Get the company reviews for this employer.
     */
    public function companyReviews()
    {
        return $this->hasMany(CompanyReview::class);
    }

    /**
     * Get the approved company reviews.
     */
    public function approvedReviews()
    {
        return $this->hasMany(CompanyReview::class)->where('is_approved', true);
    }

    /**
     * Get the average rating for this company.
     */
    public function getAverageRatingAttribute()
    {
        return $this->approvedReviews()->avg('rating') ?? 0;
    }

    /**
     * Get the total number of approved reviews.
     */
    public function getReviewsCountAttribute()
    {
        return $this->approvedReviews()->count();
    }

    /**
     * Check if employer can post featured jobs.
     */
    public function canPostFeaturedJobs(): bool
    {
        return $this->user->subscribed('default') && 
               $this->user->subscription('default')->stripe_plan === 'premium';
    }

    /**
     * Get the job posting limit based on subscription.
     */
    public function getJobPostingLimit(): int
    {
        if (!$this->user->subscribed('default')) {
            return 1; // Free plan
        }

        $plan = $this->user->subscription('default')->stripe_plan;
        
        return match($plan) {
            'basic' => 5,
            'premium' => -1, // Unlimited
            default => 1
        };
    }
}