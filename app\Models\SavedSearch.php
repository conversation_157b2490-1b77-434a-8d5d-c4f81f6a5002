<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;

class SavedSearch extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'name',
        'search_criteria',
        'email_alerts',
        'alert_frequency',
        'last_alert_sent',
        'is_active',
    ];

    protected $casts = [
        'search_criteria' => 'array',
        'email_alerts' => 'boolean',
        'last_alert_sent' => 'datetime',
        'is_active' => 'boolean',
    ];

    /**
     * Get the user that owns the saved search.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope a query to only include active searches.
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to only include searches with email alerts enabled.
     */
    public function scopeWithEmailAlerts(Builder $query): Builder
    {
        return $query->where('email_alerts', true);
    }

    /**
     * Scope a query to filter by alert frequency.
     */
    public function scopeAlertFrequency(Builder $query, string $frequency): Builder
    {
        return $query->where('alert_frequency', $frequency);
    }

    /**
     * Scope a query to get searches that need alerts.
     */
    public function scopeNeedsAlert(Builder $query): Builder
    {
        return $query->active()
            ->withEmailAlerts()
            ->where(function ($q) {
                $q->whereNull('last_alert_sent')
                  ->orWhere('last_alert_sent', '<=', now()->subDay())
                  ->where('alert_frequency', 'daily');
            })
            ->orWhere(function ($q) {
                $q->where('last_alert_sent', '<=', now()->subWeek())
                  ->where('alert_frequency', 'weekly');
            })
            ->orWhere(function ($q) {
                $q->where('last_alert_sent', '<=', now()->subMonth())
                  ->where('alert_frequency', 'monthly');
            });
    }

    /**
     * Get jobs matching this saved search criteria.
     */
    public function getMatchingJobs()
    {
        $query = Job::query()->active();
        $criteria = $this->search_criteria;

        // Apply search filters based on criteria
        if (!empty($criteria['keywords'])) {
            $query->where(function ($q) use ($criteria) {
                $q->where('title', 'like', '%' . $criteria['keywords'] . '%')
                  ->orWhere('description', 'like', '%' . $criteria['keywords'] . '%');
            });
        }

        if (!empty($criteria['location'])) {
            $query->where('location', 'like', '%' . $criteria['location'] . '%');
        }

        if (!empty($criteria['job_type'])) {
            $query->where('job_type', $criteria['job_type']);
        }

        if (!empty($criteria['experience_level'])) {
            $query->where('experience_level', $criteria['experience_level']);
        }

        if (!empty($criteria['industry'])) {
            $query->where('industry', $criteria['industry']);
        }

        if (!empty($criteria['is_remote'])) {
            $query->where('is_remote', true);
        }

        if (!empty($criteria['salary_min'])) {
            $query->where('salary_min', '>=', $criteria['salary_min']);
        }

        if (!empty($criteria['salary_max'])) {
            $query->where('salary_max', '<=', $criteria['salary_max']);
        }

        return $query->orderBy('created_at', 'desc');
    }

    /**
     * Get new jobs since last alert.
     */
    public function getNewJobs()
    {
        $query = $this->getMatchingJobs();
        
        if ($this->last_alert_sent) {
            $query->where('created_at', '>', $this->last_alert_sent);
        }

        return $query->get();
    }

    /**
     * Mark alert as sent.
     */
    public function markAlertSent(): void
    {
        $this->update(['last_alert_sent' => now()]);
    }

    /**
     * Toggle the active status.
     */
    public function toggleActive(): void
    {
        $this->update(['is_active' => !$this->is_active]);
    }

    /**
     * Get the search criteria as a human-readable string.
     */
    public function getSearchSummaryAttribute(): string
    {
        $criteria = $this->search_criteria;
        $parts = [];

        if (!empty($criteria['keywords'])) {
            $parts[] = 'Keywords: ' . $criteria['keywords'];
        }

        if (!empty($criteria['location'])) {
            $parts[] = 'Location: ' . $criteria['location'];
        }

        if (!empty($criteria['job_type'])) {
            $parts[] = 'Type: ' . $criteria['job_type'];
        }

        if (!empty($criteria['experience_level'])) {
            $parts[] = 'Level: ' . $criteria['experience_level'];
        }

        if (!empty($criteria['is_remote'])) {
            $parts[] = 'Remote: Yes';
        }

        return implode(' | ', $parts) ?: 'All jobs';
    }
}