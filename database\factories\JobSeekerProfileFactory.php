<?php

namespace Database\Factories;

use App\Models\JobSeekerProfile;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\JobSeekerProfile>
 */
class JobSeekerProfileFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $skills = [
            'PHP', 'Laravel', 'JavaScript', 'React', 'Vue.js', 'Node.js', 'Python', 'Java',
            'C#', 'Ruby', 'Go', 'Swift', 'Kotlin', 'TypeScript', 'Angular', 'Docker',
            'Kubernetes', 'AWS', 'Azure', 'GCP', 'MySQL', 'PostgreSQL', 'MongoDB',
            'Redis', 'Git', 'CI/CD', 'Agile', 'Scrum', 'REST API', 'GraphQL',
            'Machine Learning', 'Data Analysis', 'Project Management', 'Communication',
            'Leadership', 'Problem Solving', 'Teamwork', 'Customer Service',
            'Marketing', 'Sales', 'Content Writing', 'SEO', 'Social Media Marketing',
            'Graphic Design', 'UI/UX Design', 'Adobe Creative Suite', 'Figma'
        ];

        $experienceLevel = fake()->randomElement(['Entry-level', 'Mid-level', 'Senior', 'Manager']);
        $salaryMin = fake()->numberBetween(30000, 100000);
        $salaryMax = $salaryMin + fake()->numberBetween(20000, 80000);

        return [
            'user_id' => User::factory(),
            'summary' => fake()->paragraph(3),
            'phone_number' => fake()->phoneNumber(),
            'location' => fake()->city() . ', ' . fake()->stateAbbr(),
            'website' => fake()->optional(0.3)->url(),
            'linkedin_url' => fake()->optional(0.8)->url(),
            'github_url' => fake()->optional(0.4)->url(),
            'skills' => fake()->randomElements($skills, fake()->numberBetween(3, 10)),
            'experience_level' => $experienceLevel,
            'desired_salary_min' => $salaryMin,
            'desired_salary_max' => $salaryMax,
            'open_to_remote' => fake()->boolean(70),
            'profile_completed' => fake()->boolean(80),
        ];
    }

    /**
     * Indicate that the profile is completed.
     */
    public function completed(): static
    {
        return $this->state(fn (array $attributes) => [
            'profile_completed' => true,
        ]);
    }

    /**
     * Indicate that the profile is incomplete.
     */
    public function incomplete(): static
    {
        return $this->state(fn (array $attributes) => [
            'profile_completed' => false,
            'summary' => null,
            'skills' => [],
        ]);
    }

    /**
     * Indicate that the profile is for an entry-level candidate.
     */
    public function entryLevel(): static
    {
        return $this->state(fn (array $attributes) => [
            'experience_level' => 'Entry-level',
            'desired_salary_min' => fake()->numberBetween(30000, 50000),
            'desired_salary_max' => fake()->numberBetween(50000, 70000),
        ]);
    }

    /**
     * Indicate that the profile is for a senior-level candidate.
     */
    public function seniorLevel(): static
    {
        return $this->state(fn (array $attributes) => [
            'experience_level' => 'Senior',
            'desired_salary_min' => fake()->numberBetween(80000, 120000),
            'desired_salary_max' => fake()->numberBetween(120000, 180000),
        ]);
    }

    /**
     * Indicate that the profile is open to remote work.
     */
    public function remotePreferred(): static
    {
        return $this->state(fn (array $attributes) => [
            'open_to_remote' => true,
        ]);
    }
}