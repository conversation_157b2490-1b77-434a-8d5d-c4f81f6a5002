<?php

namespace Database\Seeders;

use App\Models\SystemSetting;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class SystemSettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $settings = [
            // General Settings
            ['key' => 'site_name', 'value' => 'Rectra', 'type' => 'string', 'description' => 'Site name displayed in header and title'],
            ['key' => 'site_tagline', 'value' => 'Find Your Dream Job', 'type' => 'string', 'description' => 'Site tagline or slogan'],
            ['key' => 'site_description', 'value' => 'Rectra is a comprehensive job portal connecting talented professionals with leading companies.', 'type' => 'text', 'description' => 'Site description for SEO'],
            
            // Contact Information
            ['key' => 'contact_email', 'value' => '<EMAIL>', 'type' => 'email', 'description' => 'Primary contact email address'],
            ['key' => 'contact_phone', 'value' => '+****************', 'type' => 'string', 'description' => 'Primary contact phone number'],
            ['key' => 'contact_address', 'value' => '123 Business Street, Suite 100\nCity, State 12345', 'type' => 'text', 'description' => 'Business address'],
            
            // Application Settings
            ['key' => 'maintenance_mode', 'value' => '0', 'type' => 'boolean', 'description' => 'Enable maintenance mode'],
            ['key' => 'user_registration', 'value' => '1', 'type' => 'boolean', 'description' => 'Allow new user registration'],
            ['key' => 'email_verification', 'value' => '1', 'type' => 'boolean', 'description' => 'Require email verification for new users'],
            
            // Email Settings
            ['key' => 'mail_from_name', 'value' => 'Rectra', 'type' => 'string', 'description' => 'Default sender name for emails'],
            ['key' => 'mail_from_address', 'value' => '<EMAIL>', 'type' => 'email', 'description' => 'Default sender email address'],
            
            // Payment Settings
            ['key' => 'stripe_publishable_key', 'value' => '', 'type' => 'string', 'description' => 'Stripe publishable key'],
            ['key' => 'stripe_secret_key', 'value' => '', 'type' => 'string', 'description' => 'Stripe secret key'],
            ['key' => 'paypal_client_id', 'value' => '', 'type' => 'string', 'description' => 'PayPal client ID'],
            ['key' => 'paypal_client_secret', 'value' => '', 'type' => 'string', 'description' => 'PayPal client secret'],
            
            // SEO Settings
            ['key' => 'meta_title', 'value' => 'Rectra - Find Your Dream Job', 'type' => 'string', 'description' => 'Default meta title'],
            ['key' => 'meta_description', 'value' => 'Discover thousands of job opportunities and connect with top employers on Rectra, the leading job portal.', 'type' => 'text', 'description' => 'Default meta description'],
            ['key' => 'meta_keywords', 'value' => 'jobs, careers, employment, hiring, job search, recruitment', 'type' => 'text', 'description' => 'Default meta keywords'],
            ['key' => 'google_analytics_id', 'value' => '', 'type' => 'string', 'description' => 'Google Analytics tracking ID'],
            ['key' => 'google_tag_manager_id', 'value' => '', 'type' => 'string', 'description' => 'Google Tag Manager ID'],
        ];

        foreach ($settings as $setting) {
            SystemSetting::updateOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }
    }
}
