<?php

namespace Database\Seeders;

use App\Models\Lesson;
use App\Models\Course;
use Illuminate\Database\Seeder;

class LessonSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $courses = Course::all();
        
        if ($courses->isEmpty()) {
            $this->command->warn('No courses found. Please run CourseSeeder first.');
            return;
        }

        foreach ($courses as $course) {
            $this->createLessonsForCourse($course);
        }
    }

    private function createLessonsForCourse($course)
    {
        $lessonCount = fake()->numberBetween(5, 15);
        $lessonTemplates = $this->getLessonTemplatesByCategory($course->category);
        
        for ($i = 1; $i <= $lessonCount; $i++) {
            $template = fake()->randomElement($lessonTemplates);
            
            Lesson::create([
                'course_id' => $course->id,
                'title' => str_replace('{order}', $i, $template['title']),
                'slug' => $this->generateSlug($course->slug, $i, $template['title']),
                'description' => $template['description'],
                'content' => $this->generateMainContent($template['content_type'], $course->category),
                'video_url' => fake()->optional(0.8)->url(),
                'duration' => fake()->numberBetween(300, 3600), // 5 minutes to 1 hour
                'order' => $i,
                'is_preview' => $i <= 2 || fake()->boolean(20), // First 2 lessons free, or 20% chance
                'created_at' => fake()->dateTimeBetween($course->created_at, 'now'),
            ]);
        }
    }

    private function getLessonTemplatesByCategory($category)
    {
        $templates = [
            'Programming' => [
                ['title' => 'Introduction to Programming Fundamentals', 'description' => 'Learn the basic concepts of programming', 'content_type' => 'theory'],
                ['title' => 'Setting Up Your Development Environment', 'description' => 'Configure your IDE and tools', 'content_type' => 'practical'],
                ['title' => 'Variables and Data Types', 'description' => 'Understanding different data types', 'content_type' => 'theory'],
                ['title' => 'Control Structures and Loops', 'description' => 'Master if statements and loops', 'content_type' => 'practical'],
                ['title' => 'Functions and Methods', 'description' => 'Creating reusable code blocks', 'content_type' => 'practical'],
                ['title' => 'Object-Oriented Programming Basics', 'description' => 'Introduction to OOP concepts', 'content_type' => 'theory'],
                ['title' => 'Working with Arrays and Collections', 'description' => 'Data manipulation techniques', 'content_type' => 'practical'],
                ['title' => 'Error Handling and Debugging', 'description' => 'Finding and fixing code issues', 'content_type' => 'practical'],
                ['title' => 'Building Your First Project', 'description' => 'Apply what you\'ve learned', 'content_type' => 'project'],
                ['title' => 'Best Practices and Code Review', 'description' => 'Writing clean, maintainable code', 'content_type' => 'theory'],
            ],
            'Design' => [
                ['title' => 'Design Principles and Theory', 'description' => 'Fundamental design concepts', 'content_type' => 'theory'],
                ['title' => 'Color Theory and Psychology', 'description' => 'Understanding color in design', 'content_type' => 'theory'],
                ['title' => 'Typography Fundamentals', 'description' => 'Choosing and using fonts effectively', 'content_type' => 'theory'],
                ['title' => 'Layout and Composition', 'description' => 'Creating balanced designs', 'content_type' => 'practical'],
                ['title' => 'User Experience (UX) Basics', 'description' => 'Designing for users', 'content_type' => 'theory'],
                ['title' => 'Wireframing and Prototyping', 'description' => 'Planning your designs', 'content_type' => 'practical'],
                ['title' => 'Design Tools Mastery', 'description' => 'Using professional design software', 'content_type' => 'practical'],
                ['title' => 'Creating a Design System', 'description' => 'Consistent design patterns', 'content_type' => 'practical'],
                ['title' => 'Portfolio Development', 'description' => 'Showcasing your work', 'content_type' => 'project'],
                ['title' => 'Client Communication and Feedback', 'description' => 'Working with clients effectively', 'content_type' => 'theory'],
            ],
            'Business' => [
                ['title' => 'Business Fundamentals Overview', 'description' => 'Core business concepts', 'content_type' => 'theory'],
                ['title' => 'Market Research and Analysis', 'description' => 'Understanding your market', 'content_type' => 'practical'],
                ['title' => 'Business Model Development', 'description' => 'Creating sustainable business models', 'content_type' => 'theory'],
                ['title' => 'Financial Planning and Budgeting', 'description' => 'Managing business finances', 'content_type' => 'practical'],
                ['title' => 'Marketing Strategy Basics', 'description' => 'Reaching your target audience', 'content_type' => 'theory'],
                ['title' => 'Sales Techniques and Customer Relations', 'description' => 'Building customer relationships', 'content_type' => 'practical'],
                ['title' => 'Operations and Process Management', 'description' => 'Streamlining business operations', 'content_type' => 'practical'],
                ['title' => 'Leadership and Team Management', 'description' => 'Leading effective teams', 'content_type' => 'theory'],
                ['title' => 'Business Plan Creation', 'description' => 'Developing a comprehensive business plan', 'content_type' => 'project'],
                ['title' => 'Scaling and Growth Strategies', 'description' => 'Growing your business', 'content_type' => 'theory'],
            ],
            'Marketing' => [
                ['title' => 'Marketing Fundamentals', 'description' => 'Core marketing principles', 'content_type' => 'theory'],
                ['title' => 'Target Audience Identification', 'description' => 'Finding your ideal customers', 'content_type' => 'practical'],
                ['title' => 'Brand Development and Positioning', 'description' => 'Building a strong brand', 'content_type' => 'theory'],
                ['title' => 'Digital Marketing Channels', 'description' => 'Online marketing strategies', 'content_type' => 'practical'],
                ['title' => 'Content Marketing Strategy', 'description' => 'Creating valuable content', 'content_type' => 'practical'],
                ['title' => 'Social Media Marketing', 'description' => 'Leveraging social platforms', 'content_type' => 'practical'],
                ['title' => 'Email Marketing Campaigns', 'description' => 'Effective email strategies', 'content_type' => 'practical'],
                ['title' => 'Analytics and Performance Measurement', 'description' => 'Measuring marketing success', 'content_type' => 'practical'],
                ['title' => 'Marketing Campaign Development', 'description' => 'Creating integrated campaigns', 'content_type' => 'project'],
                ['title' => 'Advanced Marketing Techniques', 'description' => 'Cutting-edge marketing strategies', 'content_type' => 'theory'],
            ],
        ];

        return $templates[$category] ?? $templates['Programming'];
    }

    private function generateLessonContent($contentType, $category)
    {
        $content = [
            'introduction' => $this->generateIntroduction($category),
            'main_content' => $this->generateMainContent($contentType, $category),
            'key_points' => $this->generateKeyPoints($contentType),
            'exercises' => $this->generateExercises($contentType),
            'resources' => $this->generateResources($category),
            'quiz' => $this->generateQuiz($contentType)
        ];

        return $content;
    }

    private function generateIntroduction($category)
    {
        $intros = [
            'Programming' => 'In this lesson, we\'ll explore fundamental programming concepts that will serve as the foundation for your development journey.',
            'Design' => 'This lesson covers essential design principles that will help you create visually appealing and functional designs.',
            'Business' => 'We\'ll examine key business concepts that are crucial for understanding how successful organizations operate.',
            'Marketing' => 'This lesson focuses on marketing strategies and techniques that drive business growth and customer engagement.'
        ];

        return $intros[$category] ?? 'Welcome to this comprehensive lesson that will enhance your understanding of the subject matter.';
    }

    private function generateMainContent($contentType, $category)
    {
        switch ($contentType) {
            case 'theory':
                return $this->generateTheoryContent($category);
            case 'practical':
                return $this->generatePracticalContent($category);
            case 'project':
                return $this->generateProjectContent($category);
            default:
                return 'Comprehensive content covering the lesson objectives with detailed explanations and examples.';
        }
    }

    private function generateTheoryContent($category)
    {
        return "This theoretical lesson provides in-depth coverage of core concepts in {$category}. " .
               "We'll explore the fundamental principles, examine real-world applications, and discuss best practices. " .
               "By the end of this lesson, you'll have a solid understanding of the theoretical framework that guides practical implementation.";
    }

    private function generatePracticalContent($category)
    {
        return "This hands-on lesson focuses on practical application of {$category} concepts. " .
               "You'll work through step-by-step examples, complete exercises, and build real-world solutions. " .
               "The practical approach ensures you can immediately apply what you learn in your own projects.";
    }

    private function generateProjectContent($category)
    {
        return "In this project-based lesson, you'll apply your {$category} knowledge to create a comprehensive solution. " .
               "This capstone project integrates multiple concepts from previous lessons and challenges you to think critically about implementation. " .
               "You'll receive guidance throughout the development process and learn industry best practices.";
    }

    private function generateKeyPoints($contentType)
    {
        $points = [
            'theory' => [
                'Understanding fundamental concepts and principles',
                'Exploring theoretical frameworks and models',
                'Analyzing real-world applications and case studies',
                'Identifying best practices and industry standards'
            ],
            'practical' => [
                'Hands-on implementation of concepts',
                'Step-by-step guided exercises',
                'Problem-solving techniques and strategies',
                'Tool usage and practical applications'
            ],
            'project' => [
                'Comprehensive project planning and execution',
                'Integration of multiple learning objectives',
                'Real-world problem solving',
                'Portfolio development and presentation'
            ]
        ];

        return $points[$contentType] ?? $points['theory'];
    }

    private function generateExercises($contentType)
    {
        $exercises = [
            'theory' => [
                'Review key concepts and definitions',
                'Analyze provided case studies',
                'Complete knowledge check questions',
                'Participate in discussion forums'
            ],
            'practical' => [
                'Complete hands-on coding exercises',
                'Build mini-projects using lesson concepts',
                'Debug and troubleshoot provided examples',
                'Customize solutions for different scenarios'
            ],
            'project' => [
                'Plan and scope your project',
                'Implement core functionality',
                'Test and refine your solution',
                'Present your completed project'
            ]
        ];

        return $exercises[$contentType] ?? $exercises['theory'];
    }

    private function generateResources($category)
    {
        return [
            'Documentation and reference materials',
            'Additional reading and research sources',
            'Community forums and support groups',
            'Tools and software recommendations',
            'Industry blogs and expert insights'
        ];
    }

    private function generateQuiz($contentType)
    {
        return [
            'questions' => fake()->numberBetween(5, 10),
            'passing_score' => fake()->numberBetween(70, 85),
            'attempts_allowed' => fake()->numberBetween(2, 5),
            'time_limit' => fake()->numberBetween(10, 30) // minutes
        ];
    }

    private function generateSlug($courseSlug, $order, $title)
    {
        $titleSlug = strtolower(str_replace([' ', '{order}'], ['-', $order], $title));
        $titleSlug = preg_replace('/[^a-z0-9-]/', '', $titleSlug);
        return $courseSlug . '-lesson-' . $order . '-' . $titleSlug;
    }
}