<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Api\Controller;
use App\Models\Course;
use App\Models\CourseEnrollment;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class CourseController extends Controller
{
    /**
     * Display a listing of courses with filtering options.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $query = Course::with(['instructor', 'category', 'lessons' => function ($query) {
                $query->select('id', 'course_id', 'title', 'duration', 'is_preview', 'order')
                    ->orderBy('order');
            }])
            ->active()
            ->when($request->filled('search'), function ($query) use ($request) {
                return $query->where('title', 'like', '%' . $request->search . '%')
                    ->orWhere('description', 'like', '%' . $request->search . '%');
            })
            ->when($request->filled('category'), function ($query) use ($request) {
                return $query->where('category_id', $request->category);
            })
            ->when($request->filled('level'), function ($query) use ($request) {
                return $query->where('level', $request->level);
            })
            ->when($request->filled('price_type'), function ($query) use ($request) {
                if ($request->price_type === 'free') {
                    return $query->where('price', 0);
                } elseif ($request->price_type === 'paid') {
                    return $query->where('price', '>', 0);
                }
                return $query;
            })
            ->when($request->filled('price_min'), function ($query) use ($request) {
                return $query->where('price', '>=', $request->price_min);
            })
            ->when($request->filled('price_max'), function ($query) use ($request) {
                return $query->where('price', '<=', $request->price_max);
            })
            ->when($request->filled('featured'), function ($query) use ($request) {
                return $query->where('is_featured', filter_var($request->featured, FILTER_VALIDATE_BOOLEAN));
            });

        // Handle sorting
        $sortField = $request->input('sort_by', 'created_at');
        $sortDirection = $request->input('sort_direction', 'desc');
        $allowedSortFields = ['title', 'created_at', 'price', 'rating', 'enrollments_count'];
        
        if (in_array($sortField, $allowedSortFields)) {
            if ($sortField === 'rating') {
                $query->orderBy('average_rating', $sortDirection === 'asc' ? 'asc' : 'desc');
            } else {
                $query->orderBy($sortField, $sortDirection === 'asc' ? 'asc' : 'desc');
            }
        }

        $perPage = $request->input('per_page', 15);
        $courses = $query->paginate($perPage);

        // Add user-specific data if authenticated
        if (Auth::check()) {
            $user = Auth::user();
            $enrolledCourseIds = CourseEnrollment::where('user_id', $user->id)
                ->whereIn('course_id', $courses->pluck('id'))
                ->pluck('course_id')
                ->toArray();

            $courses->getCollection()->transform(function ($course) use ($enrolledCourseIds) {
                $courseArray = $course->toArray();
                $courseArray['is_enrolled'] = in_array($course->id, $enrolledCourseIds);
                return $courseArray;
            });
        }

        return $this->success([
            'courses' => $courses->items(),
            'pagination' => [
                'total' => $courses->total(),
                'per_page' => $courses->perPage(),
                'current_page' => $courses->currentPage(),
                'last_page' => $courses->lastPage(),
            ],
        ]);
    }

    /**
     * Display the specified course with its lessons.
     *
     * @param  \App\Models\Course  $course
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(Course $course): JsonResponse
    {
        // Check if course is active
        if ($course->status !== 'active') {
            return $this->error('Course not found', 404);
        }

        // Increment view count
        $course->increment('views_count');

        // Load relationships
        $course->load(['instructor', 'category', 'lessons' => function ($query) {
            $query->orderBy('order');
        }]);

        $courseData = $course->toArray();

        // Add user-specific data if authenticated
        if (Auth::check()) {
            $user = Auth::user();
            $enrollment = CourseEnrollment::where('user_id', $user->id)
                ->where('course_id', $course->id)
                ->first();

            $courseData['is_enrolled'] = $enrollment !== null;
            $courseData['enrollment_status'] = $enrollment ? $enrollment->status : null;
            $courseData['progress_percentage'] = $enrollment ? $enrollment->progress_percentage : 0;
            $courseData['completed_lessons'] = $enrollment ? $enrollment->completed_lessons : [];
        }

        return $this->success([
            'course' => $courseData
        ]);
    }

    /**
     * Get courses by category.
     *
     * @param  int  $categoryId
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function category(int $categoryId, Request $request): JsonResponse
    {
        $limit = $request->input('limit', 12);
        $sortBy = $request->input('sort_by', 'created_at');
        $sortDirection = $request->input('sort_direction', 'desc');

        $query = Course::with(['instructor', 'category'])
            ->active()
            ->where('category_id', $categoryId);

        // Apply sorting
        $allowedSortFields = ['title', 'created_at', 'price', 'rating', 'enrollments_count'];
        if (in_array($sortBy, $allowedSortFields)) {
            if ($sortBy === 'rating') {
                $query->orderBy('average_rating', $sortDirection === 'asc' ? 'asc' : 'desc');
            } else {
                $query->orderBy($sortBy, $sortDirection === 'asc' ? 'asc' : 'desc');
            }
        }

        $courses = $query->limit($limit)->get();

        // Add user-specific data if authenticated
        if (Auth::check()) {
            $user = Auth::user();
            $enrolledCourseIds = CourseEnrollment::where('user_id', $user->id)
                ->whereIn('course_id', $courses->pluck('id'))
                ->pluck('course_id')
                ->toArray();

            $courses->transform(function ($course) use ($enrolledCourseIds) {
                $courseArray = $course->toArray();
                $courseArray['is_enrolled'] = in_array($course->id, $enrolledCourseIds);
                return $courseArray;
            });
        }

        return $this->success([
            'courses' => $courses
        ]);
    }

    /**
     * Get featured courses.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function featured(Request $request): JsonResponse
    {
        $limit = $request->input('limit', 6);

        $courses = Course::with(['instructor', 'category'])
            ->active()
            ->featured()
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();

        // Add user-specific data if authenticated
        if (Auth::check()) {
            $user = Auth::user();
            $enrolledCourseIds = CourseEnrollment::where('user_id', $user->id)
                ->whereIn('course_id', $courses->pluck('id'))
                ->pluck('course_id')
                ->toArray();

            $courses->transform(function ($course) use ($enrolledCourseIds) {
                $courseArray = $course->toArray();
                $courseArray['is_enrolled'] = in_array($course->id, $enrolledCourseIds);
                return $courseArray;
            });
        }

        return $this->success([
            'courses' => $courses
        ]);
    }

    /**
     * Get course statistics and filter data.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function stats(): JsonResponse
    {
        $stats = [
            'total_courses' => Course::active()->count(),
            'total_instructors' => Course::active()->distinct('instructor_id')->count(),
            'categories' => Course::active()
                ->join('course_categories', 'courses.category_id', '=', 'course_categories.id')
                ->select('course_categories.name', DB::raw('count(*) as count'))
                ->groupBy('course_categories.id', 'course_categories.name')
                ->orderBy('count', 'desc')
                ->pluck('count', 'name'),
            'levels' => Course::active()
                ->select('level', DB::raw('count(*) as count'))
                ->groupBy('level')
                ->pluck('count', 'level'),
            'price_ranges' => [
                'free' => Course::active()->where('price', 0)->count(),
                'under_50' => Course::active()->where('price', '>', 0)->where('price', '<=', 50)->count(),
                '50_100' => Course::active()->where('price', '>', 50)->where('price', '<=', 100)->count(),
                'over_100' => Course::active()->where('price', '>', 100)->count(),
            ],
        ];

        return $this->success($stats);
    }

    /**
     * Get search suggestions for courses.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function searchSuggestions(Request $request): JsonResponse
    {
        $query = $request->input('q', '');
        $type = $request->input('type', 'all'); // all, title, instructor, category
        $limit = $request->input('limit', 10);

        $suggestions = [];

        if ($type === 'all' || $type === 'title') {
            $titles = Course::active()
                ->where('title', 'like', '%' . $query . '%')
                ->distinct()
                ->limit($limit)
                ->pluck('title')
                ->map(function ($title) {
                    return ['type' => 'title', 'value' => $title];
                });
            $suggestions = array_merge($suggestions, $titles->toArray());
        }

        if ($type === 'all' || $type === 'instructor') {
            $instructors = Course::active()
                ->join('users', 'courses.instructor_id', '=', 'users.id')
                ->where('users.name', 'like', '%' . $query . '%')
                ->distinct()
                ->limit($limit)
                ->pluck('users.name')
                ->map(function ($instructor) {
                    return ['type' => 'instructor', 'value' => $instructor];
                });
            $suggestions = array_merge($suggestions, $instructors->toArray());
        }

        if ($type === 'all' || $type === 'category') {
            $categories = Course::active()
                ->join('course_categories', 'courses.category_id', '=', 'course_categories.id')
                ->where('course_categories.name', 'like', '%' . $query . '%')
                ->distinct()
                ->limit($limit)
                ->pluck('course_categories.name')
                ->map(function ($category) {
                    return ['type' => 'category', 'value' => $category];
                });
            $suggestions = array_merge($suggestions, $categories->toArray());
        }

        // Limit total suggestions
        $suggestions = array_slice($suggestions, 0, $limit);

        return $this->success([
            'suggestions' => $suggestions
        ]);
    }

    /**
     * Get similar courses based on the given course.
     *
     * @param  \App\Models\Course  $course
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function similar(Course $course, Request $request): JsonResponse
    {
        $limit = $request->input('limit', 5);

        $similarCourses = Course::with(['instructor', 'category'])
            ->active()
            ->where('id', '!=', $course->id)
            ->where(function ($query) use ($course) {
                $query->where('category_id', $course->category_id)
                    ->orWhere('level', $course->level)
                    ->orWhere('instructor_id', $course->instructor_id);
            })
            ->orderByRaw('CASE 
                WHEN category_id = ? THEN 3
                WHEN instructor_id = ? THEN 2
                WHEN level = ? THEN 1
                ELSE 0
            END DESC', [$course->category_id, $course->instructor_id, $course->level])
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();

        // Add user-specific data if authenticated
        if (Auth::check()) {
            $user = Auth::user();
            $enrolledCourseIds = CourseEnrollment::where('user_id', $user->id)
                ->whereIn('course_id', $similarCourses->pluck('id'))
                ->pluck('course_id')
                ->toArray();

            $similarCourses->transform(function ($course) use ($enrolledCourseIds) {
                $courseArray = $course->toArray();
                $courseArray['is_enrolled'] = in_array($course->id, $enrolledCourseIds);
                return $courseArray;
            });
        }

        return $this->success([
            'courses' => $similarCourses
        ]);
    }
}