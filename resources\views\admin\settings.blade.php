@extends('layouts.admin')

@section('title', 'System Settings')

@section('content')
<div class="min-h-screen bg-gray-50 py-6" x-data="{ activeTab: 'general' }">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900">System Settings</h1>
            <p class="mt-2 text-gray-600">Manage your application's global settings and configurations.</p>
        </div>

        <!-- Success Message -->
        @if(session('success'))
            <div class="mb-6 bg-green-50 border border-green-200 rounded-md p-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-green-800">{{ session('success') }}</p>
                    </div>
                </div>
            </div>
        @endif

        <div class="bg-white shadow rounded-lg overflow-hidden">
            <!-- Tab Navigation -->
            <div class="border-b border-gray-200">
                <nav class="-mb-px flex space-x-8 px-6" aria-label="Tabs">
                    <button @click="activeTab = 'general'" 
                            :class="activeTab === 'general' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                            class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200">
                        <svg class="w-5 h-5 inline-block mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        General
                    </button>
                    <button @click="activeTab = 'email'" 
                            :class="activeTab === 'email' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                            class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200">
                        <svg class="w-5 h-5 inline-block mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                        </svg>
                        Email
                    </button>
                    <button @click="activeTab = 'payment'" 
                            :class="activeTab === 'payment' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                            class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200">
                        <svg class="w-5 h-5 inline-block mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                        Payment
                    </button>
                    <button @click="activeTab = 'seo'" 
                            :class="activeTab === 'seo' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                            class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200">
                        <svg class="w-5 h-5 inline-block mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        SEO
                    </button>
                </nav>
            </div>

            <form method="POST" action="{{ route('admin.settings.update') }}" enctype="multipart/form-data">
                @csrf
                @method('PUT')

                <!-- General Settings Tab -->
                <div x-show="activeTab === 'general'" x-transition class="p-6">
                    <div class="border-b border-gray-200 pb-4 mb-6">
                        <h3 class="text-lg font-medium text-gray-900">General Settings</h3>
                        <p class="mt-1 text-sm text-gray-600">Basic application settings and configurations.</p>
                    </div>

                    <div class="space-y-6">
                        <!-- Site Information -->
                        <div class="bg-gray-50 rounded-lg p-4">
                            <h4 class="text-base font-medium text-gray-900 mb-4">Site Information</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label for="site_name" class="block text-sm font-medium text-gray-700 mb-2">Site Name</label>
                                    <input type="text" name="site_name" id="site_name" 
                                           value="{{ old('site_name', $settings['site_name'] ?? 'Rectra') }}"
                                           class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                </div>
                                <div>
                                    <label for="site_tagline" class="block text-sm font-medium text-gray-700 mb-2">Site Tagline</label>
                                    <input type="text" name="site_tagline" id="site_tagline" 
                                           value="{{ old('site_tagline', $settings['site_tagline'] ?? 'Find Your Dream Job') }}"
                                           class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                </div>
                                <div class="md:col-span-2">
                                    <label for="site_description" class="block text-sm font-medium text-gray-700 mb-2">Site Description</label>
                                    <textarea name="site_description" id="site_description" rows="3"
                                              class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">{{ old('site_description', $settings['site_description'] ?? '') }}</textarea>
                                </div>
                            </div>
                        </div>

                        <!-- Contact Information -->
                        <div class="bg-gray-50 rounded-lg p-4">
                            <h4 class="text-base font-medium text-gray-900 mb-4">Contact Information</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label for="contact_email" class="block text-sm font-medium text-gray-700 mb-2">Contact Email</label>
                                    <input type="email" name="contact_email" id="contact_email" 
                                           value="{{ old('contact_email', $settings['contact_email'] ?? '') }}"
                                           class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                </div>
                                <div>
                                    <label for="contact_phone" class="block text-sm font-medium text-gray-700 mb-2">Contact Phone</label>
                                    <input type="text" name="contact_phone" id="contact_phone" 
                                           value="{{ old('contact_phone', $settings['contact_phone'] ?? '') }}"
                                           class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                </div>
                                <div class="md:col-span-2">
                                    <label for="contact_address" class="block text-sm font-medium text-gray-700 mb-2">Contact Address</label>
                                    <textarea name="contact_address" id="contact_address" rows="2"
                                              class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">{{ old('contact_address', $settings['contact_address'] ?? '') }}</textarea>
                                </div>
                            </div>
                        </div>

                        <!-- Application Settings -->
                        <div class="bg-gray-50 rounded-lg p-4">
                            <h4 class="text-base font-medium text-gray-900 mb-4">Application Settings</h4>
                            <div class="space-y-4">
                                <div class="flex items-start">
                                    <div class="flex items-center h-5">
                                        <input id="maintenance_mode" name="maintenance_mode" type="checkbox" value="1" 
                                               {{ old('maintenance_mode', $settings['maintenance_mode'] ?? false) ? 'checked' : '' }}
                                               class="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded">
                                    </div>
                                    <div class="ml-3 text-sm">
                                        <label for="maintenance_mode" class="font-medium text-gray-700">Maintenance Mode</label>
                                        <p class="text-gray-500">Enable maintenance mode to temporarily disable the site for updates.</p>
                                    </div>
                                </div>

                                <div class="flex items-start">
                                    <div class="flex items-center h-5">
                                        <input id="user_registration" name="user_registration" type="checkbox" value="1" 
                                               {{ old('user_registration', $settings['user_registration'] ?? true) ? 'checked' : '' }}
                                               class="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded">
                                    </div>
                                    <div class="ml-3 text-sm">
                                        <label for="user_registration" class="font-medium text-gray-700">Allow User Registration</label>
                                        <p class="text-gray-500">Allow new users to register on the platform.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Email Settings Tab -->
                <div x-show="activeTab === 'email'" x-transition class="p-6">
                    <div class="border-b border-gray-200 pb-4 mb-6">
                        <h3 class="text-lg font-medium text-gray-900">Email Settings</h3>
                        <p class="mt-1 text-sm text-gray-600">Configure email delivery and SMTP settings.</p>
                    </div>

                    <div class="space-y-6">
                        <!-- SMTP Configuration -->
                        <div class="bg-gray-50 rounded-lg p-4">
                            <h4 class="text-base font-medium text-gray-900 mb-4">SMTP Configuration</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label for="smtp_host" class="block text-sm font-medium text-gray-700 mb-2">SMTP Host</label>
                                    <input type="text" name="smtp_host" id="smtp_host" 
                                           value="{{ old('smtp_host', $settings['smtp_host'] ?? '') }}"
                                           class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                </div>
                                <div>
                                    <label for="smtp_port" class="block text-sm font-medium text-gray-700 mb-2">SMTP Port</label>
                                    <input type="number" name="smtp_port" id="smtp_port" 
                                           value="{{ old('smtp_port', $settings['smtp_port'] ?? '587') }}"
                                           class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                </div>
                                <div>
                                    <label for="smtp_username" class="block text-sm font-medium text-gray-700 mb-2">SMTP Username</label>
                                    <input type="text" name="smtp_username" id="smtp_username" 
                                           value="{{ old('smtp_username', $settings['smtp_username'] ?? '') }}"
                                           class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                </div>
                                <div>
                                    <label for="smtp_password" class="block text-sm font-medium text-gray-700 mb-2">SMTP Password</label>
                                    <input type="password" name="smtp_password" id="smtp_password" 
                                           value="{{ old('smtp_password', $settings['smtp_password'] ?? '') }}"
                                           class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                </div>
                            </div>
                        </div>

                        <!-- Email Templates -->
                        <div class="bg-gray-50 rounded-lg p-4">
                            <h4 class="text-base font-medium text-gray-900 mb-4">Default Email Settings</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label for="from_email" class="block text-sm font-medium text-gray-700 mb-2">From Email</label>
                                    <input type="email" name="from_email" id="from_email" 
                                           value="{{ old('from_email', $settings['from_email'] ?? '') }}"
                                           class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                </div>
                                <div>
                                    <label for="from_name" class="block text-sm font-medium text-gray-700 mb-2">From Name</label>
                                    <input type="text" name="from_name" id="from_name" 
                                           value="{{ old('from_name', $settings['from_name'] ?? '') }}"
                                           class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Payment Settings Tab -->
                <div x-show="activeTab === 'payment'" x-transition class="p-6">
                    <div class="border-b border-gray-200 pb-4 mb-6">
                        <h3 class="text-lg font-medium text-gray-900">Payment Settings</h3>
                        <p class="mt-1 text-sm text-gray-600">Configure payment gateways and subscription settings.</p>
                    </div>

                    <div class="space-y-6">
                        <!-- Stripe Configuration -->
                        <div class="bg-gray-50 rounded-lg p-4">
                            <h4 class="text-base font-medium text-gray-900 mb-4">Stripe Configuration</h4>
                            <div class="space-y-4">
                                <div>
                                    <label for="stripe_public_key" class="block text-sm font-medium text-gray-700 mb-2">Stripe Public Key</label>
                                    <input type="text" name="stripe_public_key" id="stripe_public_key" 
                                           value="{{ old('stripe_public_key', $settings['stripe_public_key'] ?? '') }}"
                                           class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                </div>
                                <div>
                                    <label for="stripe_secret_key" class="block text-sm font-medium text-gray-700 mb-2">Stripe Secret Key</label>
                                    <input type="password" name="stripe_secret_key" id="stripe_secret_key" 
                                           value="{{ old('stripe_secret_key', $settings['stripe_secret_key'] ?? '') }}"
                                           class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                </div>
                            </div>
                        </div>

                        <!-- Currency Settings -->
                        <div class="bg-gray-50 rounded-lg p-4">
                            <h4 class="text-base font-medium text-gray-900 mb-4">Currency Settings</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label for="default_currency" class="block text-sm font-medium text-gray-700 mb-2">Default Currency</label>
                                    <select name="default_currency" id="default_currency" 
                                            class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                        <option value="USD" {{ old('default_currency', $settings['default_currency'] ?? 'USD') === 'USD' ? 'selected' : '' }}>USD - US Dollar</option>
                                        <option value="EUR" {{ old('default_currency', $settings['default_currency'] ?? 'USD') === 'EUR' ? 'selected' : '' }}>EUR - Euro</option>
                                        <option value="GBP" {{ old('default_currency', $settings['default_currency'] ?? 'USD') === 'GBP' ? 'selected' : '' }}>GBP - British Pound</option>
                                        <option value="CAD" {{ old('default_currency', $settings['default_currency'] ?? 'USD') === 'CAD' ? 'selected' : '' }}>CAD - Canadian Dollar</option>
                                    </select>
                                </div>
                                <div>
                                    <label for="currency_symbol" class="block text-sm font-medium text-gray-700 mb-2">Currency Symbol</label>
                                    <input type="text" name="currency_symbol" id="currency_symbol" 
                                           value="{{ old('currency_symbol', $settings['currency_symbol'] ?? '$') }}"
                                           class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- SEO Settings Tab -->
                <div x-show="activeTab === 'seo'" x-transition class="p-6">
                    <div class="border-b border-gray-200 pb-4 mb-6">
                        <h3 class="text-lg font-medium text-gray-900">SEO Settings</h3>
                        <p class="mt-1 text-sm text-gray-600">Configure search engine optimization settings.</p>
                    </div>

                    <div class="space-y-6">
                        <!-- Meta Tags -->
                        <div class="bg-gray-50 rounded-lg p-4">
                            <h4 class="text-base font-medium text-gray-900 mb-4">Meta Tags</h4>
                            <div class="space-y-4">
                                <div>
                                    <label for="meta_title" class="block text-sm font-medium text-gray-700 mb-2">Meta Title</label>
                                    <input type="text" name="meta_title" id="meta_title" 
                                           value="{{ old('meta_title', $settings['meta_title'] ?? '') }}"
                                           class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                </div>
                                <div>
                                    <label for="meta_description" class="block text-sm font-medium text-gray-700 mb-2">Meta Description</label>
                                    <textarea name="meta_description" id="meta_description" rows="3"
                                              class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">{{ old('meta_description', $settings['meta_description'] ?? '') }}</textarea>
                                </div>
                                <div>
                                    <label for="meta_keywords" class="block text-sm font-medium text-gray-700 mb-2">Meta Keywords</label>
                                    <input type="text" name="meta_keywords" id="meta_keywords" 
                                           value="{{ old('meta_keywords', $settings['meta_keywords'] ?? '') }}"
                                           placeholder="job portal, careers, employment, jobs"
                                           class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                </div>
                            </div>
                        </div>

                        <!-- Analytics -->
                        <div class="bg-gray-50 rounded-lg p-4">
                            <h4 class="text-base font-medium text-gray-900 mb-4">Analytics & Tracking</h4>
                            <div class="space-y-4">
                                <div>
                                    <label for="google_analytics_id" class="block text-sm font-medium text-gray-700 mb-2">Google Analytics ID</label>
                                    <input type="text" name="google_analytics_id" id="google_analytics_id" 
                                           value="{{ old('google_analytics_id', $settings['google_analytics_id'] ?? '') }}"
                                           placeholder="G-XXXXXXXXXX"
                                           class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                </div>
                                <div>
                                    <label for="google_tag_manager_id" class="block text-sm font-medium text-gray-700 mb-2">Google Tag Manager ID</label>
                                    <input type="text" name="google_tag_manager_id" id="google_tag_manager_id" 
                                           value="{{ old('google_tag_manager_id', $settings['google_tag_manager_id'] ?? '') }}"
                                           placeholder="GTM-XXXXXXX"
                                           class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Save Button -->
                <div class="px-6 py-4 bg-gray-50 border-t border-gray-200">
                    <div class="flex justify-end">
                        <button type="submit" class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                            <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Save Settings
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    // Auto-save draft functionality
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.querySelector('form');
        const inputs = form.querySelectorAll('input, textarea, select');
        
        inputs.forEach(input => {
            input.addEventListener('change', function() {
                // Save to localStorage for draft functionality
                localStorage.setItem('admin_settings_' + this.name, this.value);
            });
            
            // Load saved values
            const savedValue = localStorage.getItem('admin_settings_' + input.name);
            if (savedValue && !input.value) {
                input.value = savedValue;
            }
        });
        
        // Clear localStorage on successful save
        form.addEventListener('submit', function() {
            inputs.forEach(input => {
                localStorage.removeItem('admin_settings_' + input.name);
            });
        });
    });
</script>
@endsection