@extends('layouts.admin')

@section('title', 'Job Details')

@section('content')
<div class="max-w-6xl mx-auto">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">{{ $job->title }}</h1>
                <p class="mt-2 text-gray-600">{{ $job->employerProfile->company_name }}</p>
            </div>
            <div class="flex items-center space-x-3">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                    @if($job->status === 'active') bg-green-100 text-green-800
                    @elseif($job->status === 'paused') bg-yellow-100 text-yellow-800
                    @elseif($job->status === 'closed') bg-red-100 text-red-800
                    @else bg-gray-100 text-gray-800 @endif">
                    {{ ucfirst($job->status) }}
                </span>
                <a href="{{ route('admin.jobs.index') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Back to Jobs
                </a>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Job Details -->
            <div class="bg-white shadow-sm rounded-lg p-6">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">Job Details</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Location</label>
                        <p class="text-gray-900">{{ $job->location }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Job Type</label>
                        <p class="text-gray-900">{{ ucfirst($job->type) }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Experience Level</label>
                        <p class="text-gray-900">{{ ucfirst($job->experience_level) }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Category</label>
                        <p class="text-gray-900">{{ ucfirst($job->category) }}</p>
                    </div>
                </div>

                @if($job->salary_min || $job->salary_max)
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Salary Range</label>
                    <p class="text-gray-900">
                        ${{ number_format($job->salary_min) }} - ${{ number_format($job->salary_max) }}
                        @if($job->salary_currency) {{ $job->salary_currency }} @endif
                        @if($job->salary_period) per {{ $job->salary_period }} @endif
                    </p>
                </div>
                @endif

                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                    <div class="prose max-w-none text-gray-900">
                        {!! nl2br(e($job->description)) !!}
                    </div>
                </div>

                @if($job->requirements && count($job->requirements) > 0)
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Requirements</label>
                    <ul class="list-disc list-inside space-y-1 text-gray-900">
                        @foreach($job->requirements as $requirement)
                            <li>{{ $requirement }}</li>
                        @endforeach
                    </ul>
                </div>
                @endif

                @if($job->benefits && count($job->benefits) > 0)
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Benefits</label>
                    <ul class="list-disc list-inside space-y-1 text-gray-900">
                        @foreach($job->benefits as $benefit)
                            <li>{{ $benefit }}</li>
                        @endforeach
                    </ul>
                </div>
                @endif
            </div>

            <!-- Applications -->
            <div class="bg-white shadow-sm rounded-lg p-6">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-xl font-semibold text-gray-900">Applications ({{ $job->applications->count() }})</h2>
                    <a href="{{ route('admin.applications.job', $job) }}" class="text-purple-600 hover:text-purple-700 text-sm font-medium">
                        View All Applications →
                    </a>
                </div>

                @if($job->applications->count() > 0)
                    <div class="space-y-4">
                        @foreach($job->applications->take(5) as $application)
                            <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                                <div class="flex items-center space-x-4">
                                    <img class="h-10 w-10 rounded-full" src="{{ $application->user->avatar ?? 'https://ui-avatars.com/api/?name=' . urlencode($application->user->name) }}" alt="{{ $application->user->name }}">
                                    <div>
                                        <h4 class="text-sm font-medium text-gray-900">{{ $application->user->name }}</h4>
                                        <p class="text-sm text-gray-500">Applied {{ $application->created_at->diffForHumans() }}</p>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-3">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        @if($application->status === 'submitted') bg-blue-100 text-blue-800
                                        @elseif($application->status === 'viewed') bg-yellow-100 text-yellow-800
                                        @elseif($application->status === 'shortlisted') bg-green-100 text-green-800
                                        @elseif($application->status === 'rejected') bg-red-100 text-red-800
                                        @else bg-gray-100 text-gray-800 @endif">
                                        {{ ucfirst($application->status) }}
                                    </span>
                                    <a href="{{ route('admin.applications.show', $application) }}" class="text-purple-600 hover:text-purple-700 text-sm">
                                        View
                                    </a>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <p class="text-gray-500 text-center py-8">No applications yet</p>
                @endif
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Job Actions -->
            <div class="bg-white shadow-sm rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Actions</h3>
                <div class="space-y-3">
                    @if($job->status === 'pending')
                        <form action="{{ route('admin.jobs.approve', $job) }}" method="POST">
                            @csrf
                            <button type="submit" class="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700">
                                Approve Job
                            </button>
                        </form>
                        <form action="{{ route('admin.jobs.reject', $job) }}" method="POST">
                            @csrf
                            <button type="submit" class="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700">
                                Reject Job
                            </button>
                        </form>
                    @endif
                    
                    <form action="{{ route('admin.jobs.feature', $job) }}" method="POST">
                        @csrf
                        <button type="submit" class="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                            {{ $job->is_featured ? 'Remove Feature' : 'Feature Job' }}
                        </button>
                    </form>
                    
                    <a href="{{ route('jobs.show', $job) }}" target="_blank" class="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                        View Public Page
                    </a>
                </div>
            </div>

            <!-- Job Statistics -->
            <div class="bg-white shadow-sm rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Statistics</h3>
                <div class="space-y-4">
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-500">Total Views</span>
                        <span class="text-sm font-medium text-gray-900">{{ $job->views_count ?? 0 }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-500">Applications</span>
                        <span class="text-sm font-medium text-gray-900">{{ $job->applications->count() }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-500">Posted</span>
                        <span class="text-sm font-medium text-gray-900">{{ $job->created_at->format('M d, Y') }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-500">Expires</span>
                        <span class="text-sm font-medium text-gray-900">{{ $job->expires_at->format('M d, Y') }}</span>
                    </div>
                </div>
            </div>

            <!-- Company Info -->
            <div class="bg-white shadow-sm rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Company</h3>
                <div class="flex items-center space-x-3 mb-4">
                    @if($job->employerProfile->logo)
                        <img class="h-12 w-12 rounded-lg object-cover" src="{{ $job->employerProfile->logo }}" alt="{{ $job->employerProfile->company_name }}">
                    @else
                        <div class="h-12 w-12 rounded-lg bg-gray-200 flex items-center justify-center">
                            <span class="text-gray-500 font-medium">{{ substr($job->employerProfile->company_name, 0, 2) }}</span>
                        </div>
                    @endif
                    <div>
                        <h4 class="text-sm font-medium text-gray-900">{{ $job->employerProfile->company_name }}</h4>
                        <p class="text-sm text-gray-500">{{ $job->employerProfile->industry }}</p>
                    </div>
                </div>
                <a href="{{ route('admin.companies.show', $job->employerProfile->user) }}" class="text-purple-600 hover:text-purple-700 text-sm font-medium">
                    View Company Profile →
                </a>
            </div>
        </div>
    </div>
</div>
@endsection
