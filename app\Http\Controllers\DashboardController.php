<?php

namespace App\Http\Controllers;

use App\Models\Job;
use App\Models\JobApplication;
use App\Models\Course;
use App\Models\CourseEnrollment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class DashboardController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Show the application dashboard.
     */
    public function index()
    {
        $user = Auth::user();
        $data = [];

        if ($user->isJobSeeker()) {
            $data = $this->getJobSeekerDashboardData();
        } elseif ($user->isEmployer()) {
            $data = $this->getEmployerDashboardData();
        } elseif ($user->isAdmin()) {
            $data = $this->getAdminDashboardData();
        }

        return view('dashboard', compact('data'));
    }

    /**
     * Get dashboard data for job seekers.
     */
    private function getJobSeekerDashboardData()
    {
        $user = Auth::user();
        $jobSeekerProfile = $user->jobSeekerProfile;

        if (!$jobSeekerProfile) {
            return [
                'type' => 'job_seeker',
                'profile_incomplete' => true,
                'stats' => [
                    'applications' => 0,
                    'saved_jobs' => 0,
                    'course_enrollments' => 0,
                    'completed_courses' => 0
                ],
                'recent_applications' => collect(),
                'saved_jobs' => collect(),
                'recommended_jobs' => collect(),
                'course_progress' => collect(),
                'recent_courses' => collect()
            ];
        }

        // Get statistics
        $stats = [
            'applications' => JobApplication::where('job_seeker_profile_id', $jobSeekerProfile->id)->count(),
            'saved_jobs' => $user->savedJobs()->count(),
            'course_enrollments' => CourseEnrollment::where('user_id', $user->id)->count(),
            'completed_courses' => CourseEnrollment::where('user_id', $user->id)
                ->where('status', 'completed')->count()
        ];

        // Get recent job applications
        $recentApplications = JobApplication::with(['job.employerProfile.user'])
            ->where('job_seeker_profile_id', $jobSeekerProfile->id)
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        // Get saved jobs
        $savedJobs = $user->savedJobs()
            ->with(['employerProfile.user'])
            ->where('status', 'active')
            ->where('expires_at', '>', now())
            ->orderBy('pivot_created_at', 'desc')
            ->limit(5)
            ->get();

        // Get recommended jobs based on profile
        $recommendedJobs = Job::with(['employerProfile.user'])
            ->where('status', 'active')
            ->where('expires_at', '>', now())
            ->when($jobSeekerProfile->desired_job_title, function ($query) use ($jobSeekerProfile) {
                $query->where('title', 'like', '%' . $jobSeekerProfile->desired_job_title . '%');
            })
            ->when($jobSeekerProfile->preferred_location, function ($query) use ($jobSeekerProfile) {
                $query->where('location', 'like', '%' . $jobSeekerProfile->preferred_location . '%');
            })
            ->when($jobSeekerProfile->skills, function ($query) use ($jobSeekerProfile) {
                $skills = json_decode($jobSeekerProfile->skills, true) ?? [];
                if (!empty($skills)) {
                    $query->where(function ($q) use ($skills) {
                        foreach ($skills as $skill) {
                            $q->orWhere('skills', 'like', '%' . $skill . '%');
                        }
                    });
                }
            })
            ->limit(6)
            ->get();

        // Get course progress
        $courseProgress = CourseEnrollment::with(['course'])
            ->where('user_id', $user->id)
            ->where('status', 'active')
            ->orderBy('updated_at', 'desc')
            ->limit(5)
            ->get();

        // Get recent courses
        $recentCourses = Course::with(['media'])
            ->where('is_published', true)
            ->orderBy('created_at', 'desc')
            ->limit(6)
            ->get();

        return [
            'type' => 'job_seeker',
            'profile_incomplete' => false,
            'stats' => $stats,
            'recent_applications' => $recentApplications,
            'saved_jobs' => $savedJobs,
            'recommended_jobs' => $recommendedJobs,
            'course_progress' => $courseProgress,
            'recent_courses' => $recentCourses
        ];
    }

    /**
     * Get dashboard data for employers.
     */
    private function getEmployerDashboardData()
    {
        $user = Auth::user();
        $employerProfile = $user->employerProfile;

        if (!$employerProfile) {
            return [
                'type' => 'employer',
                'profile_incomplete' => true,
                'stats' => [
                    'active_jobs' => 0,
                    'total_applications' => 0,
                    'pending_applications' => 0,
                    'views_this_month' => 0
                ],
                'recent_jobs' => collect(),
                'recent_applications' => collect(),
                'job_performance' => collect()
            ];
        }

        // Get statistics
        $activeJobs = Job::where('employer_profile_id', $employerProfile->id)
            ->where('status', 'active')
            ->where('expires_at', '>', now())
            ->count();

        $totalApplications = JobApplication::whereHas('job', function ($query) use ($employerProfile) {
            $query->where('employer_profile_id', $employerProfile->id);
        })->count();

        $pendingApplications = JobApplication::whereHas('job', function ($query) use ($employerProfile) {
            $query->where('employer_profile_id', $employerProfile->id);
        })->where('status', 'pending')->count();

        $viewsThisMonth = Job::where('employer_profile_id', $employerProfile->id)
            ->where('created_at', '>=', now()->startOfMonth())
            ->sum('views_count');

        $stats = [
            'active_jobs' => $activeJobs,
            'total_applications' => $totalApplications,
            'pending_applications' => $pendingApplications,
            'views_this_month' => $viewsThisMonth
        ];

        // Get recent jobs
        $recentJobs = Job::where('employer_profile_id', $employerProfile->id)
            ->withCount('applications')
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        // Get recent applications
        $recentApplications = JobApplication::with(['job', 'jobSeekerProfile.user'])
            ->whereHas('job', function ($query) use ($employerProfile) {
                $query->where('employer_profile_id', $employerProfile->id);
            })
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        // Get job performance data
        $jobPerformance = Job::where('employer_profile_id', $employerProfile->id)
            ->select('title', 'views_count', 'created_at')
            ->withCount('applications')
            ->orderBy('views_count', 'desc')
            ->limit(5)
            ->get();

        return [
            'type' => 'employer',
            'profile_incomplete' => false,
            'stats' => $stats,
            'recent_jobs' => $recentJobs,
            'recent_applications' => $recentApplications,
            'job_performance' => $jobPerformance
        ];
    }

    /**
     * Get dashboard data for admins.
     */
    private function getAdminDashboardData()
    {
        // Get overall statistics
        $stats = [
            'total_users' => DB::table('users')->count(),
            'total_jobs' => Job::count(),
            'total_applications' => JobApplication::count(),
            'total_courses' => Course::count(),
            'active_jobs' => Job::where('status', 'active')
                ->where('expires_at', '>', now())
                ->count(),
            'pending_jobs' => Job::where('status', 'pending')->count(),
            'course_enrollments' => CourseEnrollment::count(),
            'new_users_this_month' => DB::table('users')
                ->where('created_at', '>=', now()->startOfMonth())
                ->count()
        ];

        // Get recent activities
        $recentUsers = DB::table('users')
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        $recentJobs = Job::with(['employerProfile.user'])
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        $recentApplications = JobApplication::with(['job', 'jobSeekerProfile.user'])
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        // Get monthly statistics for charts
        $monthlyStats = [];
        for ($i = 11; $i >= 0; $i--) {
            $month = now()->subMonths($i);
            $monthlyStats[] = [
                'month' => $month->format('M Y'),
                'users' => DB::table('users')
                    ->whereYear('created_at', $month->year)
                    ->whereMonth('created_at', $month->month)
                    ->count(),
                'jobs' => Job::whereYear('created_at', $month->year)
                    ->whereMonth('created_at', $month->month)
                    ->count(),
                'applications' => JobApplication::whereYear('created_at', $month->year)
                    ->whereMonth('created_at', $month->month)
                    ->count()
            ];
        }

        return [
            'type' => 'admin',
            'stats' => $stats,
            'recent_users' => $recentUsers,
            'recent_jobs' => $recentJobs,
            'recent_applications' => $recentApplications,
            'monthly_stats' => $monthlyStats
        ];
    }
}