@extends('layouts.admin')

@section('title', 'System Settings')

@section('content')
<div class="max-w-4xl mx-auto">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">System Settings</h1>
        <p class="mt-2 text-gray-600">Manage platform configuration and settings</p>
    </div>

    <!-- Settings Tabs -->
    <div class="bg-white shadow-sm rounded-lg">
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8 px-6" aria-label="Tabs">
                <button class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm tab-button active" data-tab="general">
                    General
                </button>
                <button class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm tab-button" data-tab="email">
                    Email
                </button>
                <button class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm tab-button" data-tab="payments">
                    Payments
                </button>
                <button class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm tab-button" data-tab="security">
                    Security
                </button>
            </nav>
        </div>

        <form action="{{ route('admin.settings.update') }}" method="POST" class="p-6">
            @csrf
            @method('PUT')

            <!-- General Settings -->
            <div id="general-tab" class="tab-content">
                <h3 class="text-lg font-medium text-gray-900 mb-6">General Settings</h3>
                
                <div class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="site_name" class="block text-sm font-medium text-gray-700 mb-2">Site Name</label>
                            <input type="text" id="site_name" name="site_name" value="{{ old('site_name', config('app.name')) }}" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500">
                        </div>

                        <div>
                            <label for="site_url" class="block text-sm font-medium text-gray-700 mb-2">Site URL</label>
                            <input type="url" id="site_url" name="site_url" value="{{ old('site_url', config('app.url')) }}" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500">
                        </div>
                    </div>

                    <div>
                        <label for="site_description" class="block text-sm font-medium text-gray-700 mb-2">Site Description</label>
                        <textarea id="site_description" name="site_description" rows="3" 
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500" 
                                  placeholder="Brief description of your platform">{{ old('site_description') }}</textarea>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="contact_email" class="block text-sm font-medium text-gray-700 mb-2">Contact Email</label>
                            <input type="email" id="contact_email" name="contact_email" value="{{ old('contact_email') }}" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500">
                        </div>

                        <div>
                            <label for="support_email" class="block text-sm font-medium text-gray-700 mb-2">Support Email</label>
                            <input type="email" id="support_email" name="support_email" value="{{ old('support_email') }}" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500">
                        </div>
                    </div>

                    <div class="flex items-center">
                        <input type="checkbox" id="maintenance_mode" name="maintenance_mode" value="1" 
                               class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                        <label for="maintenance_mode" class="ml-2 block text-sm text-gray-700">
                            Enable maintenance mode
                        </label>
                    </div>
                </div>
            </div>

            <!-- Email Settings -->
            <div id="email-tab" class="tab-content hidden">
                <h3 class="text-lg font-medium text-gray-900 mb-6">Email Settings</h3>
                
                <div class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="mail_driver" class="block text-sm font-medium text-gray-700 mb-2">Mail Driver</label>
                            <select id="mail_driver" name="mail_driver" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500">
                                <option value="smtp">SMTP</option>
                                <option value="mailgun">Mailgun</option>
                                <option value="ses">Amazon SES</option>
                                <option value="sendmail">Sendmail</option>
                            </select>
                        </div>

                        <div>
                            <label for="mail_from_address" class="block text-sm font-medium text-gray-700 mb-2">From Address</label>
                            <input type="email" id="mail_from_address" name="mail_from_address" value="{{ old('mail_from_address') }}" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500">
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="mail_host" class="block text-sm font-medium text-gray-700 mb-2">SMTP Host</label>
                            <input type="text" id="mail_host" name="mail_host" value="{{ old('mail_host') }}" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500">
                        </div>

                        <div>
                            <label for="mail_port" class="block text-sm font-medium text-gray-700 mb-2">SMTP Port</label>
                            <input type="number" id="mail_port" name="mail_port" value="{{ old('mail_port', '587') }}" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500">
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="mail_username" class="block text-sm font-medium text-gray-700 mb-2">SMTP Username</label>
                            <input type="text" id="mail_username" name="mail_username" value="{{ old('mail_username') }}" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500">
                        </div>

                        <div>
                            <label for="mail_password" class="block text-sm font-medium text-gray-700 mb-2">SMTP Password</label>
                            <input type="password" id="mail_password" name="mail_password" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500">
                        </div>
                    </div>

                    <div class="flex items-center">
                        <input type="checkbox" id="mail_encryption" name="mail_encryption" value="tls" 
                               class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                        <label for="mail_encryption" class="ml-2 block text-sm text-gray-700">
                            Use TLS encryption
                        </label>
                    </div>
                </div>
            </div>

            <!-- Payment Settings -->
            <div id="payments-tab" class="tab-content hidden">
                <h3 class="text-lg font-medium text-gray-900 mb-6">Payment Settings</h3>
                
                <div class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="stripe_public_key" class="block text-sm font-medium text-gray-700 mb-2">Stripe Public Key</label>
                            <input type="text" id="stripe_public_key" name="stripe_public_key" value="{{ old('stripe_public_key') }}" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500">
                        </div>

                        <div>
                            <label for="stripe_secret_key" class="block text-sm font-medium text-gray-700 mb-2">Stripe Secret Key</label>
                            <input type="password" id="stripe_secret_key" name="stripe_secret_key" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500">
                        </div>
                    </div>

                    <div>
                        <label for="currency" class="block text-sm font-medium text-gray-700 mb-2">Default Currency</label>
                        <select id="currency" name="currency" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500">
                            <option value="USD">USD - US Dollar</option>
                            <option value="EUR">EUR - Euro</option>
                            <option value="GBP">GBP - British Pound</option>
                            <option value="CAD">CAD - Canadian Dollar</option>
                        </select>
                    </div>

                    <div class="flex items-center">
                        <input type="checkbox" id="stripe_test_mode" name="stripe_test_mode" value="1" 
                               class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                        <label for="stripe_test_mode" class="ml-2 block text-sm text-gray-700">
                            Enable test mode
                        </label>
                    </div>
                </div>
            </div>

            <!-- Security Settings -->
            <div id="security-tab" class="tab-content hidden">
                <h3 class="text-lg font-medium text-gray-900 mb-6">Security Settings</h3>
                
                <div class="space-y-6">
                    <div class="flex items-center">
                        <input type="checkbox" id="require_email_verification" name="require_email_verification" value="1" 
                               class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                        <label for="require_email_verification" class="ml-2 block text-sm text-gray-700">
                            Require email verification for new accounts
                        </label>
                    </div>

                    <div class="flex items-center">
                        <input type="checkbox" id="enable_two_factor" name="enable_two_factor" value="1" 
                               class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                        <label for="enable_two_factor" class="ml-2 block text-sm text-gray-700">
                            Enable two-factor authentication
                        </label>
                    </div>

                    <div class="flex items-center">
                        <input type="checkbox" id="enable_captcha" name="enable_captcha" value="1" 
                               class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                        <label for="enable_captcha" class="ml-2 block text-sm text-gray-700">
                            Enable CAPTCHA on registration and login
                        </label>
                    </div>

                    <div>
                        <label for="session_lifetime" class="block text-sm font-medium text-gray-700 mb-2">Session Lifetime (minutes)</label>
                        <input type="number" id="session_lifetime" name="session_lifetime" value="{{ old('session_lifetime', '120') }}" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500">
                    </div>

                    <div>
                        <label for="password_min_length" class="block text-sm font-medium text-gray-700 mb-2">Minimum Password Length</label>
                        <input type="number" id="password_min_length" name="password_min_length" value="{{ old('password_min_length', '8') }}" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500" min="6" max="20">
                    </div>
                </div>
            </div>

            <!-- Save Button -->
            <div class="flex items-center justify-end pt-6 border-t border-gray-200 mt-8">
                <button type="submit" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500">
                    Save Settings
                </button>
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const tabButtons = document.querySelectorAll('.tab-button');
    const tabContents = document.querySelectorAll('.tab-content');

    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const tabName = this.getAttribute('data-tab');
            
            // Remove active class from all buttons
            tabButtons.forEach(btn => {
                btn.classList.remove('active', 'border-purple-500', 'text-purple-600');
                btn.classList.add('border-transparent', 'text-gray-500');
            });
            
            // Add active class to clicked button
            this.classList.add('active', 'border-purple-500', 'text-purple-600');
            this.classList.remove('border-transparent', 'text-gray-500');
            
            // Hide all tab contents
            tabContents.forEach(content => {
                content.classList.add('hidden');
            });
            
            // Show selected tab content
            document.getElementById(tabName + '-tab').classList.remove('hidden');
        });
    });
});
</script>
@endsection
