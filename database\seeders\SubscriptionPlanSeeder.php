<?php

namespace Database\Seeders;

use App\Models\SubscriptionPlan;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class SubscriptionPlanSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $plans = [
            [
                'name' => 'Free',
                'slug' => 'free',
                'stripe_price_id' => null,
                'stripe_product_id' => null,
                'price' => 0.00,
                'billing_period' => 'monthly',
                'description' => 'Perfect for getting started with basic job posting needs.',
                'features' => [
                    '1 job posting per month',
                    'Basic job listing visibility',
                    'Email support',
                    'Standard application management'
                ],
                'job_posting_limit' => 1,
                'featured_jobs' => 0,
                'resume_database_access' => false,
                'advanced_analytics' => false,
                'is_active' => true,
                'sort_order' => 1,
            ],
            [
                'name' => 'Starter',
                'slug' => 'starter-monthly',
                'stripe_price_id' => 'price_starter_monthly',
                'stripe_product_id' => 'prod_starter',
                'price' => 29.99,
                'billing_period' => 'monthly',
                'description' => 'Ideal for small businesses and startups looking to hire regularly.',
                'features' => [
                    '5 job postings per month',
                    'Priority listing placement',
                    '1 featured job posting',
                    'Basic resume database access',
                    'Email and chat support',
                    'Application tracking'
                ],
                'job_posting_limit' => 5,
                'featured_jobs' => 1,
                'resume_database_access' => true,
                'advanced_analytics' => false,
                'is_active' => true,
                'sort_order' => 2,
            ],
            [
                'name' => 'Starter',
                'slug' => 'starter-yearly',
                'stripe_price_id' => 'price_starter_yearly',
                'stripe_product_id' => 'prod_starter',
                'price' => 299.99,
                'billing_period' => 'yearly',
                'description' => 'Ideal for small businesses and startups looking to hire regularly. Save 17% with annual billing.',
                'features' => [
                    '5 job postings per month',
                    'Priority listing placement',
                    '1 featured job posting',
                    'Basic resume database access',
                    'Email and chat support',
                    'Application tracking',
                    '2 months free with annual plan'
                ],
                'job_posting_limit' => 5,
                'featured_jobs' => 1,
                'resume_database_access' => true,
                'advanced_analytics' => false,
                'is_active' => true,
                'sort_order' => 3,
            ],
            [
                'name' => 'Professional',
                'slug' => 'professional-monthly',
                'stripe_price_id' => 'price_professional_monthly',
                'stripe_product_id' => 'prod_professional',
                'price' => 79.99,
                'billing_period' => 'monthly',
                'description' => 'Perfect for growing companies with regular hiring needs.',
                'features' => [
                    '15 job postings per month',
                    'Premium listing placement',
                    '5 featured job postings',
                    'Full resume database access',
                    'Advanced search filters',
                    'Basic analytics dashboard',
                    'Priority support',
                    'Custom branding options'
                ],
                'job_posting_limit' => 15,
                'featured_jobs' => 5,
                'resume_database_access' => true,
                'advanced_analytics' => true,
                'is_active' => true,
                'sort_order' => 4,
            ],
            [
                'name' => 'Professional',
                'slug' => 'professional-yearly',
                'stripe_price_id' => 'price_professional_yearly',
                'stripe_product_id' => 'prod_professional',
                'price' => 799.99,
                'billing_period' => 'yearly',
                'description' => 'Perfect for growing companies with regular hiring needs. Save 17% with annual billing.',
                'features' => [
                    '15 job postings per month',
                    'Premium listing placement',
                    '5 featured job postings',
                    'Full resume database access',
                    'Advanced search filters',
                    'Basic analytics dashboard',
                    'Priority support',
                    'Custom branding options',
                    '2 months free with annual plan'
                ],
                'job_posting_limit' => 15,
                'featured_jobs' => 5,
                'resume_database_access' => true,
                'advanced_analytics' => true,
                'is_active' => true,
                'sort_order' => 5,
            ],
            [
                'name' => 'Enterprise',
                'slug' => 'enterprise-monthly',
                'stripe_price_id' => 'price_enterprise_monthly',
                'stripe_product_id' => 'prod_enterprise',
                'price' => 199.99,
                'billing_period' => 'monthly',
                'description' => 'Comprehensive solution for large organizations with extensive hiring needs.',
                'features' => [
                    'Unlimited job postings',
                    'Premium listing placement',
                    'Unlimited featured job postings',
                    'Full resume database access',
                    'Advanced analytics & reporting',
                    'Custom integrations',
                    'Dedicated account manager',
                    'White-label options',
                    'API access',
                    '24/7 priority support'
                ],
                'job_posting_limit' => null,
                'featured_jobs' => -1,
                'resume_database_access' => true,
                'advanced_analytics' => true,
                'is_active' => true,
                'sort_order' => 6,
            ],
            [
                'name' => 'Enterprise',
                'slug' => 'enterprise-yearly',
                'stripe_price_id' => 'price_enterprise_yearly',
                'stripe_product_id' => 'prod_enterprise',
                'price' => 1999.99,
                'billing_period' => 'yearly',
                'description' => 'Comprehensive solution for large organizations with extensive hiring needs. Save 17% with annual billing.',
                'features' => [
                    'Unlimited job postings',
                    'Premium listing placement',
                    'Unlimited featured job postings',
                    'Full resume database access',
                    'Advanced analytics & reporting',
                    'Custom integrations',
                    'Dedicated account manager',
                    'White-label options',
                    'API access',
                    '24/7 priority support',
                    '2 months free with annual plan'
                ],
                'job_posting_limit' => null,
                'featured_jobs' => -1,
                'resume_database_access' => true,
                'advanced_analytics' => true,
                'is_active' => true,
                'sort_order' => 7,
            ],
        ];

        foreach ($plans as $plan) {
            SubscriptionPlan::firstOrCreate(
                ['slug' => $plan['slug']],
                $plan
            );
        }
    }
}
