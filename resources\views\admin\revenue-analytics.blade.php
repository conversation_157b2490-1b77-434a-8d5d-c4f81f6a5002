@extends('layouts.admin')

@section('title', 'Revenue Analytics')

@section('content')
<div class="container mx-auto px-4 py-8">
    <!-- Header -->
    <div class="flex justify-between items-center mb-8">
        <div>
            <h1 class="text-3xl font-bold text-gray-900">Revenue Analytics</h1>
            <p class="text-gray-600 mt-2">Track subscription revenue and financial performance</p>
        </div>
        
        <!-- Period Filter -->
        <div class="flex items-center space-x-4">
            <label for="period" class="text-sm font-medium text-gray-700">Period:</label>
            <select id="period" name="period" class="border border-gray-300 rounded-md px-3 py-2 text-sm" onchange="updatePeriod(this.value)">
                <option value="7" {{ $period == '7' ? 'selected' : '' }}>Last 7 days</option>
                <option value="30" {{ $period == '30' ? 'selected' : '' }}>Last 30 days</option>
                <option value="90" {{ $period == '90' ? 'selected' : '' }}>Last 90 days</option>
                <option value="365" {{ $period == '365' ? 'selected' : '' }}>Last year</option>
            </select>
        </div>
    </div>

    <!-- Revenue Overview Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <!-- Total Revenue -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Total Revenue</p>
                    <p class="text-3xl font-bold text-green-600">${{ number_format($revenueData['total_revenue'], 2) }}</p>
                </div>
                <div class="p-3 bg-green-100 rounded-full">
                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                    </svg>
                </div>
            </div>
        </div>

        <!-- New Subscriptions -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">New Subscriptions</p>
                    <p class="text-3xl font-bold text-blue-600">{{ number_format($revenueData['new_subscriptions']) }}</p>
                </div>
                <div class="p-3 bg-blue-100 rounded-full">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"></path>
                    </svg>
                </div>
            </div>
        </div>

        <!-- Active Subscriptions -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Active Subscriptions</p>
                    <p class="text-3xl font-bold text-purple-600">{{ $subscriptionStatus['active'] ?? 0 }}</p>
                </div>
                <div class="p-3 bg-purple-100 rounded-full">
                    <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Revenue by Plan -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Revenue by Plan</h3>
            <div class="space-y-4">
                @forelse($revenueData['revenue_by_plan'] as $planName => $revenue)
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-gray-700">{{ $planName }}</span>
                        <div class="flex items-center space-x-2">
                            <div class="w-32 bg-gray-200 rounded-full h-2">
                                <div class="bg-blue-600 h-2 rounded-full" style="width: {{ ($revenue / $revenueData['total_revenue']) * 100 }}%"></div>
                            </div>
                            <span class="text-sm font-semibold text-gray-900">${{ number_format($revenue, 2) }}</span>
                        </div>
                    </div>
                @empty
                    <p class="text-gray-500 text-center py-4">No revenue data available</p>
                @endforelse
            </div>
        </div>

        <!-- Monthly Revenue Trend -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Monthly Revenue Trend</h3>
            <div class="space-y-3">
                @forelse($monthlyRevenue as $month => $revenue)
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-gray-700">{{ date('M Y', strtotime($month . '-01')) }}</span>
                        <span class="text-sm font-semibold text-green-600">${{ number_format($revenue, 2) }}</span>
                    </div>
                @empty
                    <p class="text-gray-500 text-center py-4">No monthly data available</p>
                @endforelse
            </div>
        </div>
    </div>

    <!-- Subscription Status Breakdown -->
    <div class="mt-8">
        <div class="bg-white rounded-lg shadow-md p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Subscription Status Breakdown</h3>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                @foreach($subscriptionStatus as $status => $count)
                    <div class="text-center p-4 border border-gray-200 rounded-lg">
                        <p class="text-2xl font-bold text-gray-900">{{ number_format($count) }}</p>
                        <p class="text-sm text-gray-600 capitalize">{{ str_replace('_', ' ', $status) }}</p>
                    </div>
                @endforeach
            </div>
        </div>
    </div>
</div>

<script>
function updatePeriod(period) {
    const url = new URL(window.location);
    url.searchParams.set('period', period);
    window.location.href = url.toString();
}
</script>
@endsection