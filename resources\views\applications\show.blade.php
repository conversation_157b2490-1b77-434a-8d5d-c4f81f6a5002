@extends('layouts.app')

@section('title', 'Application Details')

@section('content')
<div class="min-h-screen bg-gray-50 py-8">
    <div class="mx-auto max-w-4xl px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <nav class="flex" aria-label="Breadcrumb">
                <ol role="list" class="flex items-center space-x-4">
                    <li>
                        <div>
                            <a href="{{ route('applications.index') }}" class="text-gray-400 hover:text-gray-500">
                                <svg class="h-5 w-5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                                    <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
                                </svg>
                                <span class="sr-only">Applications</span>
                            </a>
                        </div>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <svg class="h-5 w-5 flex-shrink-0 text-gray-300" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                                <path d="M5.555 17.776l8-16 .894.448-8 16-.894-.448z" />
                            </svg>
                            <a href="{{ route('applications.index') }}" class="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700">My Applications</a>
                        </div>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <svg class="h-5 w-5 flex-shrink-0 text-gray-300" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                                <path d="M5.555 17.776l8-16 .894.448-8 16-.894-.448z" />
                            </svg>
                            <span class="ml-4 text-sm font-medium text-gray-500">Application Details</span>
                        </div>
                    </li>
                </ol>
            </nav>
            
            <div class="mt-4">
                <h1 class="text-3xl font-bold leading-tight tracking-tight text-gray-900">Application Details</h1>
                <p class="mt-2 text-lg text-gray-600">Track your application progress and details</p>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Content -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Job Information -->
                <div class="bg-white shadow rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h2 class="text-lg font-medium text-gray-900">Job Information</h2>
                    </div>
                    <div class="p-6">
                        <div class="flex items-start space-x-4">
                            @if($application->job->company->logo)
                                <img src="{{ $application->job->company->logo }}" alt="{{ $application->job->company->name }}" class="w-16 h-16 rounded-lg object-cover">
                            @else
                                <div class="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center">
                                    <span class="text-gray-500 font-medium text-xl">{{ substr($application->job->company->name, 0, 1) }}</span>
                                </div>
                            @endif
                            <div class="flex-1">
                                <h3 class="text-xl font-semibold text-gray-900">
                                    <a href="{{ route('jobs.show', $application->job) }}" class="hover:text-blue-600">
                                        {{ $application->job->title }}
                                    </a>
                                </h3>
                                <p class="text-lg text-gray-600">
                                    <a href="{{ route('companies.show', $application->job->company) }}" class="hover:text-blue-600">
                                        {{ $application->job->company->name }}
                                    </a>
                                </p>
                                
                                <div class="mt-3 flex flex-wrap items-center gap-4 text-sm text-gray-500">
                                    <div class="flex items-center">
                                        <svg class="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M15 10.5a3 3 0 11-6 0 3 3 0 016 0z" />
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1115 0z" />
                                        </svg>
                                        {{ $application->job->location }}
                                    </div>
                                    <div class="flex items-center">
                                        <svg class="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M20.25 14.15v4.25c0 1.094-.787 2.036-1.872 2.18-2.087.277-4.216.42-6.378.42s-4.291-.143-6.378-.42c-1.085-.144-1.872-1.086-1.872-2.18v-4.25m16.5 0a2.18 2.18 0 00.75-1.661V8.706c0-1.081-.768-2.015-1.837-2.175a48.114 48.114 0 00-3.413-.387m4.5 8.006c-.194.165-.42.295-.673.38A23.978 23.978 0 0112 15.75c-2.648 0-5.195-.429-7.577-1.22a2.016 2.016 0 01-.673-.38m0 0A2.18 2.18 0 013 12.489V8.706c0-1.081.768-2.015 1.837-2.175a48.111 48.111 0 013.413-.387m7.5 0V5.25A2.25 2.25 0 0013.5 3h-3a2.25 2.25 0 00-2.25 2.25v.894m7.5 0a48.667 48.667 0 00-7.5 0M12 12.75h.008v.008H12v-.008z" />
                                        </svg>
                                        {{ ucfirst(str_replace('_', ' ', $application->job->job_type)) }}
                                    </div>
                                    @if($application->job->salary_min && $application->job->salary_max)
                                        <div class="flex items-center">
                                            <svg class="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v12m-3-2.818l.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                            ${{ number_format($application->job->salary_min) }} - ${{ number_format($application->job->salary_max) }}
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Application Details -->
                <div class="bg-white shadow rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h2 class="text-lg font-medium text-gray-900">Application Details</h2>
                    </div>
                    <div class="p-6 space-y-6">
                        <!-- Resume -->
                        @if($application->resume_path)
                            <div>
                                <h3 class="text-sm font-medium text-gray-900 mb-2">Resume</h3>
                                <div class="flex items-center space-x-3">
                                    <div class="flex-shrink-0">
                                        <svg class="h-8 w-8 text-red-600" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m6.75 18H3.75a1.5 1.5 0 01-1.5-1.5V4.5a1.5 1.5 0 011.5-1.5h5.25a1.5 1.5 0 011.5 1.5v10.5a1.5 1.5 0 001.5 1.5h3.75a1.5 1.5 0 001.5-1.5V11.25a1.5 1.5 0 00-1.5-1.5H12.75a1.5 1.5 0 01-1.5-1.5v-1.5a1.5 1.5 0 011.5-1.5h3.75a1.5 1.5 0 011.5 1.5v1.5a1.5 1.5 0 001.5 1.5h1.5a1.5 1.5 0 011.5 1.5v10.5a1.5 1.5 0 01-1.5 1.5H16.5a1.5 1.5 0 01-1.5-1.5v-1.5a1.5 1.5 0 00-1.5-1.5h-3.75a1.5 1.5 0 01-1.5-1.5V9a1.5 1.5 0 011.5-1.5h1.5a1.5 1.5 0 001.5 1.5v1.5a1.5 1.5 0 001.5 1.5h1.5a1.5 1.5 0 001.5-1.5V6a1.5 1.5 0 00-1.5-1.5h-1.5a1.5 1.5 0 01-1.5-1.5V1.5a1.5 1.5 0 00-1.5-1.5H9a1.5 1.5 0 00-1.5 1.5v1.5a1.5 1.5 0 01-1.5 1.5H4.5a1.5 1.5 0 00-1.5 1.5v10.5a1.5 1.5 0 001.5 1.5H6a1.5 1.5 0 001.5-1.5v-1.5a1.5 1.5 0 011.5-1.5h1.5a1.5 1.5 0 001.5 1.5v1.5a1.5 1.5 0 001.5 1.5z" />
                                        </svg>
                                    </div>
                                    <div class="flex-1">
                                        <p class="text-sm font-medium text-gray-900">{{ basename($application->resume_path) }}</p>
                                        <p class="text-sm text-gray-500">Uploaded {{ $application->created_at->diffForHumans() }}</p>
                                    </div>
                                    <div>
                                        <a href="{{ Storage::url($application->resume_path) }}" target="_blank" class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                            <svg class="-ml-0.5 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" d="M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5M16.5 12L12 16.5m0 0L7.5 12m4.5 4.5V3" />
                                            </svg>
                                            Download
                                        </a>
                                    </div>
                                </div>
                            </div>
                        @endif

                        <!-- Cover Letter -->
                        @if($application->cover_letter)
                            <div>
                                <h3 class="text-sm font-medium text-gray-900 mb-2">Cover Letter</h3>
                                <div class="bg-gray-50 rounded-lg p-4">
                                    <p class="text-sm text-gray-700 whitespace-pre-line">{{ $application->cover_letter }}</p>
                                </div>
                            </div>
                        @endif

                        <!-- Additional Documents -->
                        @if($application->portfolio_url)
                            <div>
                                <h3 class="text-sm font-medium text-gray-900 mb-2">Portfolio</h3>
                                <a href="{{ $application->portfolio_url }}" target="_blank" class="inline-flex items-center text-sm text-blue-600 hover:text-blue-500">
                                    <svg class="-ml-0.5 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M13.19 8.688a4.5 4.5 0 011.242 7.244l-4.5 4.5a4.5 4.5 0 01-6.364-6.364l1.757-1.757m13.35-.622l1.757-1.757a4.5 4.5 0 00-6.364-6.364l-4.5 4.5a4.5 4.5 0 001.242 7.244" />
                                    </svg>
                                    {{ $application->portfolio_url }}
                                </a>
                            </div>
                        @endif

                        <!-- Notes -->
                        @if($application->notes)
                            <div>
                                <h3 class="text-sm font-medium text-gray-900 mb-2">Notes</h3>
                                <div class="bg-gray-50 rounded-lg p-4">
                                    <p class="text-sm text-gray-700">{{ $application->notes }}</p>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Application Timeline -->
                @if($application->timeline && count($application->timeline) > 0)
                    <div class="bg-white shadow rounded-lg">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h2 class="text-lg font-medium text-gray-900">Application Timeline</h2>
                        </div>
                        <div class="p-6">
                            <div class="flow-root">
                                <ul role="list" class="-mb-8">
                                    @foreach($application->timeline as $index => $event)
                                        <li>
                                            <div class="relative pb-8">
                                                @if($index < count($application->timeline) - 1)
                                                    <span class="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200" aria-hidden="true"></span>
                                                @endif
                                                <div class="relative flex space-x-3">
                                                    <div>
                                                        @php
                                                            $iconColors = [
                                                                'pending' => 'bg-yellow-500',
                                                                'reviewed' => 'bg-blue-500',
                                                                'interview' => 'bg-purple-500',
                                                                'offered' => 'bg-green-500',
                                                                'rejected' => 'bg-red-500',
                                                                'withdrawn' => 'bg-gray-500'
                                                            ];
                                                            $iconColor = $iconColors[$event['status']] ?? 'bg-gray-500';
                                                        @endphp
                                                        <span class="h-8 w-8 rounded-full {{ $iconColor }} flex items-center justify-center ring-8 ring-white">
                                                            @if($event['status'] === 'offered')
                                                                <svg class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                                </svg>
                                                            @elseif($event['status'] === 'rejected')
                                                                <svg class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
                                                                </svg>
                                                            @else
                                                                <svg class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                                </svg>
                                                            @endif
                                                        </span>
                                                    </div>
                                                    <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                                                        <div>
                                                            <p class="text-sm text-gray-900 font-medium">{{ ucfirst($event['status']) }}</p>
                                                            @if(isset($event['note']))
                                                                <p class="text-sm text-gray-500">{{ $event['note'] }}</p>
                                                            @endif
                                                        </div>
                                                        <div class="text-right text-sm whitespace-nowrap text-gray-500">
                                                            <time>{{ $event['date'] }}</time>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </li>
                                    @endforeach
                                </ul>
                            </div>
                        </div>
                    </div>
                @endif
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Status Card -->
                <div class="bg-white shadow rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h2 class="text-lg font-medium text-gray-900">Application Status</h2>
                    </div>
                    <div class="p-6">
                        @php
                            $statusColors = [
                                'pending' => 'bg-yellow-100 text-yellow-800',
                                'reviewed' => 'bg-blue-100 text-blue-800',
                                'interview' => 'bg-purple-100 text-purple-800',
                                'offered' => 'bg-green-100 text-green-800',
                                'rejected' => 'bg-red-100 text-red-800',
                                'withdrawn' => 'bg-gray-100 text-gray-800'
                            ];
                            $status = $application->status ?? 'pending';
                        @endphp
                        <div class="text-center">
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium {{ $statusColors[$status] ?? 'bg-gray-100 text-gray-800' }}">
                                {{ ucfirst($status) }}
                            </span>
                            <p class="mt-2 text-sm text-gray-500">Applied {{ $application->created_at->diffForHumans() }}</p>
                            
                            @if($application->interview_date)
                                <div class="mt-4 p-3 bg-purple-50 rounded-lg">
                                    <p class="text-sm font-medium text-purple-900">Interview Scheduled</p>
                                    <p class="text-sm text-purple-700">{{ $application->interview_date->format('M j, Y') }}</p>
                                    <p class="text-sm text-purple-700">{{ $application->interview_date->format('g:i A') }}</p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="bg-white shadow rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h2 class="text-lg font-medium text-gray-900">Quick Actions</h2>
                    </div>
                    <div class="p-6 space-y-3">
                        <a href="{{ route('jobs.show', $application->job) }}" class="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <svg class="-ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z" />
                                <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            </svg>
                            View Job Posting
                        </a>
                        
                        <a href="{{ route('companies.show', $application->job->company) }}" class="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <svg class="-ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 21h19.5m-18-18v18m2.25-18v18m13.5-18v18m2.25-18v18M6.75 6.75h.75m-.75 3h.75m-.75 3h.75m3-6h.75m-.75 3h.75m-.75 3h.75M6.75 21v-3.375c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21M3 3h12m-.75 4.5H21m-3.75 3.75h.75m-.75 3h.75m-.75 3h.75m-3.75-16.5h.75m-.75 3h.75m-.75 3h.75m-.75 3h.75M12 10.5h8.25m-8.25 3h8.25m-8.25 3h8.25" />
                            </svg>
                            View Company
                        </a>
                        
                        @if(in_array($status, ['pending', 'reviewed']))
                            <form method="POST" action="{{ route('applications.withdraw', $application) }}" class="w-full">
                                @csrf
                                @method('PATCH')
                                <button type="submit" onclick="return confirm('Are you sure you want to withdraw this application?')" class="w-full inline-flex justify-center items-center px-4 py-2 border border-red-300 shadow-sm text-sm font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                                    <svg class="-ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                    Withdraw Application
                                </button>
                            </form>
                        @endif
                    </div>
                </div>

                <!-- Application Stats -->
                <div class="bg-white shadow rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h2 class="text-lg font-medium text-gray-900">Application Info</h2>
                    </div>
                    <div class="p-6 space-y-4">
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-500">Application ID</span>
                            <span class="text-sm font-medium text-gray-900">#{{ $application->id }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-500">Applied Date</span>
                            <span class="text-sm font-medium text-gray-900">{{ $application->created_at->format('M j, Y') }}</span>
                        </div>
                        @if($application->updated_at != $application->created_at)
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-500">Last Updated</span>
                                <span class="text-sm font-medium text-gray-900">{{ $application->updated_at->diffForHumans() }}</span>
                            </div>
                        @endif
                        @if($application->job->applications_count)
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-500">Total Applicants</span>
                                <span class="text-sm font-medium text-gray-900">{{ $application->job->applications_count }}</span>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection