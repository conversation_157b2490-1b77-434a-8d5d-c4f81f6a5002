<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Api\Controller;
use App\Models\Job;
use App\Models\JobApplication;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class JobApplicationController extends Controller
{
    /**
     * Display a listing of the user's job applications.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $user = $request->user();
        
        // Ensure user is a job seeker
        if (!$user->isJobSeeker()) {
            return $this->error('Access denied. Only job seekers can view applications.', 403);
        }

        $jobSeekerProfile = $user->jobSeekerProfile;
        
        if (!$jobSeekerProfile) {
            return $this->error('Job seeker profile not found', 404);
        }

        $query = JobApplication::with(['job.employerProfile', 'resume'])
            ->where('job_seeker_profile_id', $jobSeekerProfile->id)
            ->when($request->filled('status'), function ($query) use ($request) {
                return $query->where('status', $request->status);
            })
            ->orderBy('created_at', 'desc');

        $perPage = $request->input('per_page', 15);
        $applications = $query->paginate($perPage);

        return $this->success([
            'applications' => $applications->items(),
            'pagination' => [
                'total' => $applications->total(),
                'per_page' => $applications->perPage(),
                'current_page' => $applications->currentPage(),
                'last_page' => $applications->lastPage(),
            ],
        ]);
    }

    /**
     * Apply for a job.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Job  $job
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request, Job $job): JsonResponse
    {
        $user = $request->user();
        
        // Ensure user is a job seeker
        if (!$user->isJobSeeker()) {
            return $this->error('Access denied. Only job seekers can apply for jobs.', 403);
        }

        $jobSeekerProfile = $user->jobSeekerProfile;
        
        if (!$jobSeekerProfile) {
            return $this->error('Job seeker profile not found. Please complete your profile first.', 404);
        }

        // Check if job is active
        if ($job->status !== 'active') {
            return $this->error('This job is no longer accepting applications.', 400);
        }

        // Check if user has already applied
        $existingApplication = JobApplication::where('job_id', $job->id)
            ->where('job_seeker_profile_id', $jobSeekerProfile->id)
            ->first();

        if ($existingApplication) {
            return $this->error('You have already applied for this job.', 400);
        }

        $validator = Validator::make($request->all(), [
            'resume_id' => ['required', 'exists:resumes,id'],
            'cover_letter' => ['nullable', 'string', 'max:2000'],
        ]);

        if ($validator->fails()) {
            return $this->error('Validation error', 422, $validator->errors());
        }

        // Verify that the resume belongs to the user
        $resume = $jobSeekerProfile->resumes()->find($request->resume_id);
        if (!$resume) {
            return $this->error('Resume not found or does not belong to you.', 404);
        }

        // Create the application
        $application = JobApplication::create([
            'job_id' => $job->id,
            'job_seeker_profile_id' => $jobSeekerProfile->id,
            'resume_id' => $request->resume_id,
            'cover_letter' => $request->cover_letter,
            'status' => 'submitted',
        ]);

        // Increment applications count on the job
        $job->increment('applications_count');

        // Load relationships for response
        $application->load(['job.employerProfile', 'resume']);

        return $this->success([
            'application' => $application
        ], 'Application submitted successfully', 201);
    }

    /**
     * Display the specified application.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\JobApplication  $application
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(Request $request, JobApplication $application): JsonResponse
    {
        $user = $request->user();
        
        // Ensure user owns this application
        if ($application->jobSeekerProfile->user_id !== $user->id) {
            return $this->error('Access denied.', 403);
        }

        $application->load(['job.employerProfile', 'resume']);

        return $this->success([
            'application' => $application
        ]);
    }

    /**
     * Update the specified job application.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\JobApplication  $application
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, JobApplication $application): JsonResponse
    {
        $user = $request->user();
        
        // Ensure user owns this application
        if ($application->jobSeekerProfile->user_id !== $user->id) {
            return $this->error('Access denied.', 403);
        }

        // Check if application can be updated (only submitted applications)
        if ($application->status !== 'submitted') {
            return $this->error('Cannot update application that is not in submitted status', 422);
        }

        $validator = Validator::make($request->all(), [
            'resume_id' => ['sometimes', 'required', 'exists:resumes,id'],
            'cover_letter' => ['nullable', 'string', 'max:2000'],
        ]);

        if ($validator->fails()) {
            return $this->error('Validation error', 422, $validator->errors());
        }

        $updateData = [];

        if ($request->filled('cover_letter')) {
            $updateData['cover_letter'] = $request->cover_letter;
        }

        if ($request->filled('resume_id')) {
            // Verify that the resume belongs to the user
            $resume = $application->jobSeekerProfile->resumes()->find($request->resume_id);
            if (!$resume) {
                return $this->error('Resume not found or does not belong to you.', 404);
            }
            $updateData['resume_id'] = $request->resume_id;
        }

        // Update application
        $application->update($updateData);

        // Load relationships for response
        $application->load(['job.employerProfile', 'resume']);

        return $this->success([
            'application' => $application
        ], 'Application updated successfully');
    }

    /**
     * Withdraw the specified job application.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\JobApplication  $application
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(Request $request, JobApplication $application): JsonResponse
    {
        $user = $request->user();
        
        // Ensure user owns this application
        if ($application->jobSeekerProfile->user_id !== $user->id) {
            return $this->error('Access denied.', 403);
        }

        // Check if application can be withdrawn
        if (!in_array($application->status, ['submitted', 'under_review'])) {
            return $this->error('Cannot withdraw application with current status', 422);
        }

        // Update status to withdrawn instead of deleting
        $application->update(['status' => 'withdrawn']);

        return $this->success([
            'message' => 'Application withdrawn successfully'
        ]);
    }

    /**
     * Get application statistics for the authenticated user.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function stats(Request $request): JsonResponse
    {
        $user = $request->user();
        
        // Ensure user is a job seeker
        if (!$user->isJobSeeker()) {
            return $this->error('Access denied. Only job seekers can view application statistics.', 403);
        }

        $jobSeekerProfile = $user->jobSeekerProfile;
        
        if (!$jobSeekerProfile) {
            return $this->error('Job seeker profile not found', 404);
        }
        
        $stats = [
            'total_applications' => JobApplication::where('job_seeker_profile_id', $jobSeekerProfile->id)->count(),
            'submitted_applications' => JobApplication::where('job_seeker_profile_id', $jobSeekerProfile->id)
                ->where('status', 'submitted')->count(),
            'under_review_applications' => JobApplication::where('job_seeker_profile_id', $jobSeekerProfile->id)
                ->where('status', 'under_review')->count(),
            'shortlisted_applications' => JobApplication::where('job_seeker_profile_id', $jobSeekerProfile->id)
                ->where('status', 'shortlisted')->count(),
            'interviewed_applications' => JobApplication::where('job_seeker_profile_id', $jobSeekerProfile->id)
                ->where('status', 'interviewed')->count(),
            'offered_applications' => JobApplication::where('job_seeker_profile_id', $jobSeekerProfile->id)
                ->where('status', 'offered')->count(),
            'hired_applications' => JobApplication::where('job_seeker_profile_id', $jobSeekerProfile->id)
                ->where('status', 'hired')->count(),
            'rejected_applications' => JobApplication::where('job_seeker_profile_id', $jobSeekerProfile->id)
                ->where('status', 'rejected')->count(),
            'withdrawn_applications' => JobApplication::where('job_seeker_profile_id', $jobSeekerProfile->id)
                ->where('status', 'withdrawn')->count(),
        ];

        return $this->success($stats);
    }

    /**
     * Get recent application activity.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function recentActivity(Request $request): JsonResponse
    {
        $user = $request->user();
        $limit = $request->input('limit', 10);
        
        // Ensure user is a job seeker
        if (!$user->isJobSeeker()) {
            return $this->error('Access denied. Only job seekers can view application activity.', 403);
        }

        $jobSeekerProfile = $user->jobSeekerProfile;
        
        if (!$jobSeekerProfile) {
            return $this->error('Job seeker profile not found', 404);
        }
        
        $recentApplications = JobApplication::with(['job.employerProfile'])
            ->where('job_seeker_profile_id', $jobSeekerProfile->id)
            ->orderBy('updated_at', 'desc')
            ->limit($limit)
            ->get()
            ->map(function ($application) {
                return [
                    'id' => $application->id,
                    'job_title' => $application->job->title,
                    'company_name' => $application->job->employerProfile->company_name,
                    'status' => $application->status,
                    'applied_at' => $application->created_at,
                    'updated_at' => $application->updated_at,
                ];
            });

        return $this->success([
            'recent_applications' => $recentApplications
        ]);
    }
}