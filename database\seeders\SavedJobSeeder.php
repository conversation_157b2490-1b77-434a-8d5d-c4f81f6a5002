<?php

namespace Database\Seeders;

use App\Models\SavedJob;
use App\Models\Job;
use App\Models\User;
use Illuminate\Database\Seeder;

class SavedJobSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $jobs = Job::where('status', 'active')->get();
        $jobSeekers = User::where('role', 'job_seeker')->get();
        
        if ($jobs->isEmpty() || $jobSeekers->isEmpty()) {
            $this->command->warn('No active jobs or job seekers found. Please run JobSeeder and UserSeeder first.');
            return;
        }

        foreach ($jobSeekers as $jobSeeker) {
            // Each job seeker saves 0-10 jobs
            $savedJobCount = fake()->numberBetween(0, 10);
            $savedJobs = $jobs->random(min($savedJobCount, $jobs->count()));
            
            foreach ($savedJobs as $job) {
                // Check if job is already saved by this user
                $existingSavedJob = SavedJob::where('user_id', $jobSeeker->id)
                    ->where('job_id', $job->id)
                    ->first();
                    
                if ($existingSavedJob) {
                    continue;
                }

                SavedJob::create([
                    'user_id' => $jobSeeker->id,
                    'job_id' => $job->id,
                    'notes' => fake()->optional(0.4)->paragraph(),
                    'created_at' => fake()->dateTimeBetween('-3 months', 'now'),
                ]);
            }
        }

        $this->command->info('Saved jobs created successfully!');
    }
}