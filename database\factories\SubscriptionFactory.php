<?php

namespace Database\Factories;

use App\Models\User;
use App\Models\SubscriptionPlan;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Subscription>
 */
class SubscriptionFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $startsAt = $this->faker->dateTimeBetween('-6 months', 'now');
        $endsAt = (clone $startsAt)->modify('+1 month');
        
        return [
            'user_id' => User::factory(),
            'subscription_plan_id' => SubscriptionPlan::inRandomOrder()->first()?->id ?? 1,
            'status' => $this->faker->randomElement(['active', 'inactive', 'cancelled', 'expired']),
            'starts_at' => $startsAt,
            'ends_at' => $endsAt,
            'cancelled_at' => null,
            'payment_method' => $this->faker->randomElement(['stripe', 'paypal', 'credit_card']),
            'payment_status' => $this->faker->randomElement(['pending', 'paid', 'failed']),
            'stripe_subscription_id' => $this->faker->optional()->regexify('sub_[A-Za-z0-9]{24}'),
        ];
    }

    /**
     * Indicate that the subscription is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'active',
            'payment_status' => 'paid',
            'starts_at' => now()->subDays(rand(1, 30)),
            'ends_at' => now()->addDays(rand(1, 30)),
            'cancelled_at' => null,
        ]);
    }

    /**
     * Indicate that the subscription is cancelled.
     */
    public function cancelled(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'cancelled',
            'cancelled_at' => $this->faker->dateTimeBetween('-1 month', 'now'),
        ]);
    }

    /**
     * Indicate that the subscription is expired.
     */
    public function expired(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'expired',
            'starts_at' => $this->faker->dateTimeBetween('-3 months', '-2 months'),
            'ends_at' => $this->faker->dateTimeBetween('-2 months', '-1 month'),
        ]);
    }
}