<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Api\Controller;
use App\Models\Job;
use App\Models\JobApplication;
use App\Models\SavedJob;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class JobController extends Controller
{
    /**
     * Display a listing of jobs with filtering options.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $query = Job::with('employerProfile')
            ->active()
            ->when($request->filled('search'), function ($query) use ($request) {
                return $query->where('title', 'like', '%' . $request->search . '%')
                    ->orWhere('description', 'like', '%' . $request->search . '%');
            })
            ->when($request->filled('location'), function ($query) use ($request) {
                return $query->where('location', 'like', '%' . $request->location . '%');
            })
            ->when($request->filled('job_type'), function ($query) use ($request) {
                return $query->where('job_type', $request->job_type);
            })
            ->when($request->filled('experience_level'), function ($query) use ($request) {
                return $query->where('experience_level', $request->experience_level);
            })
            ->when($request->filled('industry'), function ($query) use ($request) {
                return $query->where('industry', $request->industry);
            })
            ->when($request->filled('salary_min'), function ($query) use ($request) {
                return $query->where('salary_min', '>=', $request->salary_min);
            })
            ->when($request->filled('salary_max'), function ($query) use ($request) {
                return $query->where('salary_max', '<=', $request->salary_max);
            })
            ->when($request->filled('remote'), function ($query) use ($request) {
                return $query->where('is_remote', filter_var($request->remote, FILTER_VALIDATE_BOOLEAN));
            })
            ->when($request->filled('featured'), function ($query) use ($request) {
                return $query->where('is_featured', filter_var($request->featured, FILTER_VALIDATE_BOOLEAN));
            });

        // Handle sorting
        $sortField = $request->input('sort_by', 'created_at');
        $sortDirection = $request->input('sort_direction', 'desc');
        $allowedSortFields = ['title', 'created_at', 'salary_min', 'salary_max'];
        
        if (in_array($sortField, $allowedSortFields)) {
            $query->orderBy($sortField, $sortDirection === 'asc' ? 'asc' : 'desc');
        }

        $perPage = $request->input('per_page', 15);
        $jobs = $query->paginate($perPage);

        return $this->success([
            'jobs' => $jobs->items(),
            'pagination' => [
                'total' => $jobs->total(),
                'per_page' => $jobs->perPage(),
                'current_page' => $jobs->currentPage(),
                'last_page' => $jobs->lastPage(),
            ],
        ]);
    }

    /**
     * Display the specified job.
     *
     * @param  \App\Models\Job  $job
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(Job $job): JsonResponse
    {
        // Check if job is active
        if ($job->status !== 'active') {
            return $this->error('Job not found', 404);
        }

        // Increment view count
        $job->increment('views_count');

        // Load the employer profile relationship
        $job->load('employerProfile');

        // Add user-specific data if authenticated
        $jobData = $job->toArray();
        if (Auth::check()) {
            $user = Auth::user();
            $jobData['is_saved'] = SavedJob::where('user_id', $user->id)
                ->where('job_id', $job->id)
                ->exists();
            $jobData['is_applied'] = JobApplication::where('user_id', $user->id)
                ->where('job_id', $job->id)
                ->exists();
        }

        return $this->success([
            'job' => $jobData
        ]);
    }

    /**
     * Get job statistics and filter data.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function stats(): JsonResponse
    {
        $stats = [
            'total_jobs' => Job::active()->count(),
            'total_companies' => Job::active()->distinct('employer_id')->count(),
            'job_types' => Job::active()
                ->select('job_type', DB::raw('count(*) as count'))
                ->groupBy('job_type')
                ->pluck('count', 'job_type'),
            'experience_levels' => Job::active()
                ->select('experience_level', DB::raw('count(*) as count'))
                ->groupBy('experience_level')
                ->pluck('count', 'experience_level'),
            'industries' => Job::active()
                ->select('industry', DB::raw('count(*) as count'))
                ->groupBy('industry')
                ->orderBy('count', 'desc')
                ->limit(10)
                ->pluck('count', 'industry'),
            'locations' => Job::active()
                ->select('location', DB::raw('count(*) as count'))
                ->groupBy('location')
                ->orderBy('count', 'desc')
                ->limit(10)
                ->pluck('count', 'location'),
        ];

        return $this->success($stats);
    }

    /**
     * Get search suggestions for autocomplete.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function searchSuggestions(Request $request): JsonResponse
    {
        $query = $request->input('q', '');
        $type = $request->input('type', 'all'); // all, title, company, location
        $limit = $request->input('limit', 10);

        $suggestions = [];

        if ($type === 'all' || $type === 'title') {
            $titles = Job::active()
                ->where('title', 'like', '%' . $query . '%')
                ->distinct()
                ->limit($limit)
                ->pluck('title')
                ->map(function ($title) {
                    return ['type' => 'title', 'value' => $title];
                });
            $suggestions = array_merge($suggestions, $titles->toArray());
        }

        if ($type === 'all' || $type === 'company') {
            $companies = Job::active()
                ->join('employer_profiles', 'jobs.employer_id', '=', 'employer_profiles.user_id')
                ->where('employer_profiles.company_name', 'like', '%' . $query . '%')
                ->distinct()
                ->limit($limit)
                ->pluck('employer_profiles.company_name')
                ->map(function ($company) {
                    return ['type' => 'company', 'value' => $company];
                });
            $suggestions = array_merge($suggestions, $companies->toArray());
        }

        if ($type === 'all' || $type === 'location') {
            $locations = Job::active()
                ->where('location', 'like', '%' . $query . '%')
                ->distinct()
                ->limit($limit)
                ->pluck('location')
                ->map(function ($location) {
                    return ['type' => 'location', 'value' => $location];
                });
            $suggestions = array_merge($suggestions, $locations->toArray());
        }

        // Limit total suggestions
        $suggestions = array_slice($suggestions, 0, $limit);

        return $this->success([
            'suggestions' => $suggestions
        ]);
    }

    /**
     * Get featured jobs.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function featured(Request $request): JsonResponse
    {
        $limit = $request->input('limit', 6);

        $jobs = Job::with('employerProfile')
            ->active()
            ->featured()
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();

        // Add user-specific data if authenticated
        if (Auth::check()) {
            $user = Auth::user();
            $savedJobIds = SavedJob::where('user_id', $user->id)
                ->whereIn('job_id', $jobs->pluck('id'))
                ->pluck('job_id')
                ->toArray();
            $appliedJobIds = JobApplication::where('user_id', $user->id)
                ->whereIn('job_id', $jobs->pluck('id'))
                ->pluck('job_id')
                ->toArray();

            $jobs->transform(function ($job) use ($savedJobIds, $appliedJobIds) {
                $jobArray = $job->toArray();
                $jobArray['is_saved'] = in_array($job->id, $savedJobIds);
                $jobArray['is_applied'] = in_array($job->id, $appliedJobIds);
                return $jobArray;
            });
        }

        return $this->success([
            'jobs' => $jobs
        ]);
    }

    /**
     * Get similar jobs based on the given job.
     *
     * @param  \App\Models\Job  $job
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function similar(Job $job, Request $request): JsonResponse
    {
        $limit = $request->input('limit', 5);

        $similarJobs = Job::with('employerProfile')
            ->active()
            ->where('id', '!=', $job->id)
            ->where(function ($query) use ($job) {
                $query->where('industry', $job->industry)
                    ->orWhere('job_type', $job->job_type)
                    ->orWhere('experience_level', $job->experience_level)
                    ->orWhere('location', 'like', '%' . $job->location . '%');
            })
            ->orderByRaw('CASE 
                WHEN industry = ? THEN 4
                WHEN job_type = ? THEN 3
                WHEN experience_level = ? THEN 2
                WHEN location LIKE ? THEN 1
                ELSE 0
            END DESC', [$job->industry, $job->job_type, $job->experience_level, '%' . $job->location . '%'])
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();

        // Add user-specific data if authenticated
        if (Auth::check()) {
            $user = Auth::user();
            $savedJobIds = SavedJob::where('user_id', $user->id)
                ->whereIn('job_id', $similarJobs->pluck('id'))
                ->pluck('job_id')
                ->toArray();
            $appliedJobIds = JobApplication::where('user_id', $user->id)
                ->whereIn('job_id', $similarJobs->pluck('id'))
                ->pluck('job_id')
                ->toArray();

            $similarJobs->transform(function ($job) use ($savedJobIds, $appliedJobIds) {
                $jobArray = $job->toArray();
                $jobArray['is_saved'] = in_array($job->id, $savedJobIds);
                $jobArray['is_applied'] = in_array($job->id, $appliedJobIds);
                return $jobArray;
            });
        }

        return $this->success([
            'jobs' => $similarJobs
        ]);
    }
}