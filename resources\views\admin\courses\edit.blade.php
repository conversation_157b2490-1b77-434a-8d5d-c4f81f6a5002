@extends('layouts.admin')

@section('title', 'Edit Course')

@section('content')
<div class="max-w-4xl mx-auto">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Edit Course</h1>
                <p class="mt-2 text-gray-600">Update course information</p>
            </div>
            <a href="{{ route('admin.courses.index') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Back to Courses
            </a>
        </div>
    </div>

    <!-- Course Edit Form -->
    <div class="bg-white shadow-sm rounded-lg">
        <form action="{{ route('admin.courses.update', $course) }}" method="POST" enctype="multipart/form-data" class="space-y-6 p-6">
            @csrf
            @method('PUT')

            <!-- Basic Information -->
            <div class="space-y-6">
                <h2 class="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2">Basic Information</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="md:col-span-2">
                        <label for="title" class="block text-sm font-medium text-gray-700 mb-2">Course Title</label>
                        <input type="text" id="title" name="title" value="{{ old('title', $course->title) }}" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500" 
                               placeholder="e.g., Complete Web Development Bootcamp" required>
                        @error('title')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="category" class="block text-sm font-medium text-gray-700 mb-2">Category</label>
                        <select id="category" name="category" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500" required>
                            <option value="">Select Category</option>
                            @foreach($categories as $category)
                                <option value="{{ $category }}" {{ old('category', $course->category) === $category ? 'selected' : '' }}>{{ ucfirst($category) }}</option>
                            @endforeach
                        </select>
                        @error('category')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="difficulty_level" class="block text-sm font-medium text-gray-700 mb-2">Difficulty Level</label>
                        <select id="difficulty_level" name="difficulty_level" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500" required>
                            <option value="">Select Level</option>
                            @foreach($levels as $level)
                                <option value="{{ $level }}" {{ old('difficulty_level', $course->difficulty_level) === $level ? 'selected' : '' }}>{{ ucfirst($level) }}</option>
                            @endforeach
                        </select>
                        @error('difficulty_level')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="instructor_id" class="block text-sm font-medium text-gray-700 mb-2">Instructor</label>
                        <select id="instructor_id" name="instructor_id" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500" required>
                            <option value="">Select Instructor</option>
                            @foreach($instructors as $instructor)
                                <option value="{{ $instructor->id }}" {{ old('instructor_id', $course->instructor_id) == $instructor->id ? 'selected' : '' }}>{{ $instructor->name }}</option>
                            @endforeach
                        </select>
                        @error('instructor_id')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="duration_hours" class="block text-sm font-medium text-gray-700 mb-2">Duration (Hours)</label>
                        <input type="number" id="duration_hours" name="duration_hours" value="{{ old('duration_hours', $course->duration_hours) }}" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500" 
                               min="1" step="0.5" placeholder="e.g., 40">
                        @error('duration_hours')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="price" class="block text-sm font-medium text-gray-700 mb-2">Price ($)</label>
                        <input type="number" id="price" name="price" value="{{ old('price', $course->price) }}" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500" 
                               min="0" step="0.01" placeholder="0.00">
                        @error('price')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <div>
                    <label for="short_description" class="block text-sm font-medium text-gray-700 mb-2">Short Description</label>
                    <textarea id="short_description" name="short_description" rows="3" 
                              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500" 
                              placeholder="Brief overview of the course (max 200 characters)">{{ old('short_description', $course->short_description) }}</textarea>
                    @error('short_description')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="description" class="block text-sm font-medium text-gray-700 mb-2">Full Description</label>
                    <textarea id="description" name="description" rows="6" 
                              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500" 
                              placeholder="Detailed course description, what students will learn, etc." required>{{ old('description', $course->description) }}</textarea>
                    @error('description')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Course Image -->
            <div class="space-y-6">
                <h2 class="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2">Course Image</h2>
                
                @if($course->getFirstMediaUrl('thumbnail'))
                    <div class="mb-4">
                        <p class="text-sm font-medium text-gray-700 mb-2">Current Image:</p>
                        <img src="{{ $course->getFirstMediaUrl('thumbnail') }}" alt="Course thumbnail" class="w-32 h-20 object-cover rounded-md border">
                    </div>
                @endif
                
                <div>
                    <label for="image" class="block text-sm font-medium text-gray-700 mb-2">Course Thumbnail</label>
                    <input type="file" id="image" name="image" accept="image/*" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500">
                    <p class="mt-1 text-sm text-gray-500">Upload a new course thumbnail image (recommended: 1200x630px) - leave empty to keep current image</p>
                    @error('image')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Prerequisites -->
            <div class="space-y-6">
                <h2 class="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2">Prerequisites</h2>
                
                <div>
                    <label for="prerequisites" class="block text-sm font-medium text-gray-700 mb-2">Prerequisites</label>
                    <textarea id="prerequisites" name="prerequisites" rows="4"
                              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500"
                              placeholder="Enter each prerequisite on a new line">{{ old('prerequisites', is_array($course->prerequisites) ? implode("\n", $course->prerequisites) : $course->prerequisites) }}</textarea>
                    <p class="mt-1 text-sm text-gray-500">Enter each prerequisite on a new line</p>
                    @error('prerequisites')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Learning Outcomes -->
            <div class="space-y-6">
                <h2 class="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2">Learning Outcomes</h2>
                
                <div>
                    <label for="learning_outcomes" class="block text-sm font-medium text-gray-700 mb-2">What Students Will Learn</label>
                    <textarea id="learning_outcomes" name="learning_outcomes" rows="4"
                              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500"
                              placeholder="Enter each learning outcome on a new line">{{ old('learning_outcomes', is_array($course->learning_outcomes) ? implode("\n", $course->learning_outcomes) : $course->learning_outcomes) }}</textarea>
                    <p class="mt-1 text-sm text-gray-500">Enter each learning outcome on a new line</p>
                    @error('learning_outcomes')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Course Settings -->
            <div class="space-y-6">
                <h2 class="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2">Course Settings</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="flex items-center">
                        <input type="checkbox" id="is_published" name="is_published" value="1" 
                               {{ old('is_published', $course->is_published) ? 'checked' : '' }}
                               class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                        <label for="is_published" class="ml-2 block text-sm text-gray-700">
                            Published
                        </label>
                    </div>

                    <div class="flex items-center">
                        <input type="checkbox" id="is_featured" name="is_featured" value="1" 
                               {{ old('is_featured', $course->is_featured) ? 'checked' : '' }}
                               class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                        <label for="is_featured" class="ml-2 block text-sm text-gray-700">
                            Featured course
                        </label>
                    </div>

                    <div class="flex items-center">
                        <input type="checkbox" id="has_certificate" name="has_certificate" value="1" 
                               {{ old('has_certificate', $course->has_certificate) ? 'checked' : '' }}
                               class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                        <label for="has_certificate" class="ml-2 block text-sm text-gray-700">
                            Provides certificate
                        </label>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
                <a href="{{ route('admin.courses.index') }}" class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                    Cancel
                </a>
                <button type="submit" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500">
                    Update Course
                </button>
            </div>
        </form>
    </div>
</div>
@endsection