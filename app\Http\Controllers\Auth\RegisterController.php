<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\JobSeekerProfile;
use App\Models\EmployerProfile;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Auth\Events\Registered;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Validation\Rules\Password;
use Spatie\Permission\Models\Role;

class RegisterController extends Controller
{
    /**
     * Show the registration form.
     */
    public function showRegistrationForm(): View
    {
        return view('auth.register');
    }

    /**
     * Handle registration request.
     */
    public function register(Request $request): RedirectResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users'],
            'password' => ['required', 'confirmed', Password::defaults()],
            'user_type' => ['required', 'in:job_seeker,employer'],
            'terms' => ['required', 'accepted'],
        ]);

        if ($validator->fails()) {
            return back()
                ->withErrors($validator)
                ->withInput($request->except('password', 'password_confirmation'));
        }

        // Create the user
        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'email_verified_at' => null, // Will be verified via email
        ]);

        // Assign role based on user type
        if ($request->user_type === 'employer') {
            $role = Role::firstOrCreate(['name' => 'employer']);
            $user->assignRole($role);
            
            // Create employer profile
            EmployerProfile::create([
                'user_id' => $user->id,
                'company_name' => null, // Will be filled later
                'description' => null,
                'website' => null,
                'industry' => null,
                'company_size' => null,
                'location' => null,
            ]);
        } else {
            $role = Role::firstOrCreate(['name' => 'job_seeker']);
            $user->assignRole($role);
            
            // Create job seeker profile
            JobSeekerProfile::create([
                'user_id' => $user->id,
                'summary' => null,
                'phone_number' => null,
                'location' => null,
                'experience_level' => null,
                'desired_salary_min' => null,
                'desired_salary_max' => null,
                'availability' => 'available',
            ]);
        }

        // Fire the registered event
        event(new Registered($user));

        // Log the user in
        Auth::login($user);

        // Redirect to email verification notice
        return redirect()->route('verification.notice')
            ->with('message', 'Registration successful! Please check your email to verify your account.');
    }

    /**
     * Get a validator for an incoming registration request.
     */
    protected function validator(array $data): \Illuminate\Contracts\Validation\Validator
    {
        return Validator::make($data, [
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users'],
            'password' => ['required', 'string', 'min:8', 'confirmed'],
            'user_type' => ['required', 'in:job_seeker,employer'],
            'terms' => ['required', 'accepted'],
        ]);
    }

    /**
     * Create a new user instance after a valid registration.
     */
    protected function create(array $data): User
    {
        return User::create([
            'name' => $data['name'],
            'email' => $data['email'],
            'password' => Hash::make($data['password']),
        ]);
    }
}