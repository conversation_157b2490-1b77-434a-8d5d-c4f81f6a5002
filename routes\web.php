<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\JobController;
use App\Http\Controllers\CourseController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\Auth\LoginController;
use App\Http\Controllers\Auth\RegisterController;
use App\Http\Controllers\JobApplicationController;
use App\Http\Controllers\SavedJobController;
use App\Http\Controllers\ResumeBuilderController;
use App\Http\Controllers\EmployerController;
use App\Http\Controllers\AdminController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

// Public Routes
Route::get('/', function () {
    return view('home');
})->name('home');

// Career Advice
Route::get('/career-advice', function () {
    return view('career.advice');
})->name('career.advice');

// Pricing
Route::get('/pricing', function () {
    return view('pricing');
})->name('pricing');

// Talent Search
Route::get('/talent-search', function () {
    return view('talent.search');
})->name('talent.search');

// Employer Registration
Route::get('/employer/register', function () {
    return view('employer.register');
})->name('employer.register');

// Employer Resources
Route::get('/employer/resources', function () {
    return view('employer.resources');
})->name('employer.resources');

// Legal Pages
Route::get('/privacy', [HomeController::class, 'privacy'])->name('privacy');
Route::get('/terms', function () {
    return view('legal.terms');
})->name('terms');
Route::get('/contact', function () {
    return view('contact');
})->name('contact');

// Job Routes
Route::prefix('jobs')->name('jobs.')->group(function () {
    Route::get('/', [JobController::class, 'index'])->name('index');
    Route::get('/{job}', [JobController::class, 'show'])->name('show');
    Route::get('/search/suggestions', [JobController::class, 'searchSuggestions'])->name('search.suggestions');
});

// Course Routes
Route::prefix('courses')->name('courses.')->group(function () {
    Route::get('/', [CourseController::class, 'index'])->name('index');
    Route::get('/{course}', [CourseController::class, 'show'])->name('show');
    Route::get('/category/{category}', [CourseController::class, 'category'])->name('category');
});

// Company Routes
Route::prefix('companies')->name('companies.')->group(function () {
    Route::get('/', [EmployerController::class, 'companies'])->name('index');
    Route::get('/{company}', [EmployerController::class, 'showCompany'])->name('show');
    Route::get('/{company}/jobs', [EmployerController::class, 'companyJobs'])->name('jobs');
    Route::get('/{company}/reviews', [EmployerController::class, 'companyReviews'])->name('reviews');
});

// Authentication Routes
Route::middleware('guest')->group(function () {
    Route::get('/login', [LoginController::class, 'showLoginForm'])->name('login');
    Route::post('/login', [LoginController::class, 'login']);

    // Social Authentication Routes
    Route::get('/auth/google', [LoginController::class, 'redirectToGoogle'])->name('auth.google');
    Route::get('/auth/google/callback', [LoginController::class, 'handleGoogleCallback']);
    Route::get('/auth/linkedin', [LoginController::class, 'redirectToLinkedIn'])->name('auth.linkedin');
    Route::get('/auth/linkedin/callback', [LoginController::class, 'handleLinkedInCallback']);
    Route::get('/register', [RegisterController::class, 'showRegistrationForm'])->name('register');
    Route::post('/register', [RegisterController::class, 'register']);
    Route::get('/forgot-password', [LoginController::class, 'showForgotPasswordForm'])->name('password.request');
    Route::post('/forgot-password', [LoginController::class, 'sendResetLinkEmail'])->name('password.email');
    Route::get('/reset-password/{token}', [LoginController::class, 'showResetPasswordForm'])->name('password.reset');
    Route::post('/reset-password', [LoginController::class, 'resetPassword'])->name('password.update');
});

Route::post('/logout', [LoginController::class, 'logout'])->name('logout')->middleware('auth');

// Protected Routes
Route::middleware(['auth', 'verified'])->group(function () {
    // Dashboard
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

    // Profile Management
    Route::prefix('profile')->name('profile.')->group(function () {
        Route::get('/', [ProfileController::class, 'show'])->name('show');
        Route::get('/edit', [ProfileController::class, 'edit'])->name('edit');
        Route::put('/', [ProfileController::class, 'update'])->name('update');
        Route::delete('/', [ProfileController::class, 'destroy'])->name('destroy');
        Route::post('/avatar', [ProfileController::class, 'updateAvatar'])->name('avatar.update');
        Route::get('/export', [ProfileController::class, 'exportData'])->name('export');
        Route::get('/settings', [ProfileController::class, 'settings'])->name('settings');
        Route::put('/settings', [ProfileController::class, 'updateSettings'])->name('settings.update');
    });

    // Job Seeker Routes
    Route::middleware('role:job_seeker')->group(function () {
        // Job Applications
        Route::prefix('applications')->name('applications.')->group(function () {
            Route::get('/', [JobApplicationController::class, 'index'])->name('index');
            Route::get('/{application}', [JobApplicationController::class, 'show'])->name('show');
            Route::post('/jobs/{job}', [JobApplicationController::class, 'store'])->name('store');
            Route::put('/{application}', [JobApplicationController::class, 'update'])->name('update');
            Route::delete('/{application}', [JobApplicationController::class, 'destroy'])->name('destroy');
        });

        // Saved Jobs
        Route::prefix('saved-jobs')->name('saved-jobs.')->group(function () {
            Route::get('/', [SavedJobController::class, 'index'])->name('index');
            Route::post('/jobs/{job}', [SavedJobController::class, 'store'])->name('store');
            Route::delete('/jobs/{job}', [SavedJobController::class, 'destroy'])->name('destroy');
        });

        // Resume Builder
        Route::prefix('resume')->name('resume.')->group(function () {
            Route::get('/', [ResumeBuilderController::class, 'index'])->name('index');
            Route::get('/builder', [ResumeBuilderController::class, 'builder'])->name('builder');
            Route::get('/create', [ResumeBuilderController::class, 'create'])->name('create');
            Route::post('/', [ResumeBuilderController::class, 'store'])->name('store');
            Route::get('/{resume}', [ResumeBuilderController::class, 'show'])->name('show');
            Route::get('/{resume}/edit', [ResumeBuilderController::class, 'edit'])->name('edit');
            Route::put('/{resume}', [ResumeBuilderController::class, 'update'])->name('update');
            Route::delete('/{resume}', [ResumeBuilderController::class, 'destroy'])->name('destroy');
            Route::get('/{resume}/download', [ResumeBuilderController::class, 'download'])->name('download');
        });

        // Course Enrollments
        Route::prefix('my-learning')->name('courses.')->group(function () {
            Route::get('/', [CourseController::class, 'myLearning'])->name('my-learning');
            Route::get('/courses/{course}/enroll', [CourseController::class, 'showEnrollment'])->name('enroll.show');
            Route::post('/courses/{course}/enroll', [CourseController::class, 'enroll'])->name('enroll');
            Route::get('/courses/{course}/learn', [CourseController::class, 'learn'])->name('learn');
            Route::post('/courses/{course}/lessons/{lesson}/complete', [CourseController::class, 'completeLesson'])->name('lesson.complete');
            Route::get('/certificates', [CourseController::class, 'certificates'])->name('certificates');
            Route::get('/certificates/{enrollment}/download', [CourseController::class, 'downloadCertificate'])->name('certificate.download');
        });
    });

    // Employer Routes
    Route::middleware('role:employer')->prefix('employer')->name('employer.')->group(function () {
        // Employer Dashboard
        Route::get('/dashboard', [EmployerController::class, 'dashboard'])->name('dashboard');

        // Job Management
        Route::prefix('jobs')->name('jobs.')->group(function () {
            Route::get('/', [EmployerController::class, 'jobs'])->name('index');
            Route::get('/create', [EmployerController::class, 'createJob'])->name('create');
            Route::post('/', [EmployerController::class, 'storeJob'])->name('store');
            Route::get('/{job}', [EmployerController::class, 'showJob'])->name('show');
            Route::get('/{job}/edit', [EmployerController::class, 'editJob'])->name('edit');
            Route::put('/{job}', [EmployerController::class, 'updateJob'])->name('update');
            Route::delete('/{job}', [EmployerController::class, 'destroyJob'])->name('destroy');
            Route::post('/{job}/toggle-status', [EmployerController::class, 'toggleJobStatus'])->name('toggle-status');
            Route::post('/{job}/feature', [EmployerController::class, 'featureJob'])->name('feature');
        });

        // Application Management
        Route::prefix('applications')->name('applications.')->group(function () {
            Route::get('/', [EmployerController::class, 'applications'])->name('index');
            Route::get('/{application}', [EmployerController::class, 'showApplication'])->name('show');
            Route::put('/{application}/status', [EmployerController::class, 'updateApplicationStatus'])->name('update-status');
            Route::post('/{application}/notes', [EmployerController::class, 'addApplicationNote'])->name('add-note');
            Route::get('/jobs/{job}', [EmployerController::class, 'jobApplications'])->name('job');
        });

        // Company Profile
        Route::prefix('profile')->name('profile.')->group(function () {
            Route::get('/', [EmployerController::class, 'profile'])->name('show');
            Route::get('/edit', [EmployerController::class, 'editProfile'])->name('edit');
            Route::put('/', [EmployerController::class, 'updateProfile'])->name('update');
            Route::post('/logo', [EmployerController::class, 'updateLogo'])->name('logo.update');
        });

        // Subscription Management
        Route::prefix('subscription')->name('subscription.')->group(function () {
            Route::get('/', [EmployerController::class, 'subscription'])->name('index');
            Route::get('/plans', [EmployerController::class, 'subscriptionPlans'])->name('plans');
            Route::post('/subscribe/{plan}', [EmployerController::class, 'subscribe'])->name('subscribe');
            Route::post('/cancel', [EmployerController::class, 'cancelSubscription'])->name('cancel');
            Route::get('/billing', [EmployerController::class, 'billing'])->name('billing');
        });
    });

    // Admin Routes
    Route::middleware('role:admin')->prefix('admin')->name('admin.')->group(function () {
        // Admin Dashboard
        Route::get('/dashboard', [AdminController::class, 'dashboard'])->name('dashboard');

        // User Management
        Route::prefix('users')->name('users.')->group(function () {
            Route::get('/', [AdminController::class, 'users'])->name('index');
            Route::post('/', [AdminController::class, 'storeUser'])->name('store');
            Route::get('/{user}', [AdminController::class, 'showUser'])->name('show');
            Route::get('/{user}/edit', [AdminController::class, 'editUser'])->name('edit');
            Route::put('/{user}', [AdminController::class, 'updateUser'])->name('update');
            Route::delete('/{user}', [AdminController::class, 'destroyUser'])->name('destroy');
            Route::post('/{user}/suspend', [AdminController::class, 'suspendUser'])->name('suspend');
            Route::post('/{user}/activate', [AdminController::class, 'activateUser'])->name('activate');
            Route::post('/{user}/message', [AdminController::class, 'sendMessageToUser'])->name('message');
            Route::post('/bulk-export', [AdminController::class, 'bulkExportUsers'])->name('bulk-export');
            Route::post('/bulk-action', [AdminController::class, 'bulkActionUsers'])->name('bulk-action');
        });

        // Job Management
        Route::prefix('jobs')->name('jobs.')->group(function () {
            Route::get('/', [AdminController::class, 'jobs'])->name('index');
            Route::get('/{job}', [AdminController::class, 'showJob'])->name('show');
            Route::put('/{job}', [AdminController::class, 'updateJob'])->name('update');
            Route::delete('/{job}', [AdminController::class, 'destroyJob'])->name('destroy');
            Route::post('/{job}/approve', [AdminController::class, 'approveJob'])->name('approve');
            Route::post('/{job}/reject', [AdminController::class, 'rejectJob'])->name('reject');
            Route::post('/{job}/suspend', [AdminController::class, 'suspendJob'])->name('suspend');
            Route::post('/{job}/activate', [AdminController::class, 'activateJob'])->name('activate');
            Route::post('/{job}/feature', [AdminController::class, 'featureJob'])->name('feature');
        });

        // Application Management
        Route::prefix('applications')->name('applications.')->group(function () {
            Route::get('/', [AdminController::class, 'applications'])->name('index');
            Route::get('/{application}', [AdminController::class, 'showApplication'])->name('show');
            Route::put('/{application}', [AdminController::class, 'updateApplication'])->name('update');
            Route::delete('/{application}', [AdminController::class, 'destroyApplication'])->name('destroy');
            Route::post('/{application}/approve', [AdminController::class, 'approveApplication'])->name('approve');
            Route::post('/{application}/reject', [AdminController::class, 'rejectApplication'])->name('reject');
        });

        // Course Management
        Route::prefix('courses')->name('courses.')->group(function () {
            Route::get('/', [AdminController::class, 'courses'])->name('index');
            Route::get('/create', [AdminController::class, 'createCourse'])->name('create');
            Route::post('/', [AdminController::class, 'storeCourse'])->name('store');
            Route::get('/{course}', [AdminController::class, 'showCourse'])->name('show');
            Route::get('/{course}/edit', [AdminController::class, 'editCourse'])->name('edit');
            Route::put('/{course}', [AdminController::class, 'updateCourse'])->name('update');
            Route::delete('/{course}', [AdminController::class, 'destroyCourse'])->name('destroy');
            Route::post('/{course}/publish', [AdminController::class, 'publishCourse'])->name('publish');
            Route::post('/{course}/unpublish', [AdminController::class, 'unpublishCourse'])->name('unpublish');
        });

        // Lesson Management
        Route::prefix('lessons')->name('lessons.')->group(function () {
            Route::get('/courses/{course}', [AdminController::class, 'courseLessons'])->name('course');
            Route::get('/create/{course}', [AdminController::class, 'createLesson'])->name('create');
            Route::post('/courses/{course}', [AdminController::class, 'storeLesson'])->name('store');
            Route::get('/{lesson}', [AdminController::class, 'showLesson'])->name('show');
            Route::get('/{lesson}/edit', [AdminController::class, 'editLesson'])->name('edit');
            Route::put('/{lesson}', [AdminController::class, 'updateLesson'])->name('update');
            Route::delete('/{lesson}', [AdminController::class, 'destroyLesson'])->name('destroy');
            Route::post('/{lesson}/reorder', [AdminController::class, 'reorderLesson'])->name('reorder');
        });

        // Subscription Plan Management
        Route::prefix('subscription-plans')->name('subscription-plans.')->group(function () {
            Route::get('/', [AdminController::class, 'subscriptionPlans'])->name('index');
            Route::get('/create', [AdminController::class, 'createSubscriptionPlan'])->name('create');
            Route::post('/', [AdminController::class, 'storeSubscriptionPlan'])->name('store');
            Route::get('/export', [AdminController::class, 'exportSubscriptionPlans'])->name('export');
            Route::get('/{plan}', [AdminController::class, 'showSubscriptionPlan'])->name('show');
            Route::get('/{plan}/edit', [AdminController::class, 'editSubscriptionPlan'])->name('edit');
            Route::put('/{plan}', [AdminController::class, 'updateSubscriptionPlan'])->name('update');
            Route::delete('/{plan}', [AdminController::class, 'destroySubscriptionPlan'])->name('destroy');
            Route::post('/{plan}/activate', [AdminController::class, 'activateSubscriptionPlan'])->name('activate');
            Route::post('/{plan}/deactivate', [AdminController::class, 'deactivateSubscriptionPlan'])->name('deactivate');
            Route::post('/{plan}/duplicate', [AdminController::class, 'duplicateSubscriptionPlan'])->name('duplicate');
            Route::get('/{plan}/subscribers', [AdminController::class, 'getSubscriptionPlanSubscribers'])->name('subscribers');
            Route::post('/{plan}/toggle-status', [AdminController::class, 'toggleSubscriptionPlanStatus'])->name('toggle-status');
        });

        // Analytics & Reports
        Route::prefix('analytics')->name('analytics.')->group(function () {
            Route::get('/', [AdminController::class, 'analytics'])->name('index');
            Route::get('/users', [AdminController::class, 'userAnalytics'])->name('users');
            Route::get('/jobs', [AdminController::class, 'jobAnalytics'])->name('jobs');
            Route::get('/courses', [AdminController::class, 'courseAnalytics'])->name('courses');
            Route::get('/revenue', [AdminController::class, 'revenueAnalytics'])->name('revenue');
            Route::get('/export/{type}', [AdminController::class, 'exportAnalytics'])->name('export');
        });

        // System Settings
        Route::prefix('settings')->name('settings.')->group(function () {
            Route::get('/', [AdminController::class, 'settings'])->name('index');
            Route::put('/', [AdminController::class, 'updateSettings'])->name('update');
            Route::put('/general', [AdminController::class, 'updateGeneralSettings'])->name('general.update');
            Route::put('/email', [AdminController::class, 'updateEmailSettings'])->name('email.update');
            Route::put('/payment', [AdminController::class, 'updatePaymentSettings'])->name('payment.update');
            Route::put('/seo', [AdminController::class, 'updateSeoSettings'])->name('seo.update');
        });
    });
});

// Email Verification Routes
Route::middleware('auth')->group(function () {
    Route::get('/email/verify', [LoginController::class, 'showVerifyEmailForm'])->name('verification.notice');
    Route::get('/email/verify/{id}/{hash}', [LoginController::class, 'verifyEmail'])->name('verification.verify');
    Route::post('/email/verification-notification', [LoginController::class, 'sendVerificationEmail'])->name('verification.send');
});

// API Documentation (if using Swagger/OpenAPI)
Route::get('/api/documentation', function () {
    return view('api.documentation');
})->name('api.documentation');

// Health Check
Route::get('/health', function () {
    return response()->json([
        'status' => 'ok',
        'timestamp' => now()->toISOString(),
        'environment' => app()->environment(),
    ]);
})->name('health');

// Sitemap
Route::get('/sitemap.xml', function () {
    return response()->view('sitemap')
        ->header('Content-Type', 'text/xml');
})->name('sitemap');

// Robots.txt
Route::get('/robots.txt', function () {
    return response()->view('robots')
        ->header('Content-Type', 'text/plain');
})->name('robots');

// Fallback route for SPA-like behavior (optional)
Route::fallback(function () {
    return view('errors.404');
});
