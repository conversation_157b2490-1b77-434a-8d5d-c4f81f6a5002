<?php

namespace Database\Seeders;

use App\Models\CourseEnrollment;
use App\Models\Course;
use App\Models\User;
use Illuminate\Database\Seeder;
use Carbon\Carbon;

class CourseEnrollmentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $courses = Course::where('is_published', true)->get();
        $users = User::where('role', 'job_seeker')->get();
        
        if ($courses->isEmpty() || $users->isEmpty()) {
            $this->command->warn('No published courses or job seeker users found. Please run CourseSeeder and UserSeeder first.');
            return;
        }

        foreach ($users as $user) {
            // Each user enrolls in 0-5 courses
            $enrollmentCount = fake()->numberBetween(0, 5);
            $enrolledCourses = $courses->random(min($enrollmentCount, $courses->count()));
            
            foreach ($enrolledCourses as $course) {
                // Check if user is already enrolled
                $existingEnrollment = CourseEnrollment::where('user_id', $user->id)
                    ->where('course_id', $course->id)
                    ->first();
                    
                if ($existingEnrollment) {
                    continue;
                }

                $enrolledAt = fake()->dateTimeBetween('-6 months', 'now');
                $progress = fake()->numberBetween(0, 100);
                $isCompleted = $progress === 100;
                $completedAt = $isCompleted ? fake()->dateTimeBetween($enrolledAt, 'now') : null;
                
                // Determine payment status based on course type
                $paymentStatus = $course->is_premium ? 
                    fake()->randomElement(['pending', 'completed', 'failed']) : 
                    'completed'; // Free courses are automatically completed
                
                // If payment failed, user shouldn't have much progress
                if ($paymentStatus === 'failed') {
                    $progress = 0;
                    $isCompleted = false;
                    $completedAt = null;
                }
                
                // Calculate last accessed date
                $lastAccessedAt = null;
                if ($progress > 0) {
                    $lastAccessedAt = fake()->dateTimeBetween($enrolledAt, 'now');
                }

                CourseEnrollment::create([
                    'user_id' => $user->id,
                    'course_id' => $course->id,
                    'progress' => $progress,
                    'completed_at' => $completedAt,
                    'certificate_issued_at' => $isCompleted && fake()->boolean(80) ? $completedAt : null,
                    'last_accessed_at' => $lastAccessedAt,
                    'created_at' => $enrolledAt,
                    'updated_at' => $lastAccessedAt ?: $enrolledAt,
                ]);
            }
        }

        // Update course enrollment counts
        foreach ($courses as $course) {
            $enrollmentCount = CourseEnrollment::where('course_id', $course->id)
                ->count();
                
            $course->update(['enrollments_count' => $enrollmentCount]);
        }

        $this->command->info('Course enrollments created successfully!');
    }
}