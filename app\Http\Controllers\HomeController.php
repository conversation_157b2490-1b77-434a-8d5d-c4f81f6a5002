<?php

namespace App\Http\Controllers;

use App\Models\Job;
use App\Models\Course;
use App\Models\User;
use App\Models\JobApplication;
use App\Models\CourseEnrollment;
use App\Models\SubscriptionPlan;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class HomeController extends Controller
{
    /**
     * Display the homepage.
     */
    public function index()
    {
        // Cache homepage data for better performance
        $data = Cache::remember('homepage_data', 3600, function () {
            return [
                'featured_jobs' => $this->getFeaturedJobs(),
                'popular_courses' => $this->getPopularCourses(),
                'job_categories' => $this->getJobCategories(),
                'course_categories' => $this->getCourseCategories(),
                'platform_stats' => $this->getPlatformStats(),
                'testimonials' => $this->getTestimonials(),
                'recent_companies' => $this->getRecentCompanies(),
            ];
        });

        // SEO Optimization for homepage
        \App\Helpers\SeoHelper::setTitle('Rectra - Job Board & Professional Training Platform')
            ->setDescription('Find your dream job and advance your career with Rectra. Browse thousands of job opportunities and professional training courses.')
            ->setKeywords(['jobs', 'careers', 'training', 'courses', 'employment', 'job board', 'professional development', 'hiring'])
            ->setUrl(url('/'))
            ->addStructuredData(\App\Helpers\SeoHelper::generateOrganizationStructuredData())
            ->addStructuredData(\App\Helpers\SeoHelper::generateWebsiteStructuredData());

        return view('home', $data);
    }

    /**
     * Display the about page.
     */
    public function about()
    {
        $stats = $this->getPlatformStats();
        $teamMembers = $this->getTeamMembers();
        $milestones = $this->getMilestones();

        return view('about', compact('stats', 'teamMembers', 'milestones'));
    }

    /**
     * Display the contact page.
     */
    public function contact()
    {
        return view('contact');
    }

    /**
     * Handle contact form submission.
     */
    public function submitContact(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'subject' => 'required|string|max:255',
            'message' => 'required|string|max:2000',
            'g-recaptcha-response' => 'nullable|string', // For reCAPTCHA if implemented
        ]);

        // Store contact message in database or send email
        // For now, we'll just return success
        // In a real application, you would:
        // 1. Save to a contacts table
        // 2. Send email notification to admin
        // 3. Send confirmation email to user

        return back()->with('success', 'Thank you for your message! We will get back to you soon.');
    }

    /**
     * Display the privacy policy page.
     */
    public function privacy()
    {
        return view('legal.privacy');
    }

    /**
     * Display the terms of service page.
     */
    public function terms()
    {
        return view('legal.terms');
    }

    /**
     * Display the FAQ page.
     */
    public function faq()
    {
        $faqs = $this->getFaqs();
        return view('faq', compact('faqs'));
    }

    /**
     * Display the pricing page.
     */
    public function pricing()
    {
        $plans = SubscriptionPlan::active()
            ->ordered()
            ->get();

        $features = $this->getPricingFeatures();

        return view('pricing', compact('plans', 'features'));
    }

    /**
     * Display the blog/news page.
     */
    public function blog(Request $request)
    {
        // This would typically fetch from a blog/news model
        // For now, we'll return a placeholder view
        $posts = collect([]); // Placeholder for blog posts
        $categories = collect([]); // Placeholder for blog categories

        return view('blog.index', compact('posts', 'categories'));
    }

    /**
     * Display a specific blog post.
     */
    public function blogPost($slug)
    {
        // This would typically fetch a specific blog post
        // For now, we'll return a placeholder view
        return view('blog.show', ['post' => null]);
    }

    /**
     * Search across the platform.
     */
    public function search(Request $request)
    {
        $request->validate([
            'q' => 'required|string|max:255',
            'type' => 'nullable|in:jobs,courses,all',
        ]);

        $query = $request->get('q');
        $type = $request->get('type', 'all');
        $results = [];

        if ($type === 'jobs' || $type === 'all') {
            $results['jobs'] = Job::where('status', 'active')
                ->where(function ($q) use ($query) {
                    $q->where('title', 'like', "%{$query}%")
                      ->orWhere('description', 'like', "%{$query}%")
                      ->orWhere('requirements', 'like', "%{$query}%")
                      ->orWhere('location', 'like', "%{$query}%")
                      ->orWhereHas('employerProfile', function ($eq) use ($query) {
                          $eq->where('company_name', 'like', "%{$query}%");
                      });
                })
                ->with(['employerProfile'])
                ->limit(10)
                ->get();
        }

        if ($type === 'courses' || $type === 'all') {
            $results['courses'] = Course::where('is_published', true)
                ->where(function ($q) use ($query) {
                    $q->where('title', 'like', "%{$query}%")
                      ->orWhere('description', 'like', "%{$query}%")
                      ->orWhere('category', 'like', "%{$query}%")
                      ->orWhereHas('instructor', function ($iq) use ($query) {
                          $iq->where('name', 'like', "%{$query}%");
                      });
                })
                ->with(['instructor'])
                ->limit(10)
                ->get();
        }

        return view('search', compact('query', 'type', 'results'));
    }

    /**
     * Get search suggestions for autocomplete.
     */
    public function searchSuggestions(Request $request)
    {
        $query = $request->get('q');
        $type = $request->get('type', 'all');
        $suggestions = [];

        if (strlen($query) >= 2) {
            if ($type === 'jobs' || $type === 'all') {
                $jobSuggestions = Job::where('status', 'active')
                    ->where('title', 'like', "%{$query}%")
                    ->select('title')
                    ->distinct()
                    ->limit(5)
                    ->pluck('title')
                    ->map(function ($title) {
                        return ['text' => $title, 'type' => 'job'];
                    });

                $suggestions = array_merge($suggestions, $jobSuggestions->toArray());
            }

            if ($type === 'courses' || $type === 'all') {
                $courseSuggestions = Course::where('is_published', true)
                    ->where('title', 'like', "%{$query}%")
                    ->select('title')
                    ->distinct()
                    ->limit(5)
                    ->pluck('title')
                    ->map(function ($title) {
                        return ['text' => $title, 'type' => 'course'];
                    });

                $suggestions = array_merge($suggestions, $courseSuggestions->toArray());
            }
        }

        return response()->json(array_slice($suggestions, 0, 10));
    }

    /**
     * Get featured jobs for homepage.
     */
    private function getFeaturedJobs()
    {
        return Job::where('status', 'active')
            ->where('is_featured', true)
            ->where('expires_at', '>', now())
            ->with(['employerProfile'])
            ->orderBy('created_at', 'desc')
            ->limit(6)
            ->get();
    }

    /**
     * Get popular courses for homepage.
     */
    private function getPopularCourses()
    {
        return Course::where('is_published', true)
            ->withCount('enrollments')
            ->with(['instructor'])
            ->orderBy('enrollments_count', 'desc')
            ->orderBy('rating', 'desc')
            ->limit(6)
            ->get();
    }

    /**
     * Get job categories with counts.
     */
    private function getJobCategories()
    {
        return Job::where('status', 'active')
            ->where('expires_at', '>', now())
            ->selectRaw('industry as category, COUNT(*) as count')
            ->whereNotNull('industry')
            ->groupBy('industry')
            ->orderBy('count', 'desc')
            ->limit(8)
            ->get();
    }

    /**
     * Get course categories with counts.
     */
    private function getCourseCategories()
    {
        return Course::where('is_published', true)
            ->selectRaw('category, COUNT(*) as count')
            ->whereNotNull('category')
            ->groupBy('category')
            ->orderBy('count', 'desc')
            ->limit(8)
            ->get();
    }

    /**
     * Get platform statistics.
     */
    private function getPlatformStats()
    {
        return [
            'total_jobs' => Job::where('status', 'active')
                ->where('expires_at', '>', now())
                ->count(),
            'total_companies' => User::where('role', 'employer')
                ->whereHas('employerProfile')
                ->count(),
            'total_job_seekers' => User::where('role', 'job_seeker')
                ->whereHas('jobSeekerProfile')
                ->count(),
            'total_courses' => Course::where('is_published', true)->count(),
            'total_students' => CourseEnrollment::distinct('user_id')->count('user_id'),
            'successful_placements' => JobApplication::where('status', 'hired')->count(),
        ];
    }

    /**
     * Get testimonials for homepage.
     */
    private function getTestimonials()
    {
        // This would typically come from a testimonials table
        // For now, we'll return sample data
        return [
            [
                'name' => 'Sarah Johnson',
                'role' => 'Software Developer',
                'company' => 'TechCorp',
                'content' => 'Rectra helped me find my dream job in just 2 weeks. The platform is intuitive and the job matching is excellent.',
                'rating' => 5,
                'avatar' => null,
            ],
            [
                'name' => 'Michael Chen',
                'role' => 'HR Manager',
                'company' => 'InnovateLab',
                'content' => 'As an employer, Rectra has been invaluable for finding qualified candidates. The screening tools save us so much time.',
                'rating' => 5,
                'avatar' => null,
            ],
            [
                'name' => 'Emily Rodriguez',
                'role' => 'Marketing Specialist',
                'company' => 'Creative Agency',
                'content' => 'The courses on Rectra helped me upskill and land a promotion. The quality of content is outstanding.',
                'rating' => 5,
                'avatar' => null,
            ],
        ];
    }

    /**
     * Get recent companies that joined.
     */
    private function getRecentCompanies()
    {
        return User::where('role', 'employer')
            ->whereHas('employerProfile')
            ->with('employerProfile')
            ->latest()
            ->limit(12)
            ->get()
            ->map(function ($user) {
                return [
                    'name' => $user->employerProfile->company_name,
                    'logo' => $user->employerProfile->logo,
                    'industry' => $user->employerProfile->industry,
                ];
            });
    }

    /**
     * Get team members for about page.
     */
    private function getTeamMembers()
    {
        // This would typically come from a team_members table
        // For now, we'll return sample data
        return [
            [
                'name' => 'John Smith',
                'role' => 'CEO & Founder',
                'bio' => 'Passionate about connecting talent with opportunities.',
                'image' => null,
                'linkedin' => '#',
                'twitter' => '#',
            ],
            [
                'name' => 'Jane Doe',
                'role' => 'CTO',
                'bio' => 'Leading our technical vision and platform development.',
                'image' => null,
                'linkedin' => '#',
                'twitter' => '#',
            ],
            [
                'name' => 'Alex Wilson',
                'role' => 'Head of Product',
                'bio' => 'Focused on creating the best user experience.',
                'image' => null,
                'linkedin' => '#',
                'twitter' => '#',
            ],
        ];
    }

    /**
     * Get company milestones for about page.
     */
    private function getMilestones()
    {
        return [
            [
                'year' => '2020',
                'title' => 'Company Founded',
                'description' => 'Rectra was founded with a vision to revolutionize job searching and learning.',
            ],
            [
                'year' => '2021',
                'title' => '10,000 Users',
                'description' => 'Reached our first major milestone of 10,000 registered users.',
            ],
            [
                'year' => '2022',
                'title' => 'Course Platform Launch',
                'description' => 'Launched our comprehensive learning platform with professional courses.',
            ],
            [
                'year' => '2023',
                'title' => '100,000 Job Matches',
                'description' => 'Successfully facilitated over 100,000 job matches between employers and candidates.',
            ],
        ];
    }

    /**
     * Get FAQs.
     */
    private function getFaqs()
    {
        return [
            'general' => [
                [
                    'question' => 'What is Rectra?',
                    'answer' => 'Rectra is a comprehensive platform that combines job searching with professional learning. We help job seekers find opportunities while providing courses to enhance their skills.',
                ],
                [
                    'question' => 'Is Rectra free to use?',
                    'answer' => 'Yes, basic job searching and course browsing is free. We also offer premium plans with additional features for both job seekers and employers.',
                ],
                [
                    'question' => 'How do I create an account?',
                    'answer' => 'Click the "Sign Up" button and choose whether you\'re a job seeker or employer. Fill in your details and verify your email to get started.',
                ],
            ],
            'job_seekers' => [
                [
                    'question' => 'How do I apply for jobs?',
                    'answer' => 'Browse jobs, click on ones that interest you, and click "Apply Now". You can upload your resume and write a cover letter for each application.',
                ],
                [
                    'question' => 'Can I track my applications?',
                    'answer' => 'Yes, your dashboard shows all your applications and their current status. You\'ll receive notifications when employers update your application status.',
                ],
                [
                    'question' => 'How do course certificates work?',
                    'answer' => 'Complete all lessons in a course to earn a certificate. Certificates can be downloaded and shared on your profile or LinkedIn.',
                ],
            ],
            'employers' => [
                [
                    'question' => 'How do I post a job?',
                    'answer' => 'After creating an employer account, go to your dashboard and click "Post New Job". Fill in the job details and publish when ready.',
                ],
                [
                    'question' => 'How do I manage applications?',
                    'answer' => 'View all applications in your employer dashboard. You can filter, sort, and update application statuses to manage your hiring process.',
                ],
                [
                    'question' => 'What are the pricing plans?',
                    'answer' => 'We offer various plans based on your hiring needs. Check our pricing page for detailed information about features and costs.',
                ],
            ],
        ];
    }

    /**
     * Get pricing features comparison.
     */
    private function getPricingFeatures()
    {
        return [
            'job_seeker' => [
                'free' => [
                    'Job search and applications',
                    'Basic profile',
                    'Course browsing',
                    'Email notifications',
                ],
                'premium' => [
                    'Everything in Free',
                    'Advanced profile features',
                    'Unlimited course access',
                    'Priority application status',
                    'Resume builder',
                    'Career coaching sessions',
                ],
            ],
            'employer' => [
                'basic' => [
                    'Post up to 5 jobs',
                    'Basic applicant management',
                    'Company profile',
                    'Email support',
                ],
                'professional' => [
                    'Everything in Basic',
                    'Unlimited job postings',
                    'Advanced filtering tools',
                    'Analytics and reporting',
                    'Priority support',
                ],
                'enterprise' => [
                    'Everything in Professional',
                    'Custom branding',
                    'API access',
                    'Dedicated account manager',
                    'Custom integrations',
                ],
            ],
        ];
    }
}