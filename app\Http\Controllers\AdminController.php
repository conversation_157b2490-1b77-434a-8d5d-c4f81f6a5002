<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Job;
use App\Models\Course;
use App\Models\JobApplication;
use App\Models\CourseEnrollment;
use App\Models\SubscriptionPlan;
use App\Models\Subscription;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Carbon\Carbon;

class AdminController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('role:admin');
    }

    /**
     * Display the admin dashboard.
     */
    public function dashboard()
    {
        // Get overall statistics
        $stats = $this->getDashboardStats();

        // Get recent activities
        $recentUsers = User::with(['jobSeekerProfile', 'employerProfile'])
            ->latest()
            ->limit(10)
            ->get();

        $recentJobs = Job::with(['employerProfile.user'])
            ->latest()
            ->limit(10)
            ->get();

        $recentApplications = JobApplication::with(['user', 'job'])
            ->latest()
            ->limit(10)
            ->get();

        // Get analytics data for charts
        $analyticsData = $this->getAnalyticsData();

        return view('admin.dashboard', compact(
            'stats',
            'recentUsers',
            'recentJobs',
            'recentApplications',
            'analyticsData'
        ));
    }

    /**
     * Display user management.
     */
    public function users(Request $request)
    {
        $query = User::with(['jobSeekerProfile', 'employerProfile', 'subscription']);

        // Filter by role
        if ($request->filled('role')) {
            $query->where('role', $request->role);
        }

        // Filter by status
        if ($request->filled('status')) {
            if ($request->status === 'active') {
                $query->whereNotNull('email_verified_at');
            } elseif ($request->status === 'unverified') {
                $query->whereNull('email_verified_at');
            }
            // Note: Suspension functionality temporarily disabled until migration is run
        }

        // Filter by registration date
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        // Search by name or email
        if ($request->filled('search')) {
            $searchTerm = $request->search;
            $query->where(function ($q) use ($searchTerm) {
                $q->where('name', 'like', "%{$searchTerm}%")
                  ->orWhere('email', 'like', "%{$searchTerm}%");
            });
        }

        // Sort options
        $sortBy = $request->get('sort', 'created_at');
        $sortOrder = $request->get('order', 'desc');

        switch ($sortBy) {
            case 'name':
                $query->orderBy('name', $sortOrder);
                break;
            case 'email':
                $query->orderBy('email', $sortOrder);
                break;
            case 'role':
                $query->orderBy('role', $sortOrder);
                break;
            case 'last_login':
                $query->orderBy('last_login_at', $sortOrder);
                break;
            default: // created_at
                $query->orderBy('created_at', $sortOrder);
        }

        $users = $query->paginate(20)->withQueryString();

        // Get filter options
        $roles = ['job_seeker', 'employer', 'admin'];
        $statuses = ['active', 'suspended', 'unverified'];

        // Get counts for status filter
        $statusCounts = [
            'all' => User::count(),
            'active' => User::whereNotNull('email_verified_at')->count(),
            'unverified' => User::whereNull('email_verified_at')->count(),
        ];

        return view('admin.users.index', compact(
            'users',
            'roles',
            'statuses',
            'statusCounts'
        ));
    }

    /**
     * Display a specific user.
     */
    public function showUser(User $user)
    {
        $user->load([
            'jobSeekerProfile',
            'employerProfile',
            'subscription.plan',
            'jobApplications.job',
            'courseEnrollments.course'
        ]);

        // Get user statistics
        $userStats = $this->getUserStats($user);

        return view('admin.users.show', compact('user', 'userStats'));
    }

    /**
     * Suspend a user.
     */
    public function suspendUser(Request $request, User $user)
    {
        // Temporarily disable suspension functionality until migration is run
        // For now, we'll remove email verification as a proxy for suspension
        $user->update([
            'email_verified_at' => null,
        ]);

        return back()->with('success', 'User access suspended successfully (email verification removed).');
    }

    /**
     * Unsuspend a user.
     */
    public function unsuspendUser(User $user)
    {
        // Restore email verification as a proxy for unsuspension
        $user->update([
            'email_verified_at' => now(),
        ]);

        return back()->with('success', 'User access restored successfully (email verification restored).');
    }

    /**
     * Activate a user (alias for unsuspend).
     */
    public function activateUser(User $user)
    {
        return $this->unsuspendUser($user);
    }

    /**
     * Delete a user.
     */
    public function deleteUser(User $user)
    {
        if ($user->role === 'admin') {
            return back()->with('error', 'Cannot delete admin users.');
        }

        $user->delete();

        return back()->with('success', 'User deleted successfully.');
    }

    /**
     * Store a new user.
     */
    public function storeUser(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'role' => 'required|string|in:job_seeker,employer,admin',
            'password' => 'required|string|min:8',
        ]);

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'role' => $request->role,
            'password' => Hash::make($request->password),
            'email_verified_at' => now(),
        ]);

        return back()->with('success', 'User created successfully.');
    }

    /**
     * Bulk export users.
     */
    public function bulkExportUsers(Request $request)
    {
        $userIds = $request->user_ids;
        $users = User::whereIn('id', $userIds)->get();

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="selected_users_' . now()->format('Y-m-d') . '.csv"',
        ];

        $callback = function () use ($users) {
            $file = fopen('php://output', 'w');
            fputcsv($file, ['Name', 'Email', 'Role', 'Registration Date', 'Email Verified', 'Last Login']);

            foreach ($users as $user) {
                fputcsv($file, [
                    $user->name,
                    $user->email,
                    $user->role,
                    $user->created_at->format('Y-m-d H:i:s'),
                    $user->email_verified_at ? 'Yes' : 'No',
                    $user->last_login_at ? $user->last_login_at->format('Y-m-d H:i:s') : 'Never',
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Bulk action on users.
     */
    public function bulkActionUsers(Request $request)
    {
        $request->validate([
            'action' => 'required|string|in:activate,suspend',
            'user_ids' => 'required|array',
            'user_ids.*' => 'exists:users,id',
        ]);

        $userIds = $request->user_ids;
        $action = $request->action;

        switch ($action) {
            case 'activate':
                User::whereIn('id', $userIds)->update(['email_verified_at' => now()]);
                $message = 'Users activated successfully.';
                break;
            case 'suspend':
                User::whereIn('id', $userIds)->update(['email_verified_at' => null]);
                $message = 'Users suspended successfully.';
                break;
        }

        return back()->with('success', $message);
    }

    /**
     * Send message to user.
     */
    public function sendMessageToUser(Request $request, User $user)
    {
        $request->validate([
            'subject' => 'required|string|max:255',
            'message' => 'required|string',
        ]);

        // Here you would typically send an email or create a notification
        // For now, we'll just return success

        return back()->with('success', 'Message sent successfully to ' . $user->name);
    }

    /**
     * Display job management.
     */
    public function jobs(Request $request)
    {
        $query = Job::with(['employerProfile.user', 'media'])
            ->withCount('applications');

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by job type
        if ($request->filled('type')) {
            $query->where('job_type', $request->type);
        }

        // Filter by industry
        if ($request->filled('industry')) {
            $query->where('industry', $request->industry);
        }

        // Filter by creation date
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        // Search by title or company
        if ($request->filled('search')) {
            $searchTerm = $request->search;
            $query->where(function ($q) use ($searchTerm) {
                $q->where('title', 'like', "%{$searchTerm}%")
                  ->orWhere('description', 'like', "%{$searchTerm}%")
                  ->orWhereHas('employerProfile', function ($eq) use ($searchTerm) {
                      $eq->where('company_name', 'like', "%{$searchTerm}%");
                  });
            });
        }

        // Sort options
        $sortBy = $request->get('sort', 'created_at');
        $sortOrder = $request->get('order', 'desc');

        switch ($sortBy) {
            case 'title':
                $query->orderBy('title', $sortOrder);
                break;
            case 'company':
                $query->join('employer_profiles', 'job_listings.employer_profile_id', '=', 'employer_profiles.id')
                      ->orderBy('employer_profiles.company_name', $sortOrder)
                      ->select('job_listings.*');
                break;
            case 'applications':
                $query->orderBy('applications_count', $sortOrder);
                break;
            case 'views':
                $query->orderBy('views_count', $sortOrder);
                break;
            default: // created_at
                $query->orderBy('created_at', $sortOrder);
        }

        $jobs = $query->paginate(20)->withQueryString();

        // Get statistics
        $totalJobs = Job::count();
        $activeJobs = Job::where('status', 'active')->count();
        $pendingJobs = Job::where('status', 'draft')->count();
        $closedJobs = Job::where('status', 'closed')->count();
        $expiredJobs = Job::where('expires_at', '<', now())->count();

        // Get filter options
        $statuses = ['draft', 'active', 'paused', 'closed'];
        $jobTypes = ['full-time', 'part-time', 'contract', 'freelance', 'internship'];
        $industries = Job::select('industry')
            ->distinct()
            ->whereNotNull('industry')
            ->pluck('industry')
            ->sort();

        return view('admin.jobs.index', compact(
            'jobs',
            'totalJobs',
            'activeJobs',
            'pendingJobs',
            'closedJobs',
            'expiredJobs',
            'statuses',
            'jobTypes',
            'industries'
        ));
    }

    /**
     * Display a specific job.
     */
    public function showJob(Job $job)
    {
        $job->load([
            'employerProfile.user',
            'applications.user',
            'media'
        ]);

        // Get job statistics
        $jobStats = [
            'total_applications' => $job->applications->count(),
            'new_applications' => $job->applications->where('created_at', '>=', now()->subDays(7))->count(),
            'viewed_applications' => $job->applications->where('status', '!=', 'submitted')->count(),
            'total_views' => $job->views_count,
            'days_active' => $job->created_at->diffInDays(now()),
        ];

        return view('admin.jobs.show', compact('job', 'jobStats'));
    }

    /**
     * Suspend a job.
     */
    public function suspendJob(Job $job)
    {
        $job->update(['status' => 'paused']);

        return back()->with('success', 'Job suspended successfully.');
    }

    /**
     * Activate a job.
     */
    public function activateJob(Job $job)
    {
        $job->update(['status' => 'active']);

        return back()->with('success', 'Job activated successfully.');
    }

    /**
     * Update a job.
     */
    public function updateJob(Request $request, Job $job)
    {
        $request->validate([
            'status' => 'required|in:draft,active,paused,closed,archived',
            'admin_notes' => 'nullable|string|max:1000',
        ]);

        $job->update([
            'status' => $request->status,
            'admin_notes' => $request->admin_notes,
        ]);

        return back()->with('success', 'Job updated successfully.');
    }

    /**
     * Delete a job.
     */
    public function destroyJob(Job $job)
    {
        $job->delete();

        return back()->with('success', 'Job deleted successfully.');
    }

    /**
     * Approve a job.
     */
    public function approveJob(Job $job)
    {
        $job->update(['status' => 'active']);

        return back()->with('success', 'Job approved successfully.');
    }

    /**
     * Reject a job.
     */
    public function rejectJob(Job $job)
    {
        $job->update(['status' => 'closed']);

        return back()->with('success', 'Job rejected successfully.');
    }

    /**
     * Feature a job.
     */
    public function featureJob(Job $job)
    {
        $job->update(['is_featured' => !$job->is_featured]);

        $message = $job->is_featured ? 'Job featured successfully.' : 'Job unfeatured successfully.';
        return back()->with('success', $message);
    }

    /**
     * Display course management.
     */
    public function courses(Request $request)
    {
        $query = Course::with(['instructor', 'media'])
            ->withCount(['enrollments', 'lessons']);

        // Filter by status
        if ($request->filled('status')) {
            if ($request->status === 'published') {
                $query->where('is_published', true);
            } elseif ($request->status === 'draft') {
                $query->where('is_published', false);
            }
        }

        // Filter by category
        if ($request->filled('category')) {
            $query->where('category', $request->category);
        }

        // Filter by level
        if ($request->filled('level')) {
            $query->where('level', $request->level);
        }

        // Search by title or instructor
        if ($request->filled('search')) {
            $searchTerm = $request->search;
            $query->where(function ($q) use ($searchTerm) {
                $q->where('title', 'like', "%{$searchTerm}%")
                  ->orWhere('description', 'like', "%{$searchTerm}%")
                  ->orWhereHas('instructor', function ($iq) use ($searchTerm) {
                      $iq->where('name', 'like', "%{$searchTerm}%");
                  });
            });
        }

        // Sort options
        $sortBy = $request->get('sort', 'created_at');
        $sortOrder = $request->get('order', 'desc');

        switch ($sortBy) {
            case 'title':
                $query->orderBy('title', $sortOrder);
                break;
            case 'instructor':
                $query->join('users', 'courses.instructor_id', '=', 'users.id')
                      ->orderBy('users.name', $sortOrder)
                      ->select('courses.*');
                break;
            case 'enrollments':
                $query->orderBy('enrollments_count', $sortOrder);
                break;
            case 'rating':
                $query->orderBy('rating', $sortOrder);
                break;
            default: // created_at
                $query->orderBy('created_at', $sortOrder);
        }

        $courses = $query->paginate(20)->withQueryString();

        // Get filter options
        $categories = Course::select('category')
            ->distinct()
            ->whereNotNull('category')
            ->pluck('category')
            ->sort();

        $levels = ['beginner', 'intermediate', 'advanced'];

        // Calculate statistics
        $totalCourses = Course::count();
        $publishedCourses = Course::where('is_published', true)->count();
        $draftCourses = Course::where('is_published', false)->count();
        $totalEnrollments = \App\Models\CourseEnrollment::count();

        return view('admin.courses.index', compact(
            'courses',
            'categories',
            'levels',
            'totalCourses',
            'publishedCourses',
            'draftCourses',
            'totalEnrollments'
        ));
    }

    /**
     * Show the form for creating a new course.
     */
    public function createCourse()
    {
        // Get all instructors for the dropdown
        $instructors = User::where('role', 'instructor')
            ->orWhere('role', 'admin')
            ->select('id', 'name')
            ->orderBy('name')
            ->get();

        // Get categories and levels for dropdowns
        $categories = Course::select('category')
            ->distinct()
            ->whereNotNull('category')
            ->pluck('category')
            ->sort();

        $levels = ['beginner', 'intermediate', 'advanced'];

        return view('admin.courses.create', compact(
            'instructors',
            'categories',
            'levels'
        ));
    }

    /**
     * Store a newly created course.
     */
    public function storeCourse(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'short_description' => 'nullable|string|max:500',
            'category' => 'required|string|in:web-development,mobile-development,data-science,design,business,marketing',
            'difficulty_level' => 'required|string|in:beginner,intermediate,advanced',
            'instructor_id' => 'required|exists:users,id',
            'duration_hours' => 'nullable|numeric|min:0',
            'price' => 'nullable|numeric|min:0',
            'prerequisites' => 'nullable|string',
            'learning_outcomes' => 'nullable|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_published' => 'boolean',
            'is_featured' => 'boolean',
            'has_certificate' => 'boolean'
        ]);

        // Convert textarea strings to arrays
        $prerequisites = $request->prerequisites ?
            array_filter(array_map('trim', explode("\n", $request->prerequisites))) :
            [];

        $learningOutcomes = $request->learning_outcomes ?
            array_filter(array_map('trim', explode("\n", $request->learning_outcomes))) :
            [];

        $course = Course::create([
            'title' => $request->title,
            'slug' => \Illuminate\Support\Str::slug($request->title),
            'description' => $request->description,
            'short_description' => $request->short_description,
            'category' => $request->category,
            'difficulty_level' => $request->difficulty_level,
            'instructor_id' => $request->instructor_id,
            'duration_hours' => $request->duration_hours,
            'price' => $request->price ?: 0,
            'prerequisites' => $prerequisites,
            'learning_outcomes' => $learningOutcomes,
            'is_published' => $request->boolean('is_published'),
            'is_featured' => $request->boolean('is_featured'),
            'has_certificate' => $request->boolean('has_certificate')
        ]);

        // Handle image upload
        if ($request->hasFile('image')) {
            $course->addMediaFromRequest('image')
                ->toMediaCollection('thumbnail');
        }

        return redirect()->route('admin.courses.index')
            ->with('success', 'Course created successfully!');
    }

    /**
     * Display a specific course.
     */
    public function showCourse(Course $course)
    {
        $course->load([
            'instructor',
            'lessons' => function ($query) {
                $query->orderBy('order');
            },
            'enrollments.user',
            'media'
        ]);

        // Get course statistics
        $courseStats = [
            'total_enrollments' => $course->enrollments->count(),
            'new_enrollments' => $course->enrollments->where('created_at', '>=', now()->subDays(7))->count(),
            'completed_enrollments' => $course->enrollments->whereNotNull('completed_at')->count(),
            'average_progress' => $course->enrollments->avg('progress') ?? 0,
            'total_lessons' => $course->lessons->count(),
            'total_duration' => $course->lessons->sum('duration_minutes') ?? $course->duration_hours * 60,
        ];

        return view('admin.courses.show', compact('course', 'courseStats'));
    }

    /**
     * Show the form for editing a course.
     */
    public function editCourse(Course $course)
    {
        // Load course with relationships
        $course->load(['instructor', 'lessons', 'media']);

        // Get all instructors for the dropdown
        $instructors = User::where('role', 'instructor')
            ->orWhere('role', 'admin')
            ->select('id', 'name')
            ->orderBy('name')
            ->get();

        // Get categories and levels for dropdowns
        $categories = Course::select('category')
            ->distinct()
            ->whereNotNull('category')
            ->pluck('category')
            ->sort();

        $levels = ['beginner', 'intermediate', 'advanced'];

        return view('admin.courses.edit', compact(
            'course',
            'instructors',
            'categories',
            'levels'
        ));
    }

    /**
     * Update a course.
     */
    public function updateCourse(Request $request, Course $course)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'short_description' => 'nullable|string|max:500',
            'category' => 'required|string|in:web-development,mobile-development,data-science,design,business,marketing',
            'difficulty_level' => 'required|string|in:beginner,intermediate,advanced',
            'instructor_id' => 'required|exists:users,id',
            'duration_hours' => 'nullable|numeric|min:0',
            'price' => 'nullable|numeric|min:0',
            'prerequisites' => 'nullable|string',
            'learning_outcomes' => 'nullable|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_published' => 'boolean',
            'is_featured' => 'boolean',
            'has_certificate' => 'boolean'
        ]);

        // Convert textarea strings to arrays
        $prerequisites = $request->prerequisites ?
            array_filter(array_map('trim', explode("\n", $request->prerequisites))) :
            [];

        $learningOutcomes = $request->learning_outcomes ?
            array_filter(array_map('trim', explode("\n", $request->learning_outcomes))) :
            [];

        $course->update([
            'title' => $request->title,
            'slug' => \Illuminate\Support\Str::slug($request->title),
            'description' => $request->description,
            'short_description' => $request->short_description,
            'category' => $request->category,
            'difficulty_level' => $request->difficulty_level,
            'instructor_id' => $request->instructor_id,
            'duration_hours' => $request->duration_hours,
            'price' => $request->price ?: 0,
            'prerequisites' => $prerequisites,
            'learning_outcomes' => $learningOutcomes,
            'is_published' => $request->boolean('is_published'),
            'is_featured' => $request->boolean('is_featured'),
            'has_certificate' => $request->boolean('has_certificate')
        ]);

        // Handle image upload
        if ($request->hasFile('image')) {
            $course->clearMediaCollection('thumbnail');
            $course->addMediaFromRequest('image')
                ->toMediaCollection('thumbnail');
        }

        // Handle different submission types
        if ($request->has('auto_save')) {
            return response()->json(['status' => 'success', 'message' => 'Course auto-saved successfully']);
        }

        if ($request->has('continue_editing')) {
            return redirect()->route('admin.courses.edit', $course)
                ->with('success', 'Course updated successfully! You can continue editing.');
        }

        return redirect()->route('admin.courses.index')
            ->with('success', 'Course updated successfully!');
    }

    /**
     * Delete a course.
     */
    public function destroyCourse(Course $course)
    {
        // Check if course has enrollments
        if ($course->enrollments()->count() > 0) {
            return back()->with('error', 'Cannot delete course with active enrollments.');
        }

        // Delete course media
        $course->clearMediaCollection('thumbnail');

        // Delete course
        $course->delete();

        return redirect()->route('admin.courses.index')
            ->with('success', 'Course deleted successfully!');
    }

    /**
     * Publish/unpublish a course.
     */
    public function publishCourse(Course $course)
    {
        $course->update(['is_published' => !$course->is_published]);

        $message = $course->is_published ? 'Course published successfully!' : 'Course unpublished successfully!';

        return back()->with('success', $message);
    }

    /**
     * Display subscription plans management.
     */
    public function subscriptionPlans()
    {
        $plans = SubscriptionPlan::withCount('subscriptions')
            ->with(['subscriptions' => function($query) {
                $query->where('status', 'active');
            }])
            ->ordered()
            ->get();

        // Calculate additional stats for each plan
        $plans->each(function($plan) {
            $plan->subscribers_count = $plan->subscriptions_count;
            $plan->total_revenue = $plan->subscriptions->sum(function($subscription) use ($plan) {
                return $plan->price;
            });
            // Set status based on is_active field
            $plan->status = $plan->is_active ? 'active' : 'inactive';
        });

        return view('admin.subscription-plans.index', compact('plans'));
    }

    /**
     * Store a new subscription plan.
     */
    public function storeSubscriptionPlan(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'price' => 'required|numeric|min:0',
            'billing_period' => 'required|string|in:monthly,yearly',
            'job_posting_limit' => 'nullable|integer|min:-1',
            'featured_jobs' => 'nullable|integer|min:0',
            'resume_database_access' => 'boolean',
            'advanced_analytics' => 'boolean',
            'is_active' => 'boolean',
        ]);

        // Generate slug from name
        $slug = \Illuminate\Support\Str::slug($request->name);

        // Ensure unique slug
        $originalSlug = $slug;
        $counter = 1;
        while (SubscriptionPlan::where('slug', $slug)->exists()) {
            $slug = $originalSlug . '-' . $counter;
            $counter++;
        }

        $plan = SubscriptionPlan::create([
            'name' => $request->name,
            'slug' => $slug,
            'description' => $request->description,
            'price' => $request->price,
            'billing_period' => $request->billing_period,
            'job_posting_limit' => $request->job_posting_limit,
            'featured_jobs' => $request->featured_jobs ?? 0,
            'resume_database_access' => $request->boolean('resume_database_access'),
            'advanced_analytics' => $request->boolean('advanced_analytics'),
            'is_active' => $request->boolean('is_active'),
            'features' => [], // Default empty features array
            'sort_order' => SubscriptionPlan::max('sort_order') + 1,
        ]);

        return back()->with('success', 'Subscription plan created successfully.');
    }

    /**
     * Show subscription plan edit form.
     */
    public function editSubscriptionPlan(SubscriptionPlan $plan)
    {
        return view('admin.subscription-plans.edit', compact('plan'));
    }

    /**
     * Update subscription plan.
     */
    public function updateSubscriptionPlan(Request $request, SubscriptionPlan $plan)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'price' => 'required|numeric|min:0',
            'billing_period' => 'required|string|in:monthly,yearly',
            'job_posting_limit' => 'nullable|integer|min:-1',
            'featured_jobs' => 'nullable|integer|min:0',
            'resume_database_access' => 'boolean',
            'advanced_analytics' => 'boolean',
            'is_active' => 'boolean',
        ]);

        // Update slug if name changed
        $slug = $plan->slug;
        if ($plan->name !== $request->name) {
            $slug = \Illuminate\Support\Str::slug($request->name);

            // Ensure unique slug
            $originalSlug = $slug;
            $counter = 1;
            while (SubscriptionPlan::where('slug', $slug)->where('id', '!=', $plan->id)->exists()) {
                $slug = $originalSlug . '-' . $counter;
                $counter++;
            }
        }

        $plan->update([
            'name' => $request->name,
            'slug' => $slug,
            'description' => $request->description,
            'price' => $request->price,
            'billing_period' => $request->billing_period,
            'job_posting_limit' => $request->job_posting_limit,
            'featured_jobs' => $request->featured_jobs ?? 0,
            'resume_database_access' => $request->boolean('resume_database_access'),
            'advanced_analytics' => $request->boolean('advanced_analytics'),
            'is_active' => $request->boolean('is_active'),
        ]);

        return back()->with('success', 'Subscription plan updated successfully.');
    }

    /**
     * Delete subscription plan.
     */
    public function destroySubscriptionPlan(SubscriptionPlan $plan)
    {
        // Check if plan has active subscriptions
        if ($plan->subscriptions()->where('status', 'active')->count() > 0) {
            return back()->with('error', 'Cannot delete plan with active subscriptions.');
        }

        $plan->delete();

        return back()->with('success', 'Subscription plan deleted successfully.');
    }

    /**
     * Activate subscription plan.
     */
    public function activateSubscriptionPlan(SubscriptionPlan $plan)
    {
        $plan->update(['is_active' => true]);
        return back()->with('success', 'Subscription plan activated successfully.');
    }

    /**
     * Deactivate subscription plan.
     */
    public function deactivateSubscriptionPlan(SubscriptionPlan $plan)
    {
        $plan->update(['is_active' => false]);
        return back()->with('success', 'Subscription plan deactivated successfully.');
    }

    /**
     * Duplicate subscription plan.
     */
    public function duplicateSubscriptionPlan(SubscriptionPlan $plan)
    {
        $newPlan = $plan->replicate();
        $newPlan->name = $plan->name . ' (Copy)';
        $newPlan->slug = \Illuminate\Support\Str::slug($newPlan->name);

        // Ensure unique slug
        $originalSlug = $newPlan->slug;
        $counter = 1;
        while (SubscriptionPlan::where('slug', $newPlan->slug)->exists()) {
            $newPlan->slug = $originalSlug . '-' . $counter;
            $counter++;
        }

        $newPlan->is_active = false;
        $newPlan->sort_order = SubscriptionPlan::max('sort_order') + 1;
        $newPlan->save();

        return response()->json(['success' => true, 'message' => 'Plan duplicated successfully']);
    }

    /**
     * Get plan subscribers.
     */
    public function getSubscriptionPlanSubscribers(SubscriptionPlan $plan)
    {
        $subscribers = $plan->subscriptions()
            ->with('user')
            ->where('status', 'active')
            ->latest()
            ->paginate(20);

        return view('admin.subscription-plans.subscribers', compact('plan', 'subscribers'));
    }

    /**
     * Export subscription plans.
     */
    public function exportSubscriptionPlans()
    {
        $plans = SubscriptionPlan::withCount('subscriptions')->get();

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="subscription_plans_' . now()->format('Y-m-d') . '.csv"',
        ];

        $callback = function () use ($plans) {
            $file = fopen('php://output', 'w');
            fputcsv($file, ['Name', 'Price', 'Billing Cycle', 'Status', 'Subscribers', 'Created Date']);

            foreach ($plans as $plan) {
                fputcsv($file, [
                    $plan->name,
                    '$' . number_format($plan->price, 2),
                    $plan->billing_cycle,
                    $plan->status,
                    $plan->subscriptions_count,
                    $plan->created_at->format('Y-m-d H:i:s'),
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }



    /**
     * Toggle subscription plan status.
     */
    public function toggleSubscriptionPlanStatus(SubscriptionPlan $plan)
    {
        $newStatus = $plan->status === 'active' ? 'inactive' : 'active';
        $plan->update(['status' => $newStatus]);

        $message = $newStatus === 'active' ? 'activated' : 'deactivated';
        return back()->with('success', "Subscription plan {$message} successfully.");
    }

    /**
     * Display subscriptions management.
     */
    public function subscriptions(Request $request)
    {
        $query = Subscription::with(['user', 'plan']);

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by plan
        if ($request->filled('plan_id')) {
            $query->where('subscription_plan_id', $request->plan_id);
        }

        // Search by user
        if ($request->filled('search')) {
            $searchTerm = $request->search;
            $query->whereHas('user', function ($q) use ($searchTerm) {
                $q->where('name', 'like', "%{$searchTerm}%")
                  ->orWhere('email', 'like', "%{$searchTerm}%");
            });
        }

        $subscriptions = $query->latest()->paginate(20)->withQueryString();

        // Get filter options
        $plans = SubscriptionPlan::select('id', 'name')->get();
        $statuses = ['active', 'cancelled', 'expired', 'past_due'];

        return view('admin.subscriptions.index', compact(
            'subscriptions',
            'plans',
            'statuses'
        ));
    }

    /**
     * Display system analytics.
     */
    public function analytics(Request $request)
    {
        $period = $request->get('period', '30'); // days
        $startDate = now()->subDays($period);

        // User analytics
        $userAnalytics = $this->getUserAnalytics($startDate);

        // Job analytics
        $jobAnalytics = $this->getJobAnalytics($startDate);

        // Course analytics
        $courseAnalytics = $this->getCourseAnalytics($startDate);

        // Revenue analytics
        $revenueAnalytics = $this->getRevenueAnalytics($startDate);

        return view('admin.analytics.index', compact(
            'userAnalytics',
            'jobAnalytics',
            'courseAnalytics',
            'revenueAnalytics',
            'period'
        ));
    }

    /**
     * Display user analytics.
     */
    public function userAnalytics(Request $request)
    {
        $period = $request->get('period', '30');
        $startDate = now()->subDays($period);

        // Get user analytics data
        $userAnalytics = $this->getUserAnalytics($startDate);

        // Get detailed user statistics
        $userStats = [
            'total_users' => User::count(),
            'new_users' => User::where('created_at', '>=', $startDate)->count(),
            'active_users' => User::whereNotNull('email_verified_at')->count(),
            'users_by_role' => User::selectRaw('role, COUNT(*) as count')
                ->groupBy('role')
                ->pluck('count', 'role'),
            'user_growth' => User::where('created_at', '>=', now()->subDays(30))
                ->selectRaw('DATE(created_at) as date, COUNT(*) as count')
                ->groupBy('date')
                ->orderBy('date')
                ->get(),
            'top_locations' => User::leftJoin('job_seeker_profiles', 'users.id', '=', 'job_seeker_profiles.user_id')
                ->selectRaw('COALESCE(job_seeker_profiles.location, "Unknown") as location, COUNT(*) as count')
                ->groupBy('job_seeker_profiles.location')
                ->orderBy('count', 'desc')
                ->limit(10)
                ->pluck('count', 'location'),
        ];

        return view('admin.analytics.users', compact('userStats', 'period'));
    }

    /**
     * Display job analytics.
     */
    public function jobAnalytics(Request $request)
    {
        $period = $request->get('period', '30');
        $startDate = now()->subDays($period);

        // Get job analytics data
        $jobAnalytics = $this->getJobAnalytics($startDate);

        // Get detailed job statistics
        $jobStats = [
            'total_jobs' => Job::count(),
            'new_jobs' => Job::where('created_at', '>=', $startDate)->count(),
            'active_jobs' => Job::where('status', 'active')->count(),
            'jobs_by_type' => Job::selectRaw('job_type, COUNT(*) as count')
                ->groupBy('job_type')
                ->pluck('count', 'job_type'),
            'jobs_by_status' => Job::selectRaw('status, COUNT(*) as count')
                ->groupBy('status')
                ->pluck('count', 'status'),
            'jobs_by_industry' => Job::selectRaw('industry, COUNT(*) as count')
                ->whereNotNull('industry')
                ->groupBy('industry')
                ->orderBy('count', 'desc')
                ->limit(10)
                ->pluck('count', 'industry'),
            'job_growth' => Job::where('created_at', '>=', now()->subDays(30))
                ->selectRaw('DATE(created_at) as date, COUNT(*) as count')
                ->groupBy('date')
                ->orderBy('date')
                ->get(),
            'application_stats' => [
                'total_applications' => JobApplication::count(),
                'new_applications' => JobApplication::where('created_at', '>=', $startDate)->count(),
                'applications_by_status' => JobApplication::selectRaw('status, COUNT(*) as count')
                    ->groupBy('status')
                    ->pluck('count', 'status'),
            ],
        ];

        return view('admin.analytics.jobs', compact('jobStats', 'period'));
    }

    /**
     * Display course analytics.
     */
    public function courseAnalytics(Request $request)
    {
        $period = $request->get('period', '30');
        $startDate = now()->subDays($period);

        // Get course analytics data
        $courseAnalytics = $this->getCourseAnalytics($startDate);

        // Get detailed course statistics
        $courseStats = [
            'total_courses' => Course::count(),
            'new_courses' => Course::where('created_at', '>=', $startDate)->count(),
            'published_courses' => Course::where('is_published', true)->count(),
            'courses_by_category' => Course::selectRaw('category, COUNT(*) as count')
                ->whereNotNull('category')
                ->groupBy('category')
                ->orderBy('count', 'desc')
                ->pluck('count', 'category'),
            'courses_by_level' => Course::selectRaw('difficulty_level, COUNT(*) as count')
                ->whereNotNull('difficulty_level')
                ->groupBy('difficulty_level')
                ->pluck('count', 'difficulty_level'),
            'enrollment_stats' => [
                'total_enrollments' => CourseEnrollment::count(),
                'new_enrollments' => CourseEnrollment::where('created_at', '>=', $startDate)->count(),
                'completed_enrollments' => CourseEnrollment::whereNotNull('completed_at')->count(),
                'enrollment_growth' => CourseEnrollment::where('created_at', '>=', now()->subDays(30))
                    ->selectRaw('DATE(created_at) as date, COUNT(*) as count')
                    ->groupBy('date')
                    ->orderBy('date')
                    ->get(),
            ],
            'popular_courses' => Course::withCount('enrollments')
                ->orderBy('enrollments_count', 'desc')
                ->limit(10)
                ->get(['id', 'title', 'enrollments_count']),
        ];

        return view('admin.analytics.courses', compact('courseStats', 'period'));
    }

    /**
     * Display revenue analytics.
     */
    public function revenueAnalytics(Request $request)
    {
        // Get date range from request or default to last 30 days
        $period = $request->get('period', '30');
        $startDate = Carbon::now()->subDays($period);

        // Get revenue analytics data
        $revenueData = $this->getRevenueAnalytics($startDate);

        // Get monthly revenue trend
        $monthlyRevenue = Subscription::where('subscriptions.status', 'active')
            ->where('subscriptions.created_at', '>=', $startDate)
            ->join('subscription_plans', 'subscriptions.subscription_plan_id', '=', 'subscription_plans.id')
            ->selectRaw('DATE_FORMAT(subscriptions.created_at, "%Y-%m") as month, SUM(subscription_plans.price) as revenue')
            ->groupBy('month')
            ->orderBy('month')
            ->pluck('revenue', 'month');

        // Get subscription status breakdown
        $subscriptionStatus = Subscription::selectRaw('status, COUNT(*) as count')
            ->groupBy('status')
            ->pluck('count', 'status');

        // Get detailed revenue statistics
        $revenueStats = [
            'total_revenue' => $revenueData['total_revenue'],
            'monthly_revenue' => $monthlyRevenue,
            'revenue_by_plan' => $revenueData['revenue_by_plan'],
            'new_subscriptions' => $revenueData['new_subscriptions'],
            'subscription_status' => $subscriptionStatus,
            'revenue_growth' => Subscription::where('subscriptions.created_at', '>=', now()->subDays(30))
                ->join('subscription_plans', 'subscriptions.subscription_plan_id', '=', 'subscription_plans.id')
                ->selectRaw('DATE(subscriptions.created_at) as date, SUM(subscription_plans.price) as revenue')
                ->groupBy('date')
                ->orderBy('date')
                ->get(),
        ];

        return view('admin.analytics.revenue', compact('revenueStats', 'period'));
    }

    /**
     * Export analytics data.
     */
    public function exportAnalytics(Request $request, $type)
    {
        $period = $request->get('period', '30');
        $startDate = now()->subDays($period);

        switch ($type) {
            case 'users':
                return $this->exportUserAnalytics($startDate);
            case 'jobs':
                return $this->exportJobAnalytics($startDate);
            case 'courses':
                return $this->exportCourseAnalytics($startDate);
            case 'revenue':
                return $this->exportRevenueAnalytics($startDate);
            default:
                return back()->with('error', 'Invalid export type.');
        }
    }

    /**
     * Export user analytics data.
     */
    private function exportUserAnalytics($startDate)
    {
        $users = User::with(['jobSeekerProfile', 'employerProfile'])
            ->where('created_at', '>=', $startDate)
            ->get();

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="user_analytics_' . now()->format('Y-m-d') . '.csv"',
        ];

        $callback = function () use ($users) {
            $file = fopen('php://output', 'w');
            fputcsv($file, ['Name', 'Email', 'Role', 'Registration Date', 'Email Verified', 'Last Login']);

            foreach ($users as $user) {
                fputcsv($file, [
                    $user->name,
                    $user->email,
                    $user->role,
                    $user->created_at->format('Y-m-d H:i:s'),
                    $user->email_verified_at ? 'Yes' : 'No',
                    $user->last_login_at ? $user->last_login_at->format('Y-m-d H:i:s') : 'Never',
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Export job analytics data.
     */
    private function exportJobAnalytics($startDate)
    {
        $jobs = Job::with(['employerProfile.user', 'applications'])
            ->where('created_at', '>=', $startDate)
            ->get();

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="job_analytics_' . now()->format('Y-m-d') . '.csv"',
        ];

        $callback = function () use ($jobs) {
            $file = fopen('php://output', 'w');
            fputcsv($file, ['Title', 'Company', 'Type', 'Status', 'Industry', 'Applications', 'Views', 'Posted Date']);

            foreach ($jobs as $job) {
                fputcsv($file, [
                    $job->title,
                    $job->employerProfile->company_name ?? 'N/A',
                    $job->job_type,
                    $job->status,
                    $job->industry,
                    $job->applications->count(),
                    $job->views_count,
                    $job->created_at->format('Y-m-d H:i:s'),
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Export course analytics data.
     */
    private function exportCourseAnalytics($startDate)
    {
        $courses = Course::with(['instructor', 'enrollments'])
            ->where('created_at', '>=', $startDate)
            ->get();

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="course_analytics_' . now()->format('Y-m-d') . '.csv"',
        ];

        $callback = function () use ($courses) {
            $file = fopen('php://output', 'w');
            fputcsv($file, ['Title', 'Instructor', 'Category', 'Level', 'Enrollments', 'Published', 'Created Date']);

            foreach ($courses as $course) {
                fputcsv($file, [
                    $course->title,
                    $course->instructor->name ?? 'N/A',
                    $course->category,
                    $course->difficulty_level,
                    $course->enrollments->count(),
                    $course->is_published ? 'Yes' : 'No',
                    $course->created_at->format('Y-m-d H:i:s'),
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Export revenue analytics data.
     */
    private function exportRevenueAnalytics($startDate)
    {
        $subscriptions = Subscription::with(['user', 'subscriptionPlan'])
            ->where('created_at', '>=', $startDate)
            ->get();

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="revenue_analytics_' . now()->format('Y-m-d') . '.csv"',
        ];

        $callback = function () use ($subscriptions) {
            $file = fopen('php://output', 'w');
            fputcsv($file, ['User', 'Plan', 'Price', 'Status', 'Start Date', 'End Date']);

            foreach ($subscriptions as $subscription) {
                fputcsv($file, [
                    $subscription->user->name ?? 'N/A',
                    $subscription->subscriptionPlan->name ?? 'N/A',
                    $subscription->subscriptionPlan->price ?? 0,
                    $subscription->status,
                    $subscription->created_at->format('Y-m-d H:i:s'),
                    $subscription->ends_at ? $subscription->ends_at->format('Y-m-d H:i:s') : 'N/A',
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Display system settings.
     */
    public function settings()
    {
        // Get current system settings
        $settings = \App\Models\SystemSetting::pluck('value', 'key');

        return view('admin.settings', compact('settings'));
    }

    /**
     * Update system settings.
     */
    public function updateSettings(Request $request)
    {
        $request->validate([
            'site_name' => 'required|string|max:255',
            'site_description' => 'nullable|string|max:500',
            'contact_email' => 'required|email',
            'maintenance_mode' => 'boolean',
            'user_registration' => 'boolean',
            'job_posting_approval' => 'boolean',
            'course_approval' => 'boolean',
        ]);

        foreach ($request->except('_token') as $key => $value) {
            \App\Models\SystemSetting::updateOrCreate(
                ['key' => $key],
                ['value' => $value]
            );
        }

        return back()->with('success', 'Settings updated successfully.');
    }

    /**
     * Get dashboard statistics.
     */
    private function getDashboardStats()
    {
        return [
            'total_users' => User::count(),
            'new_users_today' => User::whereDate('created_at', today())->count(),
            'total_jobs' => Job::count(),
            'active_jobs' => Job::where('status', 'active')
                ->where('expires_at', '>', now())
                ->count(),
            'total_applications' => JobApplication::count(),
            'pending_applications' => JobApplication::where('status', 'pending')->count(),
            'total_courses' => Course::count(),
            'published_courses' => Course::where('is_published', true)->count(),
            'total_enrollments' => CourseEnrollment::count(),
            'active_subscriptions' => Subscription::where('subscriptions.status', 'active')->count(),
            'monthly_revenue' => Subscription::where('subscriptions.status', 'active')
                ->join('subscription_plans', 'subscriptions.subscription_plan_id', '=', 'subscription_plans.id')
                ->sum('subscription_plans.price'),
        ];
    }

    /**
     * Get analytics data for charts.
     */
    private function getAnalyticsData()
    {
        $last30Days = collect(range(0, 29))->map(function ($i) {
            return now()->subDays($i)->format('Y-m-d');
        })->reverse()->values();

        // User registrations per day
        $userRegistrations = User::where('created_at', '>=', now()->subDays(30))
            ->selectRaw('DATE(created_at) as date, COUNT(*) as count')
            ->groupBy('date')
            ->pluck('count', 'date');

        // Job postings per day
        $jobPostings = Job::where('created_at', '>=', now()->subDays(30))
            ->selectRaw('DATE(created_at) as date, COUNT(*) as count')
            ->groupBy('date')
            ->pluck('count', 'date');

        // Applications per day
        $applications = JobApplication::where('created_at', '>=', now()->subDays(30))
            ->selectRaw('DATE(created_at) as date, COUNT(*) as count')
            ->groupBy('date')
            ->pluck('count', 'date');

        return [
            'dates' => $last30Days,
            'user_registrations' => $last30Days->map(fn($date) => $userRegistrations->get($date, 0)),
            'job_postings' => $last30Days->map(fn($date) => $jobPostings->get($date, 0)),
            'applications' => $last30Days->map(fn($date) => $applications->get($date, 0)),
        ];
    }

    /**
     * Get user statistics for a specific user.
     */
    private function getUserStats(User $user)
    {
        $stats = [
            'total_logins' => $user->login_count ?? 0,
            'last_login' => $user->last_login_at,
            'account_age' => $user->created_at->diffInDays(now()),
        ];

        if ($user->role === 'job_seeker') {
            $stats['applications_sent'] = $user->jobApplications()->count();
            $stats['courses_enrolled'] = $user->courseEnrollments()->count();
            $stats['courses_completed'] = $user->courseEnrollments()
                ->whereNotNull('completed_at')
                ->count();
        } elseif ($user->role === 'employer') {
            $stats['jobs_posted'] = $user->employerProfile->jobs()->count();
            $stats['applications_received'] = JobApplication::whereHas('job', function ($q) use ($user) {
                $q->where('employer_profile_id', $user->employerProfile->id);
            })->count();
        }

        return $stats;
    }

    /**
     * Get user analytics.
     */
    private function getUserAnalytics($startDate)
    {
        return [
            'new_users' => User::where('created_at', '>=', $startDate)->count(),
            'users_by_role' => User::selectRaw('role, COUNT(*) as count')
                ->groupBy('role')
                ->pluck('count', 'role'),
            'user_growth' => User::where('created_at', '>=', $startDate)
                ->selectRaw('DATE(created_at) as date, COUNT(*) as count')
                ->groupBy('date')
                ->orderBy('date')
                ->get(),
        ];
    }

    /**
     * Get job analytics.
     */
    private function getJobAnalytics($startDate)
    {
        return [
            'new_jobs' => Job::where('created_at', '>=', $startDate)->count(),
            'jobs_by_type' => Job::selectRaw('job_type, COUNT(*) as count')
                ->groupBy('job_type')
                ->pluck('count', 'job_type'),
            'jobs_by_status' => Job::selectRaw('status, COUNT(*) as count')
                ->groupBy('status')
                ->pluck('count', 'status'),
            'top_industries' => Job::selectRaw('industry, COUNT(*) as count')
                ->whereNotNull('industry')
                ->groupBy('industry')
                ->orderBy('count', 'desc')
                ->limit(10)
                ->pluck('count', 'industry'),
        ];
    }

    /**
     * Get course analytics.
     */
    private function getCourseAnalytics($startDate)
    {
        return [
            'new_courses' => Course::where('created_at', '>=', $startDate)->count(),
            'courses_by_category' => Course::selectRaw('category, COUNT(*) as count')
                ->whereNotNull('category')
                ->groupBy('category')
                ->pluck('count', 'category'),
            'courses_by_level' => Course::selectRaw('difficulty_level, COUNT(*) as count')
                ->whereNotNull('difficulty_level')
                ->groupBy('difficulty_level')
                ->pluck('count', 'difficulty_level'),
            'enrollment_trends' => CourseEnrollment::where('created_at', '>=', $startDate)
                ->selectRaw('DATE(created_at) as date, COUNT(*) as count')
                ->groupBy('date')
                ->orderBy('date')
                ->get(),
        ];
    }

    /**
     * Get revenue analytics.
     */
    private function getRevenueAnalytics($startDate)
    {
        return [
            'total_revenue' => Subscription::where('subscriptions.status', 'active')
                ->join('subscription_plans', 'subscriptions.subscription_plan_id', '=', 'subscription_plans.id')
                ->sum('subscription_plans.price'),
            'revenue_by_plan' => Subscription::where('subscriptions.status', 'active')
                ->join('subscription_plans', 'subscriptions.subscription_plan_id', '=', 'subscription_plans.id')
                ->selectRaw('subscription_plans.name, SUM(subscription_plans.price) as revenue')
                ->groupBy('subscription_plans.id', 'subscription_plans.name')
                ->pluck('revenue', 'name'),
            'new_subscriptions' => Subscription::where('subscriptions.created_at', '>=', $startDate)->count(),
        ];
    }

    /**
     * Display job applications management.
     */
    public function applications(Request $request)
    {
        $query = JobApplication::with(['user', 'job.employerProfile', 'jobSeekerProfile']);

        // Filter by job if job_id is provided
        if ($request->filled('job_id')) {
            $query->where('job_id', $request->job_id);
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        // Search by applicant name or job title
        if ($request->filled('search')) {
            $searchTerm = $request->search;
            $query->where(function ($q) use ($searchTerm) {
                $q->whereHas('user', function ($userQuery) use ($searchTerm) {
                    $userQuery->where('name', 'like', "%{$searchTerm}%")
                             ->orWhere('email', 'like', "%{$searchTerm}%");
                })
                ->orWhereHas('job', function ($jobQuery) use ($searchTerm) {
                    $jobQuery->where('title', 'like', "%{$searchTerm}%");
                });
            });
        }

        $applications = $query->latest()->paginate(20)->withQueryString();

        // Get filter options
        $jobs = Job::select('id', 'title')->get();
        $statuses = ['pending', 'reviewing', 'shortlisted', 'interviewed', 'offered', 'hired', 'rejected'];

        return view('admin.applications.index', compact(
            'applications',
            'jobs',
            'statuses'
        ));
    }

    /**
     * Display a specific job application.
     */
    public function showApplication(JobApplication $application)
    {
        $application->load(['user', 'job.employerProfile', 'jobSeekerProfile', 'resume']);

        return view('admin.applications.show', compact('application'));
    }

    /**
     * Update a job application.
     */
    public function updateApplication(Request $request, JobApplication $application)
    {
        $request->validate([
            'status' => 'required|in:pending,reviewing,shortlisted,interviewed,offered,hired,rejected',
            'admin_notes' => 'nullable|string|max:1000',
        ]);

        $application->update([
            'status' => $request->status,
            'admin_notes' => $request->admin_notes,
        ]);

        return back()->with('success', 'Application updated successfully.');
    }

    /**
     * Delete a job application.
     */
    public function destroyApplication(JobApplication $application)
    {
        $application->delete();

        return back()->with('success', 'Application deleted successfully.');
    }

    /**
     * Approve a job application.
     */
    public function approveApplication(JobApplication $application)
    {
        $application->update(['status' => 'hired']);

        return back()->with('success', 'Application approved successfully.');
    }

    /**
     * Reject a job application.
     */
    public function rejectApplication(JobApplication $application)
    {
        $application->update(['status' => 'rejected']);

        return back()->with('success', 'Application rejected successfully.');
    }


}
