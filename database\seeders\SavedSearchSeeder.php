<?php

namespace Database\Seeders;

use App\Models\SavedSearch;
use App\Models\User;
use Illuminate\Database\Seeder;

class SavedSearchSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $jobSeekers = User::where('role', 'job_seeker')->where('job_alerts_enabled', true)->get();
        
        if ($jobSeekers->isEmpty()) {
            $this->command->warn('No job seekers with alerts enabled found. Please run UserSeeder first.');
            return;
        }

        $searchTerms = [
            'Software Engineer', 'Frontend Developer', 'Backend Developer', 'Full Stack Developer',
            'Data Scientist', 'Product Manager', 'UX Designer', 'DevOps Engineer',
            'Marketing Manager', 'Sales Representative', 'Business Analyst', 'Project Manager',
            'Customer Success', 'Content Writer', 'Graphic Designer', 'HR Specialist'
        ];

        $locations = [
            'New York, NY', 'San Francisco, CA', 'Los Angeles, CA', 'Chicago, IL',
            'Boston, MA', 'Seattle, WA', 'Austin, TX', 'Denver, CO', 'Atlanta, GA',
            'Remote', 'Anywhere', 'Miami, FL', 'Dallas, TX', 'Phoenix, AZ'
        ];

        $jobTypes = ['full-time', 'part-time', 'contract', 'internship'];
        $experienceLevels = ['entry', 'mid', 'senior', 'executive'];
        $industries = [
            'Technology', 'Healthcare', 'Finance', 'Education', 'Manufacturing',
            'Retail', 'Marketing', 'Consulting', 'Real Estate', 'Transportation'
        ];

        foreach ($jobSeekers as $jobSeeker) {
            // Each job seeker has 1-5 saved searches
            $searchCount = fake()->numberBetween(1, 5);
            
            for ($i = 0; $i < $searchCount; $i++) {
                $searchCriteria = $this->generateSearchCriteria(
                    $searchTerms, $locations, $jobTypes, $experienceLevels, $industries
                );
                
                SavedSearch::create([
                    'user_id' => $jobSeeker->id,
                    'name' => $this->generateSearchName($searchCriteria),
                    'search_criteria' => $searchCriteria,
                    'email_alerts' => fake()->boolean(80),
                    'alert_frequency' => fake()->randomElement(['daily', 'weekly', 'monthly']),
                    'last_alert_sent' => fake()->optional(0.7)->dateTimeBetween('-1 month', 'now'),
                    'is_active' => fake()->boolean(80),
                    'created_at' => fake()->dateTimeBetween('-6 months', 'now'),
                ]);
            }
        }

        $this->command->info('Saved searches created successfully!');
    }

    private function generateSearchCriteria($searchTerms, $locations, $jobTypes, $experienceLevels, $industries)
    {
        $criteria = [];
        
        // Keywords (80% chance)
        if (fake()->boolean(80)) {
            $criteria['keywords'] = fake()->randomElement($searchTerms);
        }
        
        // Location (70% chance)
        if (fake()->boolean(70)) {
            $criteria['location'] = fake()->randomElement($locations);
        }
        
        // Job type (60% chance)
        if (fake()->boolean(60)) {
            $criteria['job_type'] = fake()->randomElement($jobTypes);
        }
        
        // Experience level (50% chance)
        if (fake()->boolean(50)) {
            $criteria['experience_level'] = fake()->randomElement($experienceLevels);
        }
        
        // Industry (40% chance)
        if (fake()->boolean(40)) {
            $criteria['industry'] = fake()->randomElement($industries);
        }
        
        // Salary range (30% chance)
        if (fake()->boolean(30)) {
            $minSalary = fake()->numberBetween(30000, 80000);
            $criteria['salary_min'] = $minSalary;
            $criteria['salary_max'] = $minSalary + fake()->numberBetween(20000, 70000);
        }
        
        // Remote work (25% chance)
        if (fake()->boolean(25)) {
            $criteria['is_remote'] = true;
        }
        
        // Company size (20% chance)
        if (fake()->boolean(20)) {
            $criteria['company_size'] = fake()->randomElement(['1-10', '11-50', '51-200', '201-500', '501-1000', '1000+']);
        }
        
        // Posted date (15% chance)
        if (fake()->boolean(15)) {
            $criteria['posted_within'] = fake()->randomElement(['1 day', '3 days', '1 week', '2 weeks', '1 month']);
        }

        return $criteria;
    }

    private function generateSearchName($criteria)
    {
        $nameParts = [];
        
        if (isset($criteria['keywords'])) {
            $nameParts[] = $criteria['keywords'];
        }
        
        if (isset($criteria['location']) && $criteria['location'] !== 'Anywhere') {
            $nameParts[] = 'in ' . $criteria['location'];
        }
        
        if (isset($criteria['job_type'])) {
            $nameParts[] = '(' . ucfirst($criteria['job_type']) . ')';
        }
        
        if (isset($criteria['is_remote']) && $criteria['is_remote']) {
            $nameParts[] = '(Remote)';
        }
        
        if (empty($nameParts)) {
            return 'Job Search ' . fake()->numberBetween(1, 100);
        }
        
        $name = implode(' ', $nameParts);
        
        // Limit name length
        if (strlen($name) > 100) {
            $name = substr($name, 0, 97) . '...';
        }
        
        return $name;
    }
}