<?php

namespace App\Helpers;

class SeoHelper
{
    protected static $title;
    protected static $description;
    protected static $keywords;
    protected static $image;
    protected static $url;
    protected static $type = 'website';
    protected static $siteName = 'Rectra';
    protected static $twitterHandle = '@rectra';
    protected static $structuredData = [];
    
    public static function setTitle($title)
    {
        self::$title = $title;
        return new static;
    }
    
    public static function setDescription($description)
    {
        self::$description = $description;
        return new static;
    }
    
    public static function setKeywords($keywords)
    {
        if (is_array($keywords)) {
            $keywords = implode(', ', $keywords);
        }
        self::$keywords = $keywords;
        return new static;
    }
    
    public static function setImage($image)
    {
        self::$image = $image;
        return new static;
    }
    
    public static function setUrl($url)
    {
        self::$url = $url;
        return new static;
    }
    
    public static function setType($type)
    {
        self::$type = $type;
        return new static;
    }
    
    public static function addStructuredData($data)
    {
        self::$structuredData[] = $data;
        return new static;
    }
    
    public static function getTitle()
    {
        return self::$title ?: config('app.name') . ' - Job Board & Training Platform';
    }
    
    public static function getDescription()
    {
        return self::$description ?: 'Find your dream job and advance your career with Rectra - the modern job board and professional training platform.';
    }
    
    public static function getKeywords()
    {
        return self::$keywords ?: 'jobs, careers, training, courses, employment, job search, professional development';
    }
    
    public static function getImage()
    {
        return self::$image ?: asset('images/og-default.jpg');
    }
    
    public static function getUrl()
    {
        return self::$url ?: url()->current();
    }
    
    public static function getCanonicalUrl()
    {
        return self::getUrl();
    }
    
    public static function renderMetaTags()
    {
        $html = '';
        
        // Basic meta tags
        $html .= '<title>' . self::getTitle() . '</title>' . "\n";
        $html .= '<meta name="description" content="' . self::getDescription() . '">' . "\n";
        $html .= '<meta name="keywords" content="' . self::getKeywords() . '">' . "\n";
        $html .= '<link rel="canonical" href="' . self::getCanonicalUrl() . '">' . "\n";
        
        // Open Graph tags
        $html .= '<meta property="og:title" content="' . self::getTitle() . '">' . "\n";
        $html .= '<meta property="og:description" content="' . self::getDescription() . '">' . "\n";
        $html .= '<meta property="og:image" content="' . self::getImage() . '">' . "\n";
        $html .= '<meta property="og:url" content="' . self::getUrl() . '">' . "\n";
        $html .= '<meta property="og:type" content="' . self::$type . '">' . "\n";
        $html .= '<meta property="og:site_name" content="' . self::$siteName . '">' . "\n";
        
        // Twitter Card tags
        $html .= '<meta name="twitter:card" content="summary_large_image">' . "\n";
        $html .= '<meta name="twitter:site" content="' . self::$twitterHandle . '">' . "\n";
        $html .= '<meta name="twitter:title" content="' . self::getTitle() . '">' . "\n";
        $html .= '<meta name="twitter:description" content="' . self::getDescription() . '">' . "\n";
        $html .= '<meta name="twitter:image" content="' . self::getImage() . '">' . "\n";
        
        return $html;
    }
    
    public static function renderStructuredData()
    {
        if (empty(self::$structuredData)) {
            return '';
        }
        
        $html = '<script type="application/ld+json">' . "\n";
        $html .= json_encode(self::$structuredData, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES);
        $html .= "\n" . '</script>' . "\n";
        
        return $html;
    }
    
    public static function generateJobStructuredData($job)
    {
        return [
            '@context' => 'https://schema.org',
            '@type' => 'JobPosting',
            'title' => $job->title,
            'description' => strip_tags($job->description),
            'datePosted' => $job->created_at->toISOString(),
            'validThrough' => $job->expires_at->toISOString(),
            'employmentType' => strtoupper(str_replace('_', '', $job->job_type)),
            'hiringOrganization' => [
                '@type' => 'Organization',
                'name' => $job->employerProfile->company_name ?? 'Company',
                'sameAs' => $job->employerProfile->website ?? null,
            ],
            'jobLocation' => [
                '@type' => 'Place',
                'address' => [
                    '@type' => 'PostalAddress',
                    'addressLocality' => $job->location,
                ]
            ],
            'baseSalary' => $job->salary_min ? [
                '@type' => 'MonetaryAmount',
                'currency' => 'USD',
                'value' => [
                    '@type' => 'QuantitativeValue',
                    'minValue' => $job->salary_min,
                    'maxValue' => $job->salary_max,
                    'unitText' => 'YEAR'
                ]
            ] : null,
        ];
    }
    
    public static function generateCourseStructuredData($course)
    {
        return [
            '@context' => 'https://schema.org',
            '@type' => 'Course',
            'name' => $course->title,
            'description' => strip_tags($course->description),
            'provider' => [
                '@type' => 'Organization',
                'name' => 'Rectra',
                'sameAs' => url('/')
            ],
            'offers' => [
                '@type' => 'Offer',
                'price' => $course->price,
                'priceCurrency' => 'USD',
                'category' => $course->is_premium ? 'Premium' : 'Free'
            ],
            'courseMode' => 'online',
            'educationalLevel' => $course->difficulty_level,
            'timeRequired' => 'PT' . $course->estimated_duration . 'H',
        ];
    }
    
    public static function generateOrganizationStructuredData()
    {
        return [
            '@context' => 'https://schema.org',
            '@type' => 'Organization',
            'name' => 'Rectra',
            'url' => url('/'),
            'logo' => asset('images/logo.png'),
            'description' => 'Modern job board and professional training platform connecting talent with opportunity.',
            'sameAs' => [
                'https://twitter.com/rectra',
                'https://linkedin.com/company/rectra',
                'https://facebook.com/rectra'
            ],
            'contactPoint' => [
                '@type' => 'ContactPoint',
                'telephone' => '******-0123',
                'contactType' => 'customer service',
                'email' => '<EMAIL>'
            ]
        ];
    }
    
    public static function generateWebsiteStructuredData()
    {
        return [
            '@context' => 'https://schema.org',
            '@type' => 'WebSite',
            'name' => 'Rectra',
            'url' => url('/'),
            'potentialAction' => [
                [
                    '@type' => 'SearchAction',
                    'target' => [
                        '@type' => 'EntryPoint',
                        'urlTemplate' => url('/jobs') . '?search={search_term_string}'
                    ],
                    'query-input' => 'required name=search_term_string'
                ]
            ]
        ];
    }
    
    public static function reset()
    {
        self::$title = null;
        self::$description = null;
        self::$keywords = null;
        self::$image = null;
        self::$url = null;
        self::$type = 'website';
        self::$structuredData = [];
    }
}