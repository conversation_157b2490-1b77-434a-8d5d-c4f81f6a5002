<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Dropdowns</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-2xl font-bold mb-8">Dropdown Functionality Test</h1>
        
        <!-- Test User Dropdown -->
        <div class="bg-white p-6 rounded-lg shadow mb-6">
            <h2 class="text-lg font-semibold mb-4">User Actions Dropdown Test</h2>
            <div class="relative inline-block">
                <button type="button" onclick="toggleUserActions(123)" class="inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200">
                    User Actions
                    <svg class="ml-1 -mr-1 h-3 w-3 transition-transform duration-200" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
                    </svg>
                </button>
                <div id="userActions123" class="hidden absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-xl bg-white py-1 shadow-xl ring-1 ring-black ring-opacity-5 focus:outline-none border border-gray-100">
                    <a href="#" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-700 transition-colors duration-150">
                        <svg class="w-4 h-4 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                        </svg>
                        View Details
                    </a>
                    <button type="button" onclick="alert('Suspend clicked')" class="flex items-center w-full px-4 py-2 text-sm text-red-700 hover:bg-red-50 hover:text-red-800 transition-colors duration-150">
                        <svg class="w-4 h-4 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728"></path>
                        </svg>
                        Suspend User
                    </button>
                </div>
            </div>
        </div>

        <!-- Test Job Dropdown -->
        <div class="bg-white p-6 rounded-lg shadow mb-6">
            <h2 class="text-lg font-semibold mb-4">Job Actions Dropdown Test</h2>
            <div class="relative inline-block">
                <button type="button" onclick="toggleJobActions(456)" class="inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200">
                    Job Actions
                    <svg class="ml-1 -mr-1 h-3 w-3 transition-transform duration-200" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
                    </svg>
                </button>
                <div id="jobActions456" class="hidden absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-xl bg-white py-1 shadow-xl ring-1 ring-black ring-opacity-5 focus:outline-none border border-gray-100">
                    <a href="#" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-700 transition-colors duration-150">
                        <svg class="w-4 h-4 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                        </svg>
                        View Details
                    </a>
                    <button type="button" onclick="alert('Approve clicked')" class="flex items-center w-full px-4 py-2 text-sm text-green-700 hover:bg-green-50 hover:text-green-800 transition-colors duration-150">
                        <svg class="w-4 h-4 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        Approve Job
                    </button>
                </div>
            </div>
        </div>

        <!-- Debug Info -->
        <div class="bg-yellow-50 p-4 rounded-lg">
            <h3 class="font-semibold mb-2">Debug Information:</h3>
            <p class="text-sm text-gray-600">Check the browser console for debug messages when clicking buttons.</p>
        </div>
    </div>

    <script>
        // Global dropdown toggle functions
        window.toggleUserActions = function(userId) {
            console.log('Toggling dropdown for user:', userId);
            
            const dropdown = document.getElementById('userActions' + userId);
            const allDropdowns = document.querySelectorAll('[id^="userActions"]');
            
            if (!dropdown) {
                console.error('Dropdown not found for user:', userId);
                return;
            }
            
            // Close all other dropdowns first
            allDropdowns.forEach(dd => {
                if (dd.id !== 'userActions' + userId) {
                    dd.classList.add('hidden');
                }
            });
            
            // Toggle current dropdown
            dropdown.classList.toggle('hidden');
            
            console.log('Dropdown visibility:', !dropdown.classList.contains('hidden'));
        };

        window.toggleJobActions = function(jobId) {
            console.log('Toggling dropdown for job:', jobId);
            
            const dropdown = document.getElementById('jobActions' + jobId);
            const allDropdowns = document.querySelectorAll('[id^="jobActions"]');
            
            if (!dropdown) {
                console.error('Dropdown not found for job:', jobId);
                return;
            }
            
            // Close all other dropdowns first
            allDropdowns.forEach(dd => {
                if (dd.id !== 'jobActions' + jobId) {
                    dd.classList.add('hidden');
                }
            });
            
            // Toggle current dropdown
            dropdown.classList.toggle('hidden');
            
            console.log('Dropdown visibility:', !dropdown.classList.contains('hidden'));
        };

        // Close dropdowns when clicking outside
        document.addEventListener('click', function(event) {
            const isUserDropdownButton = event.target.closest('button[onclick*="toggleUserActions"]');
            const isUserDropdownMenu = event.target.closest('[id^="userActions"]');
            const isJobDropdownButton = event.target.closest('button[onclick*="toggleJobActions"]');
            const isJobDropdownMenu = event.target.closest('[id^="jobActions"]');
            
            if (!isUserDropdownButton && !isUserDropdownMenu) {
                const allUserDropdowns = document.querySelectorAll('[id^="userActions"]');
                allUserDropdowns.forEach(dd => dd.classList.add('hidden'));
            }
            
            if (!isJobDropdownButton && !isJobDropdownMenu) {
                const allJobDropdowns = document.querySelectorAll('[id^="jobActions"]');
                allJobDropdowns.forEach(dd => dd.classList.add('hidden'));
            }
        });

        console.log('Test page loaded. Functions available:', {
            toggleUserActions: typeof window.toggleUserActions,
            toggleJobActions: typeof window.toggleJobActions
        });
    </script>
</body>
</html>
<?php /**PATH C:\Users\<USER>\Desktop\laravel\rectra\resources\views/admin/test-dropdowns.blade.php ENDPATH**/ ?>