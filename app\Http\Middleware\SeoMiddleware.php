<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use App\Helpers\SeoHelper;
use Symfony\Component\HttpFoundation\Response;

class SeoMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);

        // Only process HTML responses
        if (!$this->shouldProcessResponse($response)) {
            return $response;
        }

        // Set default SEO if none was set
        $this->setDefaultSeoIfNeeded($request);

        return $response;
    }

    /**
     * Check if the response should be processed for SEO.
     */
    private function shouldProcessResponse($response): bool
    {
        return $response->getStatusCode() === 200 &&
               str_contains($response->headers->get('Content-Type', ''), 'text/html');
    }

    /**
     * Set default SEO settings if none were explicitly set.
     */
    private function setDefaultSeoIfNeeded(Request $request): void
    {
        // Check if SEO was already set by checking if title is still default
        $currentTitle = SeoHelper::getTitle();
        $defaultTitle = config('seo.defaults.title');

        if ($currentTitle === $defaultTitle || empty($currentTitle)) {
            $this->setPageSpecificSeo($request);
        }
    }

    /**
     * Set SEO based on the current route/page.
     */
    private function setPageSpecificSeo(Request $request): void
    {
        $route = $request->route();
        
        if (!$route) {
            return;
        }

        $routeName = $route->getName();
        $path = $request->path();

        // Handle different route patterns
        switch (true) {
            case str_starts_with($routeName, 'jobs.'):
                $this->setJobsSeo($routeName, $request);
                break;
                
            case str_starts_with($routeName, 'courses.'):
                $this->setCoursesSeo($routeName, $request);
                break;
                
            case str_starts_with($routeName, 'companies.'):
                $this->setCompaniesSeo($routeName, $request);
                break;
                
            case $routeName === 'pricing':
                $this->setPricingSeo();
                break;
                
            case $routeName === 'contact':
                $this->setContactSeo();
                break;
                
            case $routeName === 'about':
                $this->setAboutSeo();
                break;
                
            default:
                $this->setGenericSeo($path);
        }
    }

    /**
     * Set SEO for jobs pages.
     */
    private function setJobsSeo(string $routeName, Request $request): void
    {
        if ($routeName === 'jobs.index') {
            SeoHelper::setTitle('Find Jobs | Rectra')
                ->setDescription('Browse thousands of job opportunities across various industries and locations.')
                ->setKeywords(['jobs', 'careers', 'employment', 'job search', 'hiring']);
        }
    }

    /**
     * Set SEO for courses pages.
     */
    private function setCoursesSeo(string $routeName, Request $request): void
    {
        if ($routeName === 'courses.index') {
            SeoHelper::setTitle('Online Courses | Rectra')
                ->setDescription('Discover professional training courses to advance your career and learn new skills.')
                ->setKeywords(['courses', 'training', 'education', 'learning', 'skills']);
        }
    }

    /**
     * Set SEO for companies pages.
     */
    private function setCompaniesSeo(string $routeName, Request $request): void
    {
        if ($routeName === 'companies.index') {
            SeoHelper::setTitle('Companies | Rectra')
                ->setDescription('Explore top companies and discover career opportunities with leading employers.')
                ->setKeywords(['companies', 'employers', 'careers', 'jobs', 'workplace']);
        }
    }

    /**
     * Set SEO for pricing page.
     */
    private function setPricingSeo(): void
    {
        SeoHelper::setTitle('Pricing Plans | Rectra')
            ->setDescription('Choose the perfect plan for your career goals. Affordable pricing for job seekers and employers.')
            ->setKeywords(['pricing', 'plans', 'subscription', 'cost', 'affordable']);
    }

    /**
     * Set SEO for contact page.
     */
    private function setContactSeo(): void
    {
        SeoHelper::setTitle('Contact Us | Rectra')
            ->setDescription('Get in touch with Rectra. We\'re here to help with your job search and career development needs.')
            ->setKeywords(['contact', 'support', 'help', 'customer service']);
    }

    /**
     * Set SEO for about page.
     */
    private function setAboutSeo(): void
    {
        SeoHelper::setTitle('About Rectra | Modern Job Board & Training Platform')
            ->setDescription('Learn about Rectra\'s mission to connect talent with opportunity through our modern job board and professional training platform.')
            ->setKeywords(['about', 'company', 'mission', 'team', 'job board', 'training platform']);
    }

    /**
     * Set generic SEO based on path.
     */
    private function setGenericSeo(string $path): void
    {
        $title = ucwords(str_replace(['/', '-', '_'], [' ', ' ', ' '], $path));
        
        if (empty($title) || $title === ' ') {
            $title = 'Home';
        }
        
        SeoHelper::setTitle($title . ' | Rectra')
            ->setDescription(config('seo.defaults.description'))
            ->setKeywords(config('seo.defaults.keywords'));
    }
}