<?php

namespace App\Console\Commands;

use App\Models\SavedSearch;
use App\Models\Job;
use App\Notifications\JobAlert;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class SendJobAlerts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'job-alerts:send {--frequency=daily : The frequency to send alerts (daily, weekly)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send job alerts to users based on their saved searches';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $frequency = $this->option('frequency');
        $this->info("Starting job alerts for {$frequency} frequency...");
        
        // Get the date range based on frequency
        $dateFrom = $this->getDateFrom($frequency);
        
        // Get all saved searches that have alerts enabled
        $savedSearches = SavedSearch::with('user')
            ->whereHas('user', function ($query) {
                $query->where('job_alerts_enabled', true);
            })
            ->get();
        
        $totalAlerts = 0;
        $totalUsers = 0;
        
        foreach ($savedSearches as $savedSearch) {
            try {
                $matchingJobs = $this->findMatchingJobs($savedSearch, $dateFrom);
                
                if ($matchingJobs->count() > 0) {
                    // Send notification to user
                    $savedSearch->user->notify(new JobAlert($matchingJobs, $savedSearch));
                    
                    $totalAlerts += $matchingJobs->count();
                    $totalUsers++;
                    
                    $this->info("Sent {$matchingJobs->count()} job alerts to {$savedSearch->user->email}");
                    
                    // Update last sent timestamp
                    $savedSearch->update([
                        'last_alert_sent' => now()
                    ]);
                }
            } catch (\Exception $e) {
                Log::error("Failed to send job alert for saved search {$savedSearch->id}: " . $e->getMessage());
                $this->error("Failed to send alert for user {$savedSearch->user->email}: " . $e->getMessage());
            }
        }
        
        $this->info("Job alerts completed. Sent {$totalAlerts} job alerts to {$totalUsers} users.");
        
        return Command::SUCCESS;
    }
    
    /**
     * Get the date from based on frequency
     */
    private function getDateFrom($frequency)
    {
        switch ($frequency) {
            case 'weekly':
                return Carbon::now()->subWeek();
            case 'daily':
            default:
                return Carbon::now()->subDay();
        }
    }
    
    /**
     * Find matching jobs for a saved search
     */
    private function findMatchingJobs(SavedSearch $savedSearch, Carbon $dateFrom)
    {
        $filters = $savedSearch->filters;
        
        $query = Job::query()
            ->with(['employer.user', 'employer.company'])
            ->where('status', 'active')
            ->where('expires_at', '>', now())
            ->where('created_at', '>=', $dateFrom);
        
        // Apply search filters
        if (!empty($filters['search'])) {
            $query->where(function ($q) use ($filters) {
                $q->where('title', 'like', '%' . $filters['search'] . '%')
                  ->orWhere('description', 'like', '%' . $filters['search'] . '%')
                  ->orWhere('requirements', 'like', '%' . $filters['search'] . '%');
            });
        }
        
        // Apply location filter
        if (!empty($filters['location'])) {
            $query->where('location', 'like', '%' . $filters['location'] . '%');
        }
        
        // Apply job type filter
        if (!empty($filters['job_type'])) {
            $query->where('job_type', $filters['job_type']);
        }
        
        // Apply minimum salary filter
        if (!empty($filters['min_salary'])) {
            $query->where('salary_min', '>=', $filters['min_salary']);
        }
        
        // Apply remote filter
        if (!empty($filters['remote']) && $filters['remote']) {
            $query->where('remote', true);
        }
        
        // Apply featured filter
        if (!empty($filters['featured']) && $filters['featured']) {
            $query->where('featured', true);
        }
        
        return $query->orderBy('created_at', 'desc')->get();
    }
}
