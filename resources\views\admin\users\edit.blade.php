@extends('layouts.admin')

@section('title', 'Edit User')

@section('content')
<div class="max-w-4xl mx-auto">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Edit User</h1>
                <p class="mt-2 text-gray-600">Update user information and permissions</p>
            </div>
            <a href="{{ route('admin.users.index') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Back to Users
            </a>
        </div>
    </div>

    <!-- User Edit Form -->
    <div class="bg-white shadow-sm rounded-lg">
        <form action="{{ route('admin.users.update', $user) }}" method="POST" class="space-y-6 p-6">
            @csrf
            @method('PUT')

            <!-- Basic Information -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Full Name</label>
                    <input type="text" id="name" name="name" value="{{ old('name', $user->name) }}" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500" required>
                    @error('name')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
                    <input type="email" id="email" name="email" value="{{ old('email', $user->email) }}" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500" required>
                    @error('email')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Role and Status -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="role" class="block text-sm font-medium text-gray-700 mb-2">Role</label>
                    <select id="role" name="role" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500">
                        <option value="job_seeker" {{ $user->role === 'job_seeker' ? 'selected' : '' }}>Job Seeker</option>
                        <option value="employer" {{ $user->role === 'employer' ? 'selected' : '' }}>Employer</option>
                        <option value="admin" {{ $user->role === 'admin' ? 'selected' : '' }}>Admin</option>
                    </select>
                    @error('role')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-2">Account Status</label>
                    <select id="status" name="status" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500">
                        <option value="active" {{ !$user->suspended_at ? 'selected' : '' }}>Active</option>
                        <option value="suspended" {{ $user->suspended_at ? 'selected' : '' }}>Suspended</option>
                    </select>
                </div>
            </div>

            <!-- Email Verification -->
            <div class="flex items-center">
                <input type="checkbox" id="email_verified" name="email_verified" value="1" 
                       {{ $user->email_verified_at ? 'checked' : '' }}
                       class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                <label for="email_verified" class="ml-2 block text-sm text-gray-700">
                    Email Verified
                </label>
            </div>

            <!-- Action Buttons -->
            <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
                <a href="{{ route('admin.users.show', $user) }}" class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                    Cancel
                </a>
                <button type="submit" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500">
                    Update User
                </button>
            </div>
        </form>
    </div>

    <!-- User Statistics -->
    <div class="mt-8 bg-white shadow-sm rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">User Statistics</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="text-center">
                <div class="text-2xl font-bold text-purple-600">{{ $user->created_at->format('M d, Y') }}</div>
                <div class="text-sm text-gray-500">Member Since</div>
            </div>
            <div class="text-center">
                <div class="text-2xl font-bold text-purple-600">{{ $user->last_login_at ? $user->last_login_at->diffForHumans() : 'Never' }}</div>
                <div class="text-sm text-gray-500">Last Login</div>
            </div>
            <div class="text-center">
                <div class="text-2xl font-bold text-purple-600">
                    @if($user->role === 'job_seeker')
                        {{ $user->jobApplications()->count() }}
                    @elseif($user->role === 'employer')
                        {{ $user->employerProfile?->jobs()->count() ?? 0 }}
                    @else
                        N/A
                    @endif
                </div>
                <div class="text-sm text-gray-500">
                    @if($user->role === 'job_seeker')
                        Applications
                    @elseif($user->role === 'employer')
                        Jobs Posted
                    @else
                        Admin Actions
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
