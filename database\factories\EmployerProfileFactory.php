<?php

namespace Database\Factories;

use App\Models\EmployerProfile;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\EmployerProfile>
 */
class EmployerProfileFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $companyName = fake()->company();
        $industries = [
            'Technology', 'Healthcare', 'Finance', 'Education', 'Manufacturing',
            'Retail', 'Marketing', 'Consulting', 'Real Estate', 'Transportation',
            'Media', 'Gaming', 'E-commerce', 'Telecommunications', 'Energy',
            'Automotive', 'Aerospace', 'Biotechnology', 'Construction', 'Entertainment'
        ];

        $companySizes = ['1-10', '11-50', '51-200', '201-500', '501-1000', '1000+'];

        return [
            'user_id' => User::factory(),
            'company_name' => $companyName,
            'description' => fake()->paragraph(4),
            'logo_path' => null, // Will be handled by media library if needed
            'banner_path' => null, // Will be handled by media library if needed
            'website' => 'https://www.' . strtolower(str_replace([' ', '.', ','], ['', '', ''], $companyName)) . '.com',
            'industry' => fake()->randomElement($industries),
            'company_size' => fake()->randomElement($companySizes),
            'location' => fake()->city() . ', ' . fake()->stateAbbr(),
            'phone_number' => fake()->phoneNumber(),
            'linkedin_url' => 'https://linkedin.com/company/' . strtolower(str_replace([' ', '.', ','], ['-', '', ''], $companyName)),
            'is_verified' => fake()->boolean(40),
        ];
    }

    /**
     * Indicate that the employer profile is verified.
     */
    public function verified(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_verified' => true,
        ]);
    }

    /**
     * Indicate that the employer profile is unverified.
     */
    public function unverified(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_verified' => false,
        ]);
    }

    /**
     * Indicate that the employer is a tech company.
     */
    public function techCompany(): static
    {
        return $this->state(fn (array $attributes) => [
            'industry' => 'Technology',
            'description' => 'We are a leading technology company focused on innovation and cutting-edge solutions. Our team is passionate about creating products that make a difference in the world.',
        ]);
    }

    /**
     * Indicate that the employer is a startup.
     */
    public function startup(): static
    {
        return $this->state(fn (array $attributes) => [
            'company_size' => fake()->randomElement(['1-10', '11-50']),
            'description' => 'We are an exciting startup disrupting the industry with innovative solutions. Join our dynamic team and help us build the future.',
        ]);
    }

    /**
     * Indicate that the employer is a large enterprise.
     */
    public function enterprise(): static
    {
        return $this->state(fn (array $attributes) => [
            'company_size' => '1000+',
            'is_verified' => true,
            'description' => 'We are a well-established enterprise with a global presence. Our company offers stability, growth opportunities, and comprehensive benefits to our employees.',
        ]);
    }
}