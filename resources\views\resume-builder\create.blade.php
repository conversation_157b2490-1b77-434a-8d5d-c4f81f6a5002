@extends('layouts.app')

@section('title', 'Create Resume')

@section('content')
<div class="min-h-screen bg-gray-50 py-8">
    <div class="mx-auto max-w-4xl px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center space-x-4">
                <a href="{{ route('resume-builder.index') }}" class="text-gray-500 hover:text-gray-700">
                    <svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M10.5 19.5L3 12m0 0l7.5-7.5M3 12h18" />
                    </svg>
                </a>
                <div>
                    <h1 class="text-3xl font-bold leading-tight tracking-tight text-gray-900">Create Resume</h1>
                    <p class="mt-2 text-lg text-gray-600">Build your professional resume step by step</p>
                </div>
            </div>
        </div>

        <!-- Progress Steps -->
        <div class="mb-8">
            <nav aria-label="Progress">
                <ol role="list" class="flex items-center">
                    <li class="relative pr-8 sm:pr-20">
                        <div class="absolute inset-0 flex items-center" aria-hidden="true">
                            <div class="h-0.5 w-full bg-gray-200"></div>
                        </div>
                        <a href="#" class="relative flex h-8 w-8 items-center justify-center rounded-full bg-blue-600 hover:bg-blue-900">
                            <span class="text-white text-sm font-medium">1</span>
                        </a>
                        <span class="absolute top-10 left-1/2 transform -translate-x-1/2 text-xs text-gray-500">Template</span>
                    </li>
                    
                    <li class="relative pr-8 sm:pr-20">
                        <div class="absolute inset-0 flex items-center" aria-hidden="true">
                            <div class="h-0.5 w-full bg-gray-200"></div>
                        </div>
                        <a href="#" class="relative flex h-8 w-8 items-center justify-center rounded-full border-2 border-gray-300 bg-white hover:border-gray-400">
                            <span class="text-gray-500 text-sm font-medium">2</span>
                        </a>
                        <span class="absolute top-10 left-1/2 transform -translate-x-1/2 text-xs text-gray-500">Personal Info</span>
                    </li>
                    
                    <li class="relative pr-8 sm:pr-20">
                        <div class="absolute inset-0 flex items-center" aria-hidden="true">
                            <div class="h-0.5 w-full bg-gray-200"></div>
                        </div>
                        <a href="#" class="relative flex h-8 w-8 items-center justify-center rounded-full border-2 border-gray-300 bg-white hover:border-gray-400">
                            <span class="text-gray-500 text-sm font-medium">3</span>
                        </a>
                        <span class="absolute top-10 left-1/2 transform -translate-x-1/2 text-xs text-gray-500">Experience</span>
                    </li>
                    
                    <li class="relative">
                        <a href="#" class="relative flex h-8 w-8 items-center justify-center rounded-full border-2 border-gray-300 bg-white hover:border-gray-400">
                            <span class="text-gray-500 text-sm font-medium">4</span>
                        </a>
                        <span class="absolute top-10 left-1/2 transform -translate-x-1/2 text-xs text-gray-500">Review</span>
                    </li>
                </ol>
            </nav>
        </div>

        <!-- Template Selection -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-5 border-b border-gray-200">
                <h3 class="text-lg leading-6 font-medium text-gray-900">Choose a Template</h3>
                <p class="mt-1 text-sm text-gray-500">Select a professional template that matches your style</p>
            </div>
            <div class="px-6 py-5">
                <form method="POST" action="{{ route('resume-builder.store') }}" id="templateForm">
                    @csrf
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                        <!-- Modern Template -->
                        <div class="template-option group cursor-pointer" data-template="modern">
                            <input type="radio" name="template" value="modern" class="sr-only" id="template-modern">
                            <label for="template-modern" class="block">
                                <div class="aspect-[3/4] bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg border-2 border-gray-200 group-hover:border-blue-300 transition-all duration-200 flex items-center justify-center relative overflow-hidden">
                                    <!-- Template Preview -->
                                    <div class="w-full h-full p-4 flex flex-col">
                                        <div class="bg-blue-600 h-16 w-full rounded-t-lg mb-3 flex items-center justify-center">
                                            <div class="w-8 h-8 bg-white rounded-full"></div>
                                        </div>
                                        <div class="space-y-2 flex-1">
                                            <div class="h-2 bg-blue-300 rounded w-3/4"></div>
                                            <div class="h-1 bg-blue-200 rounded w-1/2"></div>
                                            <div class="h-1 bg-blue-200 rounded w-2/3"></div>
                                            <div class="mt-3 space-y-1">
                                                <div class="h-1 bg-gray-300 rounded w-full"></div>
                                                <div class="h-1 bg-gray-300 rounded w-5/6"></div>
                                                <div class="h-1 bg-gray-300 rounded w-4/5"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- Selection Indicator -->
                                    <div class="absolute top-2 right-2 w-6 h-6 bg-white rounded-full border-2 border-gray-300 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                                        <div class="w-3 h-3 bg-blue-600 rounded-full hidden"></div>
                                    </div>
                                </div>
                                <div class="mt-3 text-center">
                                    <h4 class="text-sm font-medium text-gray-900">Modern</h4>
                                    <p class="text-xs text-gray-500">Clean and contemporary design</p>
                                </div>
                            </label>
                        </div>

                        <!-- Classic Template -->
                        <div class="template-option group cursor-pointer" data-template="classic">
                            <input type="radio" name="template" value="classic" class="sr-only" id="template-classic">
                            <label for="template-classic" class="block">
                                <div class="aspect-[3/4] bg-gradient-to-br from-green-50 to-green-100 rounded-lg border-2 border-gray-200 group-hover:border-green-300 transition-all duration-200 flex items-center justify-center relative overflow-hidden">
                                    <!-- Template Preview -->
                                    <div class="w-full h-full p-4 flex flex-col">
                                        <div class="text-center mb-4">
                                            <div class="h-3 bg-green-600 rounded w-2/3 mx-auto mb-2"></div>
                                            <div class="h-1 bg-green-400 rounded w-1/2 mx-auto"></div>
                                        </div>
                                        <div class="space-y-3 flex-1">
                                            <div class="border-b border-green-200 pb-2">
                                                <div class="h-1 bg-green-500 rounded w-1/3 mb-1"></div>
                                                <div class="space-y-1">
                                                    <div class="h-1 bg-gray-300 rounded w-full"></div>
                                                    <div class="h-1 bg-gray-300 rounded w-4/5"></div>
                                                </div>
                                            </div>
                                            <div class="border-b border-green-200 pb-2">
                                                <div class="h-1 bg-green-500 rounded w-1/4 mb-1"></div>
                                                <div class="space-y-1">
                                                    <div class="h-1 bg-gray-300 rounded w-full"></div>
                                                    <div class="h-1 bg-gray-300 rounded w-3/4"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- Selection Indicator -->
                                    <div class="absolute top-2 right-2 w-6 h-6 bg-white rounded-full border-2 border-gray-300 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                                        <div class="w-3 h-3 bg-green-600 rounded-full hidden"></div>
                                    </div>
                                </div>
                                <div class="mt-3 text-center">
                                    <h4 class="text-sm font-medium text-gray-900">Classic</h4>
                                    <p class="text-xs text-gray-500">Traditional and professional</p>
                                </div>
                            </label>
                        </div>

                        <!-- Creative Template -->
                        <div class="template-option group cursor-pointer" data-template="creative">
                            <input type="radio" name="template" value="creative" class="sr-only" id="template-creative">
                            <label for="template-creative" class="block">
                                <div class="aspect-[3/4] bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg border-2 border-gray-200 group-hover:border-purple-300 transition-all duration-200 flex items-center justify-center relative overflow-hidden">
                                    <!-- Template Preview -->
                                    <div class="w-full h-full p-4 flex">
                                        <div class="w-1/3 bg-purple-600 rounded-l-lg p-2 mr-2">
                                            <div class="w-6 h-6 bg-white rounded-full mx-auto mb-2"></div>
                                            <div class="space-y-1">
                                                <div class="h-1 bg-purple-300 rounded w-full"></div>
                                                <div class="h-1 bg-purple-300 rounded w-3/4"></div>
                                                <div class="h-1 bg-purple-300 rounded w-5/6"></div>
                                            </div>
                                        </div>
                                        <div class="flex-1 space-y-2">
                                            <div class="h-2 bg-purple-400 rounded w-3/4"></div>
                                            <div class="h-1 bg-gray-300 rounded w-full"></div>
                                            <div class="h-1 bg-gray-300 rounded w-5/6"></div>
                                            <div class="h-1 bg-gray-300 rounded w-4/5"></div>
                                            <div class="mt-3">
                                                <div class="h-1 bg-purple-300 rounded w-1/2 mb-1"></div>
                                                <div class="h-1 bg-gray-300 rounded w-full"></div>
                                                <div class="h-1 bg-gray-300 rounded w-3/4"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- Selection Indicator -->
                                    <div class="absolute top-2 right-2 w-6 h-6 bg-white rounded-full border-2 border-gray-300 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                                        <div class="w-3 h-3 bg-purple-600 rounded-full hidden"></div>
                                    </div>
                                </div>
                                <div class="mt-3 text-center">
                                    <h4 class="text-sm font-medium text-gray-900">Creative</h4>
                                    <p class="text-xs text-gray-500">Bold and eye-catching design</p>
                                </div>
                            </label>
                        </div>

                        <!-- Minimal Template -->
                        <div class="template-option group cursor-pointer" data-template="minimal">
                            <input type="radio" name="template" value="minimal" class="sr-only" id="template-minimal">
                            <label for="template-minimal" class="block">
                                <div class="aspect-[3/4] bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg border-2 border-gray-200 group-hover:border-gray-400 transition-all duration-200 flex items-center justify-center relative overflow-hidden">
                                    <!-- Template Preview -->
                                    <div class="w-full h-full p-4 flex flex-col">
                                        <div class="text-left mb-4">
                                            <div class="h-3 bg-gray-800 rounded w-1/2 mb-1"></div>
                                            <div class="h-1 bg-gray-500 rounded w-1/3"></div>
                                        </div>
                                        <div class="space-y-4 flex-1">
                                            <div>
                                                <div class="h-1 bg-gray-600 rounded w-1/4 mb-2"></div>
                                                <div class="space-y-1">
                                                    <div class="h-1 bg-gray-400 rounded w-full"></div>
                                                    <div class="h-1 bg-gray-400 rounded w-5/6"></div>
                                                    <div class="h-1 bg-gray-400 rounded w-4/5"></div>
                                                </div>
                                            </div>
                                            <div>
                                                <div class="h-1 bg-gray-600 rounded w-1/3 mb-2"></div>
                                                <div class="space-y-1">
                                                    <div class="h-1 bg-gray-400 rounded w-full"></div>
                                                    <div class="h-1 bg-gray-400 rounded w-3/4"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- Selection Indicator -->
                                    <div class="absolute top-2 right-2 w-6 h-6 bg-white rounded-full border-2 border-gray-300 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                                        <div class="w-3 h-3 bg-gray-600 rounded-full hidden"></div>
                                    </div>
                                </div>
                                <div class="mt-3 text-center">
                                    <h4 class="text-sm font-medium text-gray-900">Minimal</h4>
                                    <p class="text-xs text-gray-500">Simple and elegant layout</p>
                                </div>
                            </label>
                        </div>

                        <!-- Executive Template -->
                        <div class="template-option group cursor-pointer" data-template="executive">
                            <input type="radio" name="template" value="executive" class="sr-only" id="template-executive">
                            <label for="template-executive" class="block">
                                <div class="aspect-[3/4] bg-gradient-to-br from-indigo-50 to-indigo-100 rounded-lg border-2 border-gray-200 group-hover:border-indigo-300 transition-all duration-200 flex items-center justify-center relative overflow-hidden">
                                    <!-- Template Preview -->
                                    <div class="w-full h-full p-4 flex flex-col">
                                        <div class="bg-indigo-600 h-8 w-full rounded mb-3 flex items-center px-2">
                                            <div class="w-4 h-4 bg-white rounded-full mr-2"></div>
                                            <div class="h-1 bg-indigo-200 rounded flex-1"></div>
                                        </div>
                                        <div class="grid grid-cols-3 gap-2 mb-3">
                                            <div class="h-1 bg-indigo-300 rounded"></div>
                                            <div class="h-1 bg-indigo-300 rounded"></div>
                                            <div class="h-1 bg-indigo-300 rounded"></div>
                                        </div>
                                        <div class="space-y-2 flex-1">
                                            <div class="h-1 bg-indigo-400 rounded w-1/3"></div>
                                            <div class="space-y-1">
                                                <div class="h-1 bg-gray-300 rounded w-full"></div>
                                                <div class="h-1 bg-gray-300 rounded w-4/5"></div>
                                                <div class="h-1 bg-gray-300 rounded w-5/6"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- Selection Indicator -->
                                    <div class="absolute top-2 right-2 w-6 h-6 bg-white rounded-full border-2 border-gray-300 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                                        <div class="w-3 h-3 bg-indigo-600 rounded-full hidden"></div>
                                    </div>
                                </div>
                                <div class="mt-3 text-center">
                                    <h4 class="text-sm font-medium text-gray-900">Executive</h4>
                                    <p class="text-xs text-gray-500">Professional and sophisticated</p>
                                </div>
                            </label>
                        </div>

                        <!-- Tech Template -->
                        <div class="template-option group cursor-pointer" data-template="tech">
                            <input type="radio" name="template" value="tech" class="sr-only" id="template-tech">
                            <label for="template-tech" class="block">
                                <div class="aspect-[3/4] bg-gradient-to-br from-cyan-50 to-cyan-100 rounded-lg border-2 border-gray-200 group-hover:border-cyan-300 transition-all duration-200 flex items-center justify-center relative overflow-hidden">
                                    <!-- Template Preview -->
                                    <div class="w-full h-full p-4 flex flex-col">
                                        <div class="flex items-center mb-3">
                                            <div class="w-6 h-6 bg-cyan-600 rounded mr-2"></div>
                                            <div class="flex-1">
                                                <div class="h-2 bg-cyan-500 rounded w-2/3 mb-1"></div>
                                                <div class="h-1 bg-cyan-300 rounded w-1/2"></div>
                                            </div>
                                        </div>
                                        <div class="grid grid-cols-2 gap-2 mb-3">
                                            <div class="space-y-1">
                                                <div class="h-1 bg-cyan-400 rounded w-3/4"></div>
                                                <div class="h-1 bg-gray-300 rounded w-full"></div>
                                                <div class="h-1 bg-gray-300 rounded w-5/6"></div>
                                            </div>
                                            <div class="space-y-1">
                                                <div class="h-1 bg-cyan-400 rounded w-2/3"></div>
                                                <div class="h-1 bg-gray-300 rounded w-full"></div>
                                                <div class="h-1 bg-gray-300 rounded w-4/5"></div>
                                            </div>
                                        </div>
                                        <div class="space-y-1 flex-1">
                                            <div class="h-1 bg-cyan-400 rounded w-1/3"></div>
                                            <div class="h-1 bg-gray-300 rounded w-full"></div>
                                            <div class="h-1 bg-gray-300 rounded w-3/4"></div>
                                        </div>
                                    </div>
                                    <!-- Selection Indicator -->
                                    <div class="absolute top-2 right-2 w-6 h-6 bg-white rounded-full border-2 border-gray-300 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                                        <div class="w-3 h-3 bg-cyan-600 rounded-full hidden"></div>
                                    </div>
                                </div>
                                <div class="mt-3 text-center">
                                    <h4 class="text-sm font-medium text-gray-900">Tech</h4>
                                    <p class="text-xs text-gray-500">Perfect for IT professionals</p>
                                </div>
                            </label>
                        </div>
                    </div>

                    <!-- Resume Title -->
                    <div class="mt-8">
                        <label for="title" class="block text-sm font-medium text-gray-700 mb-2">
                            Resume Title
                        </label>
                        <input type="text" name="title" id="title" placeholder="e.g., Software Engineer Resume" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <p class="mt-1 text-xs text-gray-500">Give your resume a descriptive title for easy identification</p>
                    </div>

                    <!-- Action Buttons -->
                    <div class="mt-8 flex justify-between">
                        <a href="{{ route('resume-builder.index') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <svg class="-ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M10.5 19.5L3 12m0 0l7.5-7.5M3 12h18" />
                            </svg>
                            Cancel
                        </a>
                        <button type="submit" id="continueBtn" disabled class="inline-flex items-center px-6 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:bg-gray-300 disabled:cursor-not-allowed">
                            Continue
                            <svg class="ml-2 -mr-1 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M13.5 4.5L21 12m0 0l-7.5 7.5M21 12H3" />
                            </svg>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const templateOptions = document.querySelectorAll('.template-option');
    const continueBtn = document.getElementById('continueBtn');
    const templateInputs = document.querySelectorAll('input[name="template"]');
    
    templateOptions.forEach(option => {
        option.addEventListener('click', function() {
            // Remove selection from all templates
            templateOptions.forEach(opt => {
                opt.classList.remove('selected');
                const indicator = opt.querySelector('.absolute .w-3');
                const border = opt.querySelector('.aspect-\\[3\/4\\]');
                indicator.classList.add('hidden');
                border.classList.remove('border-blue-500', 'border-green-500', 'border-purple-500', 'border-gray-500', 'border-indigo-500', 'border-cyan-500');
                border.classList.add('border-gray-200');
            });
            
            // Add selection to clicked template
            this.classList.add('selected');
            const indicator = this.querySelector('.absolute .w-3');
            const border = this.querySelector('.aspect-\\[3\/4\\]');
            const template = this.dataset.template;
            
            indicator.classList.remove('hidden');
            border.classList.remove('border-gray-200');
            
            // Add appropriate border color
            switch(template) {
                case 'modern':
                    border.classList.add('border-blue-500');
                    break;
                case 'classic':
                    border.classList.add('border-green-500');
                    break;
                case 'creative':
                    border.classList.add('border-purple-500');
                    break;
                case 'minimal':
                    border.classList.add('border-gray-500');
                    break;
                case 'executive':
                    border.classList.add('border-indigo-500');
                    break;
                case 'tech':
                    border.classList.add('border-cyan-500');
                    break;
            }
            
            // Check the radio button
            const radio = this.querySelector('input[type="radio"]');
            radio.checked = true;
            
            // Enable continue button
            continueBtn.disabled = false;
        });
    });
    
    // Handle radio button changes
    templateInputs.forEach(input => {
        input.addEventListener('change', function() {
            continueBtn.disabled = false;
        });
    });
});
</script>
@endsection