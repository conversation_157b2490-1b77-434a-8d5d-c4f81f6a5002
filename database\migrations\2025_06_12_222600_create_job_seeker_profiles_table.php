<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('job_seeker_profiles', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->text('summary')->nullable();
            $table->string('phone_number')->nullable();
            $table->string('location')->nullable();
            $table->string('website')->nullable();
            $table->string('linkedin_url')->nullable();
            $table->string('github_url')->nullable();
            $table->json('skills')->nullable();
            $table->enum('experience_level', ['Entry-level', 'Mid-level', 'Senior', 'Manager'])->nullable();
            $table->decimal('desired_salary_min', 10, 2)->nullable();
            $table->decimal('desired_salary_max', 10, 2)->nullable();
            $table->boolean('open_to_remote')->default(false);
            $table->boolean('profile_completed')->default(false);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('job_seeker_profiles');
    }
};