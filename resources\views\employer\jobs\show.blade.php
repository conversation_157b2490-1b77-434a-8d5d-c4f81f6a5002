@extends('layouts.app')

@section('title', 'Job Details')

@section('content')
<div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">{{ $job->title }}</h1>
                <p class="mt-2 text-gray-600">Posted {{ $job->created_at->diffForHumans() }}</p>
            </div>
            <div class="flex items-center space-x-3">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                    @if($job->status === 'active') bg-green-100 text-green-800
                    @elseif($job->status === 'paused') bg-yellow-100 text-yellow-800
                    @elseif($job->status === 'closed') bg-red-100 text-red-800
                    @else bg-gray-100 text-gray-800 @endif">
                    {{ ucfirst($job->status) }}
                </span>
                <a href="{{ route('employer.jobs.index') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Back to Jobs
                </a>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Job Details -->
            <div class="bg-white shadow-sm rounded-lg p-6">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">Job Details</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Location</label>
                        <p class="text-gray-900">{{ $job->location }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Job Type</label>
                        <p class="text-gray-900">{{ ucfirst($job->type) }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Experience Level</label>
                        <p class="text-gray-900">{{ ucfirst($job->experience_level) }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Category</label>
                        <p class="text-gray-900">{{ ucfirst($job->category) }}</p>
                    </div>
                </div>

                @if($job->salary_min || $job->salary_max)
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Salary Range</label>
                    <p class="text-gray-900">
                        ${{ number_format($job->salary_min) }} - ${{ number_format($job->salary_max) }}
                        @if($job->salary_currency) {{ $job->salary_currency }} @endif
                        @if($job->salary_period) per {{ $job->salary_period }} @endif
                    </p>
                </div>
                @endif

                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                    <div class="prose max-w-none text-gray-900">
                        {!! nl2br(e($job->description)) !!}
                    </div>
                </div>

                @if($job->requirements && trim($job->requirements) !== '')
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Requirements</label>
                    <div class="prose max-w-none text-gray-900">
                        {!! nl2br(e($job->requirements)) !!}
                    </div>
                </div>
                @endif

                @if($job->benefits && trim($job->benefits) !== '')
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Benefits</label>
                    <div class="prose max-w-none text-gray-900">
                        {!! nl2br(e($job->benefits)) !!}
                    </div>
                </div>
                @endif
            </div>

            <!-- Applications -->
            <div class="bg-white shadow-sm rounded-lg p-6">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-xl font-semibold text-gray-900">Applications ({{ $job->applications->count() }})</h2>
                    <a href="{{ route('employer.applications.job', $job) }}" class="text-purple-600 hover:text-purple-700 text-sm font-medium">
                        View All Applications →
                    </a>
                </div>

                @if($job->applications->count() > 0)
                    <div class="space-y-4">
                        @foreach($job->applications->take(5) as $application)
                            <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                                <div class="flex items-center space-x-4">
                                    <img class="h-10 w-10 rounded-full" src="{{ $application->user->avatar ?? 'https://ui-avatars.com/api/?name=' . urlencode($application->user->name) }}" alt="{{ $application->user->name }}">
                                    <div>
                                        <h4 class="text-sm font-medium text-gray-900">{{ $application->user->name }}</h4>
                                        <p class="text-sm text-gray-500">Applied {{ $application->created_at->diffForHumans() }}</p>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-3">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        @if($application->status === 'submitted') bg-blue-100 text-blue-800
                                        @elseif($application->status === 'viewed') bg-yellow-100 text-yellow-800
                                        @elseif($application->status === 'shortlisted') bg-green-100 text-green-800
                                        @elseif($application->status === 'rejected') bg-red-100 text-red-800
                                        @else bg-gray-100 text-gray-800 @endif">
                                        {{ ucfirst($application->status) }}
                                    </span>
                                    <a href="{{ route('employer.applications.show', $application) }}" class="text-purple-600 hover:text-purple-700 text-sm">
                                        View
                                    </a>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-8">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-gray-900">No applications yet</h3>
                        <p class="mt-1 text-sm text-gray-500">When candidates apply for this job, they'll appear here.</p>
                    </div>
                @endif
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Job Actions -->
            <div class="bg-white shadow-sm rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Actions</h3>
                <div class="space-y-3">
                    <a href="{{ route('employer.jobs.edit', $job) }}" class="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                        </svg>
                        Edit Job
                    </a>
                    
                    <form action="{{ route('employer.jobs.toggle-status', $job) }}" method="POST">
                        @csrf
                        <button type="submit" class="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                            @if($job->status === 'active')
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                Pause Job
                            @else
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                Activate Job
                            @endif
                        </button>
                    </form>
                    
                    <a href="{{ route('jobs.show', $job) }}" target="_blank" class="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                        </svg>
                        View Public Page
                    </a>
                </div>
            </div>

            <!-- Job Statistics -->
            <div class="bg-white shadow-sm rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Statistics</h3>
                <div class="space-y-4">
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-500">Total Views</span>
                        <span class="text-sm font-medium text-gray-900">{{ $job->views_count ?? 0 }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-500">Applications</span>
                        <span class="text-sm font-medium text-gray-900">{{ $job->applications->count() }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-500">Posted</span>
                        <span class="text-sm font-medium text-gray-900">{{ $job->created_at->format('M d, Y') }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-500">Expires</span>
                        <span class="text-sm font-medium text-gray-900">{{ $job->expires_at->format('M d, Y') }}</span>
                    </div>
                    @if($job->is_featured)
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-500">Featured</span>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                            Yes
                        </span>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Application Status Breakdown -->
            @if($job->applications->count() > 0)
            <div class="bg-white shadow-sm rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Application Status</h3>
                <div class="space-y-3">
                    @php
                        $statusCounts = $job->applications->groupBy('status')->map->count();
                    @endphp
                    @foreach(['submitted', 'viewed', 'in_review', 'shortlisted', 'interviewed', 'rejected', 'hired'] as $status)
                        @if($statusCounts->get($status, 0) > 0)
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-500">{{ ucfirst(str_replace('_', ' ', $status)) }}</span>
                            <span class="text-sm font-medium text-gray-900">{{ $statusCounts->get($status, 0) }}</span>
                        </div>
                        @endif
                    @endforeach
                </div>
            </div>
            @endif
        </div>
    </div>
</div>
@endsection
