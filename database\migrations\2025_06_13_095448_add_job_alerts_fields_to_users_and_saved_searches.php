<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add job alerts field to users table if it doesn't exist
        Schema::table('users', function (Blueprint $table) {
            if (!Schema::hasColumn('users', 'job_alerts_enabled')) {
                $table->boolean('job_alerts_enabled')->default(true)->after('email_verified_at');
            }
        });
        
        // The saved_searches table already has last_alert_sent column
        // No changes needed for saved_searches table
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            if (Schema::hasColumn('users', 'job_alerts_enabled')) {
                $table->dropColumn('job_alerts_enabled');
            }
        });
        
        // Don't drop last_alert_sent as it's part of the original saved_searches table
    }
};
