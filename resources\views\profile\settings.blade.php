@extends('layouts.app')

@section('title', 'Account Settings')

@section('content')
<div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Account Settings</h1>
        <p class="mt-2 text-gray-600">Manage your account preferences and security settings.</p>
    </div>

    <!-- Success Message -->
    @if(session('success'))
        <div class="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-green-800">{{ session('success') }}</p>
                </div>
            </div>
        </div>
    @endif

    <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
        <!-- Sidebar Navigation -->
        <div class="lg:col-span-1">
            <nav class="space-y-1" x-data="{ activeTab: '{{ request()->get('tab', 'notifications') }}' }">
                <a href="#" @click.prevent="activeTab = 'notifications'" 
                   :class="activeTab === 'notifications' ? 'bg-blue-50 border-blue-500 text-blue-700' : 'border-transparent text-gray-600 hover:bg-gray-50 hover:text-gray-900'"
                   class="group border-l-4 px-3 py-2 flex items-center text-sm font-medium transition-colors duration-200">
                    <svg class="flex-shrink-0 -ml-1 mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4 19h6v-7a1 1 0 011-1h4a1 1 0 011 1v7h6M4 10l8-8 8 8"/>
                    </svg>
                    Notifications
                </a>
                <a href="#" @click.prevent="activeTab = 'privacy'" 
                   :class="activeTab === 'privacy' ? 'bg-blue-50 border-blue-500 text-blue-700' : 'border-transparent text-gray-600 hover:bg-gray-50 hover:text-gray-900'"
                   class="group border-l-4 px-3 py-2 flex items-center text-sm font-medium transition-colors duration-200">
                    <svg class="flex-shrink-0 -ml-1 mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
                    </svg>
                    Privacy
                </a>
                <a href="#" @click.prevent="activeTab = 'security'" 
                   :class="activeTab === 'security' ? 'bg-blue-50 border-blue-500 text-blue-700' : 'border-transparent text-gray-600 hover:bg-gray-50 hover:text-gray-900'"
                   class="group border-l-4 px-3 py-2 flex items-center text-sm font-medium transition-colors duration-200">
                    <svg class="flex-shrink-0 -ml-1 mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"/>
                    </svg>
                    Security
                </a>
                <a href="#" @click.prevent="activeTab = 'data'" 
                   :class="activeTab === 'data' ? 'bg-blue-50 border-blue-500 text-blue-700' : 'border-transparent text-gray-600 hover:bg-gray-50 hover:text-gray-900'"
                   class="group border-l-4 px-3 py-2 flex items-center text-sm font-medium transition-colors duration-200">
                    <svg class="flex-shrink-0 -ml-1 mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"/>
                    </svg>
                    Data & Privacy
                </a>
            </nav>
        </div>

        <!-- Main Content -->
        <div class="lg:col-span-3">
            <div class="bg-white shadow-sm rounded-lg" x-data="{ activeTab: '{{ request()->get('tab', 'notifications') }}' }">
                <form method="POST" action="{{ route('profile.settings.update') }}">
                    @csrf
                    @method('PUT')

                    <!-- Notifications Tab -->
                    <div x-show="activeTab === 'notifications'" x-transition class="p-6">
                        <div class="border-b border-gray-200 pb-4 mb-6">
                            <h3 class="text-lg font-medium text-gray-900">Notification Preferences</h3>
                            <p class="mt-1 text-sm text-gray-600">Choose how you want to be notified about activity on Rectra.</p>
                        </div>

                        <div class="space-y-6">
                            <!-- Email Notifications -->
                            <div class="bg-gray-50 rounded-lg p-4">
                                <h4 class="text-base font-medium text-gray-900 mb-4">Email Notifications</h4>
                                <div class="space-y-4">
                                    <div class="flex items-start">
                                        <div class="flex items-center h-5">
                                            <input id="email_notifications" name="email_notifications" type="checkbox" value="1" 
                                                   {{ old('email_notifications', $user->email_notifications ?? true) ? 'checked' : '' }}
                                                   class="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded">
                                        </div>
                                        <div class="ml-3 text-sm">
                                            <label for="email_notifications" class="font-medium text-gray-700">General Email Notifications</label>
                                            <p class="text-gray-500">Receive important updates and account notifications via email.</p>
                                        </div>
                                    </div>

                                    <div class="flex items-start">
                                        <div class="flex items-center h-5">
                                            <input id="job_alerts" name="job_alerts" type="checkbox" value="1" 
                                                   {{ old('job_alerts', $user->job_alerts ?? true) ? 'checked' : '' }}
                                                   class="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded">
                                        </div>
                                        <div class="ml-3 text-sm">
                                            <label for="job_alerts" class="font-medium text-gray-700">Job Alerts</label>
                                            <p class="text-gray-500">Get notified when new jobs matching your preferences are posted.</p>
                                        </div>
                                    </div>

                                    <div class="flex items-start">
                                        <div class="flex items-center h-5">
                                            <input id="marketing_emails" name="marketing_emails" type="checkbox" value="1" 
                                                   {{ old('marketing_emails', $user->marketing_emails ?? false) ? 'checked' : '' }}
                                                   class="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded">
                                        </div>
                                        <div class="ml-3 text-sm">
                                            <label for="marketing_emails" class="font-medium text-gray-700">Marketing Communications</label>
                                            <p class="text-gray-500">Receive updates about new features, tips, and promotional content.</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Push Notifications -->
                            <div class="bg-gray-50 rounded-lg p-4">
                                <h4 class="text-base font-medium text-gray-900 mb-4">Push Notifications</h4>
                                <div class="space-y-4">
                                    <div class="flex items-start">
                                        <div class="flex items-center h-5">
                                            <input id="push_notifications" name="push_notifications" type="checkbox" value="1" 
                                                   {{ old('push_notifications', $user->push_notifications ?? true) ? 'checked' : '' }}
                                                   class="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded">
                                        </div>
                                        <div class="ml-3 text-sm">
                                            <label for="push_notifications" class="font-medium text-gray-700">Browser Notifications</label>
                                            <p class="text-gray-500">Receive real-time notifications in your browser.</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Privacy Tab -->
                    <div x-show="activeTab === 'privacy'" x-transition class="p-6">
                        <div class="border-b border-gray-200 pb-4 mb-6">
                            <h3 class="text-lg font-medium text-gray-900">Privacy Settings</h3>
                            <p class="mt-1 text-sm text-gray-600">Control who can see your profile and information.</p>
                        </div>

                        <div class="space-y-6">
                            <!-- Profile Visibility -->
                            <div class="bg-gray-50 rounded-lg p-4">
                                <h4 class="text-base font-medium text-gray-900 mb-4">Profile Visibility</h4>
                                <div class="space-y-4">
                                    <div>
                                        <label for="profile_visibility" class="block text-sm font-medium text-gray-700 mb-2">Who can see your profile?</label>
                                        <select name="profile_visibility" id="profile_visibility" 
                                                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                            <option value="public" {{ old('profile_visibility', $user->profile_visibility ?? 'public') === 'public' ? 'selected' : '' }}>
                                                🌍 Public - Visible to everyone
                                            </option>
                                            <option value="employers_only" {{ old('profile_visibility', $user->profile_visibility ?? 'public') === 'employers_only' ? 'selected' : '' }}>
                                                🏢 Employers Only - Visible to verified employers
                                            </option>
                                            <option value="private" {{ old('profile_visibility', $user->profile_visibility ?? 'public') === 'private' ? 'selected' : '' }}>
                                                🔒 Private - Not visible to others
                                            </option>
                                        </select>
                                        <p class="mt-1 text-sm text-gray-500">Choose who can view your profile information and contact you.</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Contact Preferences -->
                            <div class="bg-gray-50 rounded-lg p-4">
                                <h4 class="text-base font-medium text-gray-900 mb-4">Contact Preferences</h4>
                                <div class="space-y-4">
                                    <div class="flex items-start">
                                        <div class="flex items-center h-5">
                                            <input id="allow_contact" name="allow_contact" type="checkbox" value="1" 
                                                   {{ old('allow_contact', $user->allow_contact ?? true) ? 'checked' : '' }}
                                                   class="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded">
                                        </div>
                                        <div class="ml-3 text-sm">
                                            <label for="allow_contact" class="font-medium text-gray-700">Allow employers to contact me</label>
                                            <p class="text-gray-500">Let employers send you messages about job opportunities.</p>
                                        </div>
                                    </div>

                                    <div class="flex items-start">
                                        <div class="flex items-center h-5">
                                            <input id="show_email" name="show_email" type="checkbox" value="1" 
                                                   {{ old('show_email', $user->show_email ?? false) ? 'checked' : '' }}
                                                   class="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded">
                                        </div>
                                        <div class="ml-3 text-sm">
                                            <label for="show_email" class="font-medium text-gray-700">Show email address on profile</label>
                                            <p class="text-gray-500">Display your email address publicly on your profile.</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Security Tab -->
                    <div x-show="activeTab === 'security'" x-transition class="p-6">
                        <div class="border-b border-gray-200 pb-4 mb-6">
                            <h3 class="text-lg font-medium text-gray-900">Security Settings</h3>
                            <p class="mt-1 text-sm text-gray-600">Manage your account security and authentication.</p>
                        </div>

                        <div class="space-y-6">
                            <!-- Password Change -->
                            <div class="bg-gray-50 rounded-lg p-4">
                                <h4 class="text-base font-medium text-gray-900 mb-4">Change Password</h4>
                                <div class="space-y-4">
                                    <div>
                                        <label for="current_password" class="block text-sm font-medium text-gray-700 mb-2">Current Password</label>
                                        <input type="password" name="current_password" id="current_password" 
                                               class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                               placeholder="Enter your current password">
                                    </div>
                                    
                                    <div>
                                        <label for="new_password" class="block text-sm font-medium text-gray-700 mb-2">New Password</label>
                                        <input type="password" name="new_password" id="new_password" 
                                               class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                               placeholder="Enter your new password">
                                        <p class="mt-1 text-sm text-gray-500">Password must be at least 8 characters long.</p>
                                    </div>
                                    
                                    <div>
                                        <label for="confirm_password" class="block text-sm font-medium text-gray-700 mb-2">Confirm New Password</label>
                                        <input type="password" name="confirm_password" id="confirm_password" 
                                               class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                               placeholder="Confirm your new password">
                                    </div>
                                </div>
                            </div>

                            <!-- Two-Factor Authentication -->
                            <div class="bg-gray-50 rounded-lg p-4">
                                <h4 class="text-base font-medium text-gray-900 mb-4">Two-Factor Authentication</h4>
                                <div class="flex items-start">
                                    <div class="flex items-center h-5">
                                        <input id="two_factor_enabled" name="two_factor_enabled" type="checkbox" value="1" 
                                               {{ old('two_factor_enabled', $user->two_factor_enabled ?? false) ? 'checked' : '' }}
                                               class="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded">
                                    </div>
                                    <div class="ml-3 text-sm">
                                        <label for="two_factor_enabled" class="font-medium text-gray-700">Enable Two-Factor Authentication</label>
                                        <p class="text-gray-500">Add an extra layer of security to your account by requiring a verification code.</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Login Sessions -->
                            <div class="bg-gray-50 rounded-lg p-4">
                                <h4 class="text-base font-medium text-gray-900 mb-4">Active Sessions</h4>
                                <div class="space-y-3">
                                    <div class="flex items-center justify-between p-3 bg-white rounded border">
                                        <div class="flex items-center">
                                            <div class="w-2 h-2 bg-green-400 rounded-full mr-3"></div>
                                            <div>
                                                <p class="text-sm font-medium text-gray-900">Current Session</p>
                                                <p class="text-xs text-gray-500">Windows • Chrome • {{ request()->ip() }}</p>
                                            </div>
                                        </div>
                                        <span class="text-xs text-green-600 font-medium">Active</span>
                                    </div>
                                    <button type="button" class="text-sm text-red-600 hover:text-red-800 font-medium">
                                        Revoke all other sessions
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Data & Privacy Tab -->
                    <div x-show="activeTab === 'data'" x-transition class="p-6">
                        <div class="border-b border-gray-200 pb-4 mb-6">
                            <h3 class="text-lg font-medium text-gray-900">Data & Privacy</h3>
                            <p class="mt-1 text-sm text-gray-600">Manage your data and account deletion options.</p>
                        </div>

                        <div class="space-y-6">
                            <!-- Data Export -->
                            <div class="bg-gray-50 rounded-lg p-4">
                                <h4 class="text-base font-medium text-gray-900 mb-4">Export Your Data</h4>
                                <p class="text-sm text-gray-600 mb-4">Download a copy of all your data including profile information, applications, and activity.</p>
                                <a href="{{ route('profile.export') }}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                    <svg class="-ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                    Download My Data
                                </a>
                            </div>

                            <!-- Account Deletion -->
                            <div class="bg-red-50 rounded-lg p-4 border border-red-200">
                                <h4 class="text-base font-medium text-red-900 mb-4">Delete Account</h4>
                                <p class="text-sm text-red-700 mb-4">Permanently delete your account and all associated data. This action cannot be undone.</p>
                                <button type="button" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500" data-bs-toggle="modal" data-bs-target="#deleteAccountModal">
                                    <svg class="-ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                    </svg>
                                    Delete Account
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Save Button -->
                    <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 rounded-b-lg">
                        <div class="flex justify-between">
                            <a href="{{ route('profile.show') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                Cancel
                            </a>
                            <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                Save Settings
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Delete Account Modal -->
<div class="modal fade" id="deleteAccountModal" tabindex="-1" aria-labelledby="deleteAccountModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteAccountModalLabel">{{ __('Delete Account') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>{{ __('Are you sure you want to delete your account? This action cannot be undone.') }}</p>
                <p class="text-danger"><strong>{{ __('All your data will be permanently deleted.') }}</strong></p>
                
                <form id="deleteAccountForm" method="POST" action="{{ route('profile.destroy') }}">
                    @csrf
                    @method('DELETE')
                    
                    <div class="mb-3">
                        <label for="delete_password" class="form-label">{{ __('Confirm your password') }}</label>
                        <input type="password" class="form-control" name="password" id="delete_password" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Cancel') }}</button>
                <button type="submit" form="deleteAccountForm" class="btn btn-danger">
                    {{ __('Delete My Account') }}
                </button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Two-factor authentication toggle
document.getElementById('two_factor_enabled').addEventListener('change', function() {
    if (this.checked) {
        // Here you would typically show a modal or redirect to 2FA setup
        alert('Two-factor authentication setup would be implemented here.');
    }
});

// Form validation
document.querySelector('form').addEventListener('submit', function(e) {
    const newPassword = document.getElementById('new_password').value;
    const confirmPassword = document.getElementById('new_password_confirmation').value;
    
    if (newPassword && newPassword !== confirmPassword) {
        e.preventDefault();
        alert('New passwords do not match.');
        return false;
    }
    
    const currentPassword = document.getElementById('current_password').value;
    if (!currentPassword) {
        e.preventDefault();
        alert('Please enter your current password to save changes.');
        return false;
    }
});
</script>
@endpush