<?php $__env->startSection('title', 'Application Details'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>Application Details</h2>
                <a href="<?php echo e(route('admin.applications.index')); ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Applications
                </a>
            </div>
            
            <div class="row">
                <!-- Application Info -->
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title">Application Information</h4>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>Application ID</h6>
                                    <p><?php echo e($application->id); ?></p>
                                    
                                    <h6>Status</h6>
                                    <span class="badge bg-<?php echo e($application->getStatusColorAttribute()); ?> mb-3">
                                        <?php echo e($application->getStatusLabelAttribute()); ?>

                                    </span>
                                    
                                    <h6>Applied Date</h6>
                                    <p><?php echo e($application->created_at->format('F d, Y \a\t g:i A')); ?></p>
                                    
                                    <h6>Last Updated</h6>
                                    <p><?php echo e($application->updated_at->format('F d, Y \a\t g:i A')); ?></p>
                                </div>
                                
                                <div class="col-md-6">
                                    <?php if($application->cover_letter): ?>
                                        <h6>Cover Letter</h6>
                                        <div class="border p-3 mb-3" style="max-height: 200px; overflow-y: auto;">
                                            <?php echo e($application->cover_letter); ?>

                                        </div>
                                    <?php endif; ?>
                                    
                                    <?php if($application->admin_notes): ?>
                                        <h6>Admin Notes</h6>
                                        <div class="border p-3 mb-3" style="max-height: 150px; overflow-y: auto;">
                                            <?php echo e($application->admin_notes); ?>

                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Job Details -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h4 class="card-title">Job Details</h4>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>Job Title</h6>
                                    <p><?php echo e($application->job->title ?? 'N/A'); ?></p>
                                    
                                    <h6>Company</h6>
                                    <p><?php echo e($application->job->employerProfile->company_name ?? 'N/A'); ?></p>
                                    
                                    <h6>Location</h6>
                                    <p><?php echo e($application->job->location ?? 'N/A'); ?></p>
                                </div>
                                
                                <div class="col-md-6">
                                    <h6>Job Type</h6>
                                    <p><?php echo e($application->job->job_type ?? 'N/A'); ?></p>
                                    
                                    <h6>Experience Level</h6>
                                    <p><?php echo e($application->job->experience_level ?? 'N/A'); ?></p>
                                    
                                    <h6>Salary Range</h6>
                                    <p>
                                        <?php if($application->job->salary_min && $application->job->salary_max): ?>
                                            $<?php echo e(number_format($application->job->salary_min)); ?> - $<?php echo e(number_format($application->job->salary_max)); ?>

                                        <?php else: ?>
                                            Not specified
                                        <?php endif; ?>
                                    </p>
                                </div>
                            </div>
                            
                            <div class="mt-3">
                                <a href="<?php echo e(route('admin.jobs.show', $application->job)); ?>" class="btn btn-outline-primary">
                                    View Full Job Details
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Applicant Info & Actions -->
                <div class="col-md-4">
                    <!-- Applicant Details -->
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title">Applicant Details</h4>
                        </div>
                        <div class="card-body">
                            <div class="text-center mb-3">
                                <?php if($application->user && $application->user->avatar): ?>
                                    <img src="<?php echo e($application->user->avatar); ?>" alt="Avatar" 
                                         class="rounded-circle" width="80" height="80">
                                <?php else: ?>
                                    <div class="bg-secondary rounded-circle d-inline-flex align-items-center justify-content-center" 
                                         style="width: 80px; height: 80px;">
                                        <i class="fas fa-user fa-2x text-white"></i>
                                    </div>
                                <?php endif; ?>
                            </div>
                            
                            <h6>Name</h6>
                            <p><?php echo e($application->user->name ?? 'N/A'); ?></p>
                            
                            <h6>Email</h6>
                            <p><?php echo e($application->user->email ?? 'N/A'); ?></p>
                            
                            <?php if($application->jobSeekerProfile): ?>
                                <h6>Phone</h6>
                                <p><?php echo e($application->jobSeekerProfile->phone ?? 'Not provided'); ?></p>
                                
                                <h6>Location</h6>
                                <p><?php echo e($application->jobSeekerProfile->location ?? 'Not provided'); ?></p>
                                
                                <?php if($application->jobSeekerProfile->bio): ?>
                                    <h6>Bio</h6>
                                    <div class="border p-2" style="max-height: 100px; overflow-y: auto;">
                                        <?php echo e($application->jobSeekerProfile->bio); ?>

                                    </div>
                                <?php endif; ?>
                            <?php endif; ?>
                            
                            <div class="mt-3">
                                <a href="<?php echo e(route('admin.users.show', $application->user)); ?>" class="btn btn-outline-primary btn-sm">
                                    View Full Profile
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Resume -->
                    <?php if($application->resume): ?>
                        <div class="card mt-4">
                            <div class="card-header">
                                <h4 class="card-title">Resume</h4>
                            </div>
                            <div class="card-body">
                                <p><strong>File:</strong> <?php echo e($application->resume->file_name ?? 'resume.pdf'); ?></p>
                                <p><strong>Uploaded:</strong> <?php echo e($application->resume->created_at->format('M d, Y')); ?></p>
                                
                                <?php if($application->resume->file_path): ?>
                                    <a href="<?php echo e(Storage::url($application->resume->file_path)); ?>" 
                                       class="btn btn-outline-primary btn-sm" target="_blank">
                                        <i class="fas fa-download"></i> Download Resume
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <!-- Actions -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h4 class="card-title">Actions</h4>
                        </div>
                        <div class="card-body">
                            <!-- Status Update Form -->
                            <form method="POST" action="<?php echo e(route('admin.applications.update', $application)); ?>" class="mb-3">
                                <?php echo csrf_field(); ?>
                                <?php echo method_field('PUT'); ?>
                                
                                <div class="mb-3">
                                    <label for="status" class="form-label">Update Status</label>
                                    <select class="form-select" id="status" name="status" required>
                                        <option value="pending" <?php echo e($application->status == 'pending' ? 'selected' : ''); ?>>Pending</option>
                                        <option value="reviewing" <?php echo e($application->status == 'reviewing' ? 'selected' : ''); ?>>Reviewing</option>
                                        <option value="shortlisted" <?php echo e($application->status == 'shortlisted' ? 'selected' : ''); ?>>Shortlisted</option>
                                        <option value="interviewed" <?php echo e($application->status == 'interviewed' ? 'selected' : ''); ?>>Interviewed</option>
                                        <option value="offered" <?php echo e($application->status == 'offered' ? 'selected' : ''); ?>>Offered</option>
                                        <option value="hired" <?php echo e($application->status == 'hired' ? 'selected' : ''); ?>>Hired</option>
                                        <option value="rejected" <?php echo e($application->status == 'rejected' ? 'selected' : ''); ?>>Rejected</option>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="admin_notes" class="form-label">Admin Notes</label>
                                    <textarea class="form-control" id="admin_notes" name="admin_notes" rows="3" 
                                              placeholder="Add notes about this application..."><?php echo e($application->admin_notes); ?></textarea>
                                </div>
                                
                                <button type="submit" class="btn btn-primary btn-sm w-100">Update Application</button>
                            </form>
                            
                            <!-- Quick Actions -->
                            <div class="d-grid gap-2">
                                <?php if($application->status !== 'approved'): ?>
                                    <form method="POST" action="<?php echo e(route('admin.applications.approve', $application)); ?>">
                                        <?php echo csrf_field(); ?>
                                        <button type="submit" class="btn btn-success btn-sm w-100"
                                                onclick="return confirm('Are you sure you want to approve this application?')">
                                            <i class="fas fa-check"></i> Approve
                                        </button>
                                    </form>
                                <?php endif; ?>
                                
                                <?php if($application->status !== 'rejected'): ?>
                                    <form method="POST" action="<?php echo e(route('admin.applications.reject', $application)); ?>">
                                        <?php echo csrf_field(); ?>
                                        <button type="submit" class="btn btn-warning btn-sm w-100"
                                                onclick="return confirm('Are you sure you want to reject this application?')">
                                            <i class="fas fa-times"></i> Reject
                                        </button>
                                    </form>
                                <?php endif; ?>
                                
                                <form method="POST" action="<?php echo e(route('admin.applications.destroy', $application)); ?>">
                                    <?php echo csrf_field(); ?>
                                    <?php echo method_field('DELETE'); ?>
                                    <button type="submit" class="btn btn-danger btn-sm w-100"
                                            onclick="return confirm('Are you sure you want to delete this application? This action cannot be undone.')">
                                        <i class="fas fa-trash"></i> Delete
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\laravel\rectra\resources\views/admin/applications/show.blade.php ENDPATH**/ ?>