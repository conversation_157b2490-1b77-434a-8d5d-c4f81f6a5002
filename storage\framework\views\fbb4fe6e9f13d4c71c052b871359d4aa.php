<?php $__env->startSection('title', 'Application Details'); ?>

<?php $__env->startSection('content'); ?>
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-900">Application Details</h2>
        <a href="<?php echo e(route('admin.applications.index')); ?>" class="inline-flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium rounded-md transition-colors duration-200">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            Back to Applications
        </a>
    </div>
    
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Application Info -->
        <div class="lg:col-span-2 space-y-6">
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Application Information</h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="space-y-4">
                            <div>
                                <h4 class="text-sm font-medium text-gray-500 mb-1">Application ID</h4>
                                <p class="text-sm text-gray-900"><?php echo e($application->id); ?></p>
                            </div>
                            
                            <div>
                                <h4 class="text-sm font-medium text-gray-500 mb-1">Status</h4>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-<?php echo e($application->getStatusColorAttribute()); ?>-100 text-<?php echo e($application->getStatusColorAttribute()); ?>-800">
                                    <?php echo e($application->getStatusLabelAttribute()); ?>

                                </span>
                            </div>
                            
                            <div>
                                <h4 class="text-sm font-medium text-gray-500 mb-1">Applied Date</h4>
                                <p class="text-sm text-gray-900"><?php echo e($application->created_at->format('F d, Y \a\t g:i A')); ?></p>
                            </div>
                            
                            <div>
                                <h4 class="text-sm font-medium text-gray-500 mb-1">Last Updated</h4>
                                <p class="text-sm text-gray-900"><?php echo e($application->updated_at->format('F d, Y \a\t g:i A')); ?></p>
                            </div>
                        </div>
                        
                        <div class="space-y-4">
                            <?php if($application->cover_letter): ?>
                                <div>
                                    <h4 class="text-sm font-medium text-gray-500 mb-1">Cover Letter</h4>
                                    <div class="border border-gray-200 rounded-md p-3 max-h-48 overflow-y-auto bg-gray-50">
                                        <p class="text-sm text-gray-700"><?php echo e($application->cover_letter); ?></p>
                                    </div>
                                </div>
                            <?php endif; ?>
                            
                            <?php if($application->admin_notes): ?>
                                <div>
                                    <h4 class="text-sm font-medium text-gray-500 mb-1">Admin Notes</h4>
                                    <div class="border border-gray-200 rounded-md p-3 max-h-36 overflow-y-auto bg-gray-50">
                                        <p class="text-sm text-gray-700"><?php echo e($application->admin_notes); ?></p>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
                    
            <!-- Job Details -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Job Details</h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="space-y-4">
                            <div>
                                <h4 class="text-sm font-medium text-gray-500 mb-1">Job Title</h4>
                                <p class="text-sm text-gray-900"><?php echo e($application->job->title ?? 'N/A'); ?></p>
                            </div>
                            
                            <div>
                                <h4 class="text-sm font-medium text-gray-500 mb-1">Company</h4>
                                <p class="text-sm text-gray-900"><?php echo e($application->job->employerProfile->company_name ?? 'N/A'); ?></p>
                            </div>
                            
                            <div>
                                <h4 class="text-sm font-medium text-gray-500 mb-1">Location</h4>
                                <p class="text-sm text-gray-900"><?php echo e($application->job->location ?? 'N/A'); ?></p>
                            </div>
                        </div>
                        
                        <div class="space-y-4">
                            <div>
                                <h4 class="text-sm font-medium text-gray-500 mb-1">Job Type</h4>
                                <p class="text-sm text-gray-900"><?php echo e($application->job->job_type ?? 'N/A'); ?></p>
                            </div>
                            
                            <div>
                                <h4 class="text-sm font-medium text-gray-500 mb-1">Experience Level</h4>
                                <p class="text-sm text-gray-900"><?php echo e($application->job->experience_level ?? 'N/A'); ?></p>
                            </div>
                            
                            <div>
                                <h4 class="text-sm font-medium text-gray-500 mb-1">Salary Range</h4>
                                <p class="text-sm text-gray-900">
                                    <?php if($application->job->salary_min && $application->job->salary_max): ?>
                                        $<?php echo e(number_format($application->job->salary_min)); ?> - $<?php echo e(number_format($application->job->salary_max)); ?>

                                    <?php else: ?>
                                        Not specified
                                    <?php endif; ?>
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-6">
                        <a href="<?php echo e(route('admin.jobs.show', $application->job)); ?>" class="inline-flex items-center px-4 py-2 border border-blue-300 text-blue-700 bg-blue-50 hover:bg-blue-100 text-sm font-medium rounded-md transition-colors duration-200">
                            View Full Job Details
                        </a>
                    </div>
                </div>
            </div>
        </div>
                
        <!-- Applicant Info & Actions -->
        <div class="space-y-6">
            <!-- Applicant Details -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Applicant Details</h3>
                </div>
                <div class="p-6">
                    <div class="text-center mb-6">
                        <?php if($application->user && $application->user->avatar): ?>
                            <img src="<?php echo e($application->user->avatar); ?>" alt="Avatar" 
                                 class="w-20 h-20 rounded-full mx-auto object-cover">
                        <?php else: ?>
                            <div class="w-20 h-20 bg-gray-400 rounded-full mx-auto flex items-center justify-center">
                                <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="space-y-4">
                        <div>
                            <h4 class="text-sm font-medium text-gray-500 mb-1">Name</h4>
                            <p class="text-sm text-gray-900"><?php echo e($application->user->name ?? 'N/A'); ?></p>
                        </div>
                        
                        <div>
                            <h4 class="text-sm font-medium text-gray-500 mb-1">Email</h4>
                            <p class="text-sm text-gray-900"><?php echo e($application->user->email ?? 'N/A'); ?></p>
                        </div>
                        
                        <?php if($application->jobSeekerProfile): ?>
                            <div>
                                <h4 class="text-sm font-medium text-gray-500 mb-1">Phone</h4>
                                <p class="text-sm text-gray-900"><?php echo e($application->jobSeekerProfile->phone ?? 'Not provided'); ?></p>
                            </div>
                            
                            <div>
                                <h4 class="text-sm font-medium text-gray-500 mb-1">Location</h4>
                                <p class="text-sm text-gray-900"><?php echo e($application->jobSeekerProfile->location ?? 'Not provided'); ?></p>
                            </div>
                            
                            <?php if($application->jobSeekerProfile->bio): ?>
                                <div>
                                    <h4 class="text-sm font-medium text-gray-500 mb-1">Bio</h4>
                                    <div class="border border-gray-200 rounded-md p-2 max-h-24 overflow-y-auto bg-gray-50">
                                        <p class="text-sm text-gray-700"><?php echo e($application->jobSeekerProfile->bio); ?></p>
                                    </div>
                                </div>
                            <?php endif; ?>
                        <?php endif; ?>
                        
                        <div class="pt-2">
                            <a href="<?php echo e(route('admin.users.show', $application->user)); ?>" class="inline-flex items-center px-3 py-2 border border-blue-300 text-blue-700 bg-blue-50 hover:bg-blue-100 text-sm font-medium rounded-md transition-colors duration-200">
                                View Full Profile
                            </a>
                        </div>
                    </div>
                </div>
            </div>
                    
            <!-- Resume -->
            <?php if($application->resume): ?>
                <div class="bg-white shadow rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Resume</h3>
                    </div>
                    <div class="p-6">
                        <div class="space-y-3">
                            <div>
                                <span class="text-sm font-medium text-gray-500">File:</span>
                                <span class="text-sm text-gray-900 ml-2"><?php echo e($application->resume->file_name ?? 'resume.pdf'); ?></span>
                            </div>
                            <div>
                                <span class="text-sm font-medium text-gray-500">Uploaded:</span>
                                <span class="text-sm text-gray-900 ml-2"><?php echo e($application->resume->created_at->format('M d, Y')); ?></span>
                            </div>
                            
                            <?php if($application->resume->file_path): ?>
                                <div class="pt-2">
                                    <a href="<?php echo e(Storage::url($application->resume->file_path)); ?>" 
                                       class="inline-flex items-center px-3 py-2 border border-blue-300 text-blue-700 bg-blue-50 hover:bg-blue-100 text-sm font-medium rounded-md transition-colors duration-200" target="_blank">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                        </svg>
                                        Download Resume
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
                    
            <!-- Actions -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Actions</h3>
                </div>
                <div class="p-6">
                    <!-- Status Update Form -->
                    <form method="POST" action="<?php echo e(route('admin.applications.update', $application)); ?>" class="mb-6">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('PUT'); ?>
                        
                        <div class="mb-4">
                            <label for="status" class="block text-sm font-medium text-gray-700 mb-2">Update Status</label>
                            <select class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" id="status" name="status" required>
                                <option value="pending" <?php echo e($application->status == 'pending' ? 'selected' : ''); ?>>Pending</option>
                                <option value="reviewing" <?php echo e($application->status == 'reviewing' ? 'selected' : ''); ?>>Reviewing</option>
                                <option value="shortlisted" <?php echo e($application->status == 'shortlisted' ? 'selected' : ''); ?>>Shortlisted</option>
                                <option value="interviewed" <?php echo e($application->status == 'interviewed' ? 'selected' : ''); ?>>Interviewed</option>
                                <option value="offered" <?php echo e($application->status == 'offered' ? 'selected' : ''); ?>>Offered</option>
                                <option value="hired" <?php echo e($application->status == 'hired' ? 'selected' : ''); ?>>Hired</option>
                                <option value="rejected" <?php echo e($application->status == 'rejected' ? 'selected' : ''); ?>>Rejected</option>
                            </select>
                        </div>
                        
                        <div class="mb-4">
                            <label for="admin_notes" class="block text-sm font-medium text-gray-700 mb-2">Admin Notes</label>
                            <textarea class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" id="admin_notes" name="admin_notes" rows="3" 
                                      placeholder="Add notes about this application..."><?php echo e($application->admin_notes); ?></textarea>
                        </div>
                        
                        <button type="submit" class="w-full inline-flex justify-center items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-md transition-colors duration-200">
                            Update Application
                        </button>
                    </form>
                            
                    <!-- Quick Actions -->
                    <div class="space-y-3">
                        <?php if($application->status !== 'approved'): ?>
                            <form method="POST" action="<?php echo e(route('admin.applications.approve', $application)); ?>">
                                <?php echo csrf_field(); ?>
                                <button type="submit" class="w-full inline-flex justify-center items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white text-sm font-medium rounded-md transition-colors duration-200"
                                        onclick="return confirm('Are you sure you want to approve this application?')">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    Approve
                                </button>
                            </form>
                        <?php endif; ?>
                        
                        <?php if($application->status !== 'rejected'): ?>
                            <form method="POST" action="<?php echo e(route('admin.applications.reject', $application)); ?>">
                                <?php echo csrf_field(); ?>
                                <button type="submit" class="w-full inline-flex justify-center items-center px-4 py-2 bg-yellow-600 hover:bg-yellow-700 text-white text-sm font-medium rounded-md transition-colors duration-200"
                                        onclick="return confirm('Are you sure you want to reject this application?')">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                    </svg>
                                    Reject
                                </button>
                            </form>
                        <?php endif; ?>
                        
                        <form method="POST" action="<?php echo e(route('admin.applications.destroy', $application)); ?>">
                            <?php echo csrf_field(); ?>
                            <?php echo method_field('DELETE'); ?>
                            <button type="submit" class="w-full inline-flex justify-center items-center px-4 py-2 bg-red-600 hover:bg-red-700 text-white text-sm font-medium rounded-md transition-colors duration-200"
                                    onclick="return confirm('Are you sure you want to delete this application? This action cannot be undone.')">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                </svg>
                                Delete
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\laravel\rectra\resources\views/admin/applications/show.blade.php ENDPATH**/ ?>