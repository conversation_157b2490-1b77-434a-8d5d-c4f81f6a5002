@extends('layouts.app')

@section('title', 'Register')
@section('description', 'Create your Rectra account to start finding jobs, taking courses, and advancing your career.')

@section('content')
<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <div>
            <div class="mx-auto h-12 w-auto flex justify-center">
                <a href="{{ route('home') }}" class="text-3xl font-bold text-blue-600">
                    Rectra
                </a>
            </div>
            <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
                Create your account
            </h2>
            <p class="mt-2 text-center text-sm text-gray-600">
                Or
                <a href="{{ route('login') }}" class="font-medium text-blue-600 hover:text-blue-500">
                    sign in to your existing account
                </a>
            </p>
        </div>
        
        <form class="mt-8 space-y-6" action="{{ route('register') }}" method="POST" 
              x-data="{ 
                  isSubmitting: false, 
                  termsAccepted: false,
                  password: '',
                  passwordConfirmation: '',
                  get passwordsMatch() {
                      return this.password === this.passwordConfirmation && this.password.length > 0;
                  },
                  get canSubmit() {
                      return this.termsAccepted && this.passwordsMatch && this.password.length >= 8;
                  }
              }" 
              @submit="isSubmitting = true">
            @csrf
            
            <div class="space-y-4">
                <!-- Name -->
                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700">Full Name</label>
                    <input id="name" name="name" type="text" autocomplete="name" required 
                           class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm @error('name') border-red-300 @enderror" 
                           placeholder="Enter your full name" value="{{ old('name') }}">
                    @error('name')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Email -->
                <div>
                    <label for="email" class="block text-sm font-medium text-gray-700">Email Address</label>
                    <input id="email" name="email" type="email" autocomplete="email" required 
                           class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm @error('email') border-red-300 @enderror" 
                           placeholder="Enter your email address" value="{{ old('email') }}">
                    @error('email')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- User Type -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-3">I am a:</label>
                    <div class="space-y-2">
                        <div class="flex items-center">
                            <input id="job_seeker" name="user_type" type="radio" value="job_seeker" 
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300" 
                                   {{ old('user_type', 'job_seeker') == 'job_seeker' ? 'checked' : '' }}>
                            <label for="job_seeker" class="ml-3 block text-sm text-gray-700">
                                <span class="font-medium">Job Seeker</span>
                                <span class="block text-gray-500">Looking for job opportunities and career growth</span>
                            </label>
                        </div>
                        <div class="flex items-center">
                            <input id="employer" name="user_type" type="radio" value="employer" 
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300" 
                                   {{ old('user_type') == 'employer' ? 'checked' : '' }}>
                            <label for="employer" class="ml-3 block text-sm text-gray-700">
                                <span class="font-medium">Employer</span>
                                <span class="block text-gray-500">Hiring talent and posting job opportunities</span>
                            </label>
                        </div>
                    </div>
                    @error('user_type')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Password -->
                <div>
                    <label for="password" class="block text-sm font-medium text-gray-700">Password</label>
                    <input id="password" name="password" type="password" autocomplete="new-password" required 
                           x-model="password"
                           class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm @error('password') border-red-300 @enderror" 
                           placeholder="Create a strong password">
                    @error('password')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                    <div class="mt-1 flex items-center space-x-2">
                        <div class="flex-1">
                            <div class="h-1 bg-gray-200 rounded-full overflow-hidden">
                                <div class="h-full transition-all duration-300" 
                                     :class="{
                                         'bg-red-500 w-1/4': password.length > 0 && password.length < 6,
                                         'bg-yellow-500 w-2/4': password.length >= 6 && password.length < 8,
                                         'bg-green-500 w-full': password.length >= 8
                                     }"></div>
                            </div>
                        </div>
                        <p class="text-xs" 
                           :class="{
                               'text-red-600': password.length > 0 && password.length < 6,
                               'text-yellow-600': password.length >= 6 && password.length < 8,
                               'text-green-600': password.length >= 8,
                               'text-gray-500': password.length === 0
                           }"
                           x-text="password.length === 0 ? 'Must be at least 8 characters' : 
                                   password.length < 6 ? 'Weak' : 
                                   password.length < 8 ? 'Fair' : 'Strong'">Must be at least 8 characters</p>
                    </div>
                </div>

                <!-- Confirm Password -->
                <div>
                    <label for="password_confirmation" class="block text-sm font-medium text-gray-700">Confirm Password</label>
                    <input id="password_confirmation" name="password_confirmation" type="password" autocomplete="new-password" required 
                           x-model="passwordConfirmation"
                           class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm" 
                           placeholder="Confirm your password">
                    <p x-show="passwordConfirmation.length > 0 && !passwordsMatch" class="mt-1 text-sm text-red-600">
                        Passwords do not match
                    </p>
                    <p x-show="passwordConfirmation.length > 0 && passwordsMatch" class="mt-1 text-sm text-green-600">
                        Passwords match
                    </p>
                </div>

                <!-- Terms and Privacy -->
                <div class="flex items-center">
                    <input id="terms" name="terms" type="checkbox" required 
                           x-model="termsAccepted"
                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                    <label for="terms" class="ml-2 block text-sm text-gray-900">
                        I agree to the 
                        <a href="{{ route('terms') }}" class="text-blue-600 hover:text-blue-500">Terms of Service</a> 
                        and 
                        <a href="{{ route('privacy') }}" class="text-blue-600 hover:text-blue-500">Privacy Policy</a>
                    </label>
                </div>
                @error('terms')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <div>
                <button type="submit" 
                        :disabled="isSubmitting || !canSubmit"
                        :class="{
                            'opacity-75 cursor-not-allowed': isSubmitting || !canSubmit,
                            'hover:bg-blue-700': canSubmit && !isSubmitting,
                            'bg-gray-400': !canSubmit,
                            'bg-blue-600': canSubmit
                        }"
                        class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200">
                    <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                        <svg x-show="!isSubmitting" class="h-5 w-5 text-blue-500 group-hover:text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M10 1a4.5 4.5 0 00-4.5 4.5V9H5a2 2 0 00-2 2v6a2 2 0 002 2h10a2 2 0 002-2v-6a2 2 0 00-2-2h-.5V5.5A4.5 4.5 0 0010 1zm3 8V5.5a3 3 0 10-6 0V9h6z" clip-rule="evenodd" />
                        </svg>
                        <svg x-show="isSubmitting" class="animate-spin h-5 w-5 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                    </span>
                    <span x-text="isSubmitting ? 'Creating Account...' : 'Create Account'">Create Account</span>
                </button>
            </div>

            <!-- Social Registration Options -->
            <div class="mt-6">
                <div class="relative">
                    <div class="absolute inset-0 flex items-center">
                        <div class="w-full border-t border-gray-300" />
                    </div>
                    <div class="relative flex justify-center text-sm">
                        <span class="px-2 bg-gray-50 text-gray-500">Or register with</span>
                    </div>
                </div>

                <div class="mt-6 grid grid-cols-2 gap-3">
                    <a href="{{ route('auth.google') }}" class="w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 transition-colors duration-200">
                        <svg class="h-5 w-5" viewBox="0 0 24 24">
                            <path fill="currentColor" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                            <path fill="currentColor" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                            <path fill="currentColor" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                            <path fill="currentColor" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                        </svg>
                        <span class="ml-2">Google</span>
                    </a>

                    <a href="{{ route('auth.linkedin') }}" class="w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 transition-colors duration-200">
                        <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.338 16.338H13.67V12.16c0-.995-.017-2.277-1.387-2.277-1.39 0-1.601 1.086-1.601 2.207v4.248H8.014v-8.59h2.559v1.174h.037c.356-.675 1.227-1.387 2.526-1.387 2.703 0 3.203 1.778 3.203 4.092v4.711zM5.005 6.575a1.548 1.548 0 11-.003-3.096 1.548 1.548 0 01.003 3.096zm-1.337 9.763H6.34v-8.59H3.667v8.59zM17.668 1H2.328C1.595 1 1 1.581 1 2.298v15.403C1 18.418 1.595 19 2.328 19h15.34c.734 0 1.332-.582 1.332-1.299V2.298C19 1.581 18.402 1 17.668 1z" clip-rule="evenodd" />
                        </svg>
                        <span class="ml-2">LinkedIn</span>
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>
@endsection