@extends('layouts.app')

@section('title', 'Search Talent')

@section('content')
<div class="min-h-screen bg-gray-50 py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="text-center mb-12">
            <h1 class="text-4xl font-bold text-gray-900 mb-4">Find Top Talent</h1>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Search through thousands of qualified candidates and find the perfect match for your team.
            </p>
        </div>

        <!-- Search Filters -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Keywords</label>
                    <input type="text" placeholder="Job title, skills, company" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Location</label>
                    <input type="text" placeholder="City, state, or remote" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Experience Level</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">Any Experience</option>
                        <option value="entry">Entry Level</option>
                        <option value="mid">Mid Level</option>
                        <option value="senior">Senior Level</option>
                        <option value="executive">Executive</option>
                    </select>
                </div>
                <div class="flex items-end">
                    <button class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors">
                        Search Talent
                    </button>
                </div>
            </div>
        </div>

        <!-- Advanced Filters -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Advanced Filters</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Industry</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">All Industries</option>
                        <option value="technology">Technology</option>
                        <option value="healthcare">Healthcare</option>
                        <option value="finance">Finance</option>
                        <option value="education">Education</option>
                        <option value="marketing">Marketing</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Salary Range</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">Any Salary</option>
                        <option value="30-50k">$30k - $50k</option>
                        <option value="50-75k">$50k - $75k</option>
                        <option value="75-100k">$75k - $100k</option>
                        <option value="100k+">$100k+</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Availability</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">Any Availability</option>
                        <option value="immediate">Immediately</option>
                        <option value="2weeks">Within 2 weeks</option>
                        <option value="1month">Within 1 month</option>
                        <option value="3months">Within 3 months</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Search Results -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Candidate Cards -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Candidate 1 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <div class="flex items-start justify-between mb-4">
                        <div class="flex items-center">
                            <div class="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold text-xl mr-4">
                                JS
                            </div>
                            <div>
                                <h3 class="text-xl font-semibold text-gray-900">John Smith</h3>
                                <p class="text-gray-600">Senior Software Engineer</p>
                                <p class="text-sm text-gray-500">San Francisco, CA</p>
                            </div>
                        </div>
                        <button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors">
                            Contact
                        </button>
                    </div>
                    <div class="mb-4">
                        <p class="text-gray-700 mb-2">Experienced full-stack developer with 8+ years in React, Node.js, and cloud technologies.</p>
                        <div class="flex flex-wrap gap-2">
                            <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs">React</span>
                            <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs">Node.js</span>
                            <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs">AWS</span>
                            <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs">TypeScript</span>
                        </div>
                    </div>
                    <div class="flex justify-between text-sm text-gray-600">
                        <span>8+ years experience</span>
                        <span>Available immediately</span>
                        <span>$120k - $150k</span>
                    </div>
                </div>

                <!-- Candidate 2 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <div class="flex items-start justify-between mb-4">
                        <div class="flex items-center">
                            <div class="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center text-white font-bold text-xl mr-4">
                                MD
                            </div>
                            <div>
                                <h3 class="text-xl font-semibold text-gray-900">Maria Davis</h3>
                                <p class="text-gray-600">UX/UI Designer</p>
                                <p class="text-sm text-gray-500">New York, NY</p>
                            </div>
                        </div>
                        <button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors">
                            Contact
                        </button>
                    </div>
                    <div class="mb-4">
                        <p class="text-gray-700 mb-2">Creative designer with expertise in user research, prototyping, and design systems.</p>
                        <div class="flex flex-wrap gap-2">
                            <span class="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs">Figma</span>
                            <span class="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs">Adobe Creative Suite</span>
                            <span class="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs">User Research</span>
                            <span class="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs">Prototyping</span>
                        </div>
                    </div>
                    <div class="flex justify-between text-sm text-gray-600">
                        <span>5+ years experience</span>
                        <span>Available in 2 weeks</span>
                        <span>$85k - $110k</span>
                    </div>
                </div>

                <!-- Candidate 3 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <div class="flex items-start justify-between mb-4">
                        <div class="flex items-center">
                            <div class="w-16 h-16 bg-purple-500 rounded-full flex items-center justify-center text-white font-bold text-xl mr-4">
                                RJ
                            </div>
                            <div>
                                <h3 class="text-xl font-semibold text-gray-900">Robert Johnson</h3>
                                <p class="text-gray-600">Data Scientist</p>
                                <p class="text-sm text-gray-500">Austin, TX</p>
                            </div>
                        </div>
                        <button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors">
                            Contact
                        </button>
                    </div>
                    <div class="mb-4">
                        <p class="text-gray-700 mb-2">Data scientist specializing in machine learning, statistical analysis, and data visualization.</p>
                        <div class="flex flex-wrap gap-2">
                            <span class="bg-purple-100 text-purple-800 px-2 py-1 rounded-full text-xs">Python</span>
                            <span class="bg-purple-100 text-purple-800 px-2 py-1 rounded-full text-xs">Machine Learning</span>
                            <span class="bg-purple-100 text-purple-800 px-2 py-1 rounded-full text-xs">SQL</span>
                            <span class="bg-purple-100 text-purple-800 px-2 py-1 rounded-full text-xs">Tableau</span>
                        </div>
                    </div>
                    <div class="flex justify-between text-sm text-gray-600">
                        <span>6+ years experience</span>
                        <span>Available in 1 month</span>
                        <span>$100k - $130k</span>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Search Tips -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Search Tips</h3>
                    <ul class="space-y-2 text-sm text-gray-600">
                        <li>• Use specific keywords for better results</li>
                        <li>• Try different combinations of skills</li>
                        <li>• Filter by location for local talent</li>
                        <li>• Set salary ranges to match your budget</li>
                    </ul>
                </div>

                <!-- Saved Searches -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Saved Searches</h3>
                    <div class="space-y-2">
                        <div class="flex justify-between items-center p-2 bg-gray-50 rounded">
                            <span class="text-sm text-gray-700">React Developers</span>
                            <button class="text-blue-600 hover:text-blue-800 text-xs">View</button>
                        </div>
                        <div class="flex justify-between items-center p-2 bg-gray-50 rounded">
                            <span class="text-sm text-gray-700">UX Designers</span>
                            <button class="text-blue-600 hover:text-blue-800 text-xs">View</button>
                        </div>
                    </div>
                    <button class="w-full mt-4 text-blue-600 hover:text-blue-800 text-sm font-medium">
                        Save Current Search
                    </button>
                </div>

                <!-- Contact Info -->
                <div class="bg-blue-50 rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Need Help?</h3>
                    <p class="text-sm text-gray-600 mb-4">Our talent acquisition specialists can help you find the perfect candidates.</p>
                    <button class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors">
                        Contact Specialist
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection