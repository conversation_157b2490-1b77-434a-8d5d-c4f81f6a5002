<?php $__env->startSection('title', 'Job Applications Management'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Job Applications</h3>
                </div>
                
                <!-- Filters -->
                <div class="card-body border-bottom">
                    <form method="GET" action="<?php echo e(route('admin.applications.index')); ?>" class="row g-3">
                        <div class="col-md-3">
                            <label for="search" class="form-label">Search</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="<?php echo e(request('search')); ?>" placeholder="Applicant name, email, or job title">
                        </div>
                        
                        <div class="col-md-2">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">All Statuses</option>
                                <?php $__currentLoopData = $statuses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $status): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($status); ?>" <?php echo e(request('status') == $status ? 'selected' : ''); ?>>
                                        <?php echo e(ucfirst($status)); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                        
                        <div class="col-md-3">
                            <label for="job_id" class="form-label">Job</label>
                            <select class="form-select" id="job_id" name="job_id">
                                <option value="">All Jobs</option>
                                <?php $__currentLoopData = $jobs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $job): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($job->id); ?>" <?php echo e(request('job_id') == $job->id ? 'selected' : ''); ?>>
                                        <?php echo e($job->title); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                        
                        <div class="col-md-2">
                            <label for="date_from" class="form-label">From Date</label>
                            <input type="date" class="form-control" id="date_from" name="date_from" 
                                   value="<?php echo e(request('date_from')); ?>">
                        </div>
                        
                        <div class="col-md-2">
                            <label for="date_to" class="form-label">To Date</label>
                            <input type="date" class="form-control" id="date_to" name="date_to" 
                                   value="<?php echo e(request('date_to')); ?>">
                        </div>
                        
                        <div class="col-12">
                            <button type="submit" class="btn btn-primary">Filter</button>
                            <a href="<?php echo e(route('admin.applications.index')); ?>" class="btn btn-secondary">Clear</a>
                        </div>
                    </form>
                </div>
                
                <!-- Applications Table -->
                <div class="card-body">
                    <?php if($applications->count() > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Applicant</th>
                                        <th>Job Title</th>
                                        <th>Company</th>
                                        <th>Status</th>
                                        <th>Applied Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $applications; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $application): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td><?php echo e($application->id); ?></td>
                                            <td>
                                                <div>
                                                    <strong><?php echo e($application->user->name ?? 'N/A'); ?></strong>
                                                    <br>
                                                    <small class="text-muted"><?php echo e($application->user->email ?? 'N/A'); ?></small>
                                                </div>
                                            </td>
                                            <td><?php echo e($application->job->title ?? 'N/A'); ?></td>
                                            <td><?php echo e($application->job->employerProfile->company_name ?? 'N/A'); ?></td>
                                            <td>
                                                <span class="badge bg-<?php echo e($application->getStatusColorAttribute()); ?>">
                                                    <?php echo e($application->getStatusLabelAttribute()); ?>

                                                </span>
                                            </td>
                                            <td><?php echo e($application->created_at->format('M d, Y')); ?></td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="<?php echo e(route('admin.applications.show', $application)); ?>" 
                                                       class="btn btn-sm btn-outline-primary">View</a>
                                                    
                                                    <?php if($application->status !== 'approved'): ?>
                                                        <form method="POST" action="<?php echo e(route('admin.applications.approve', $application)); ?>" 
                                                              class="d-inline">
                                                            <?php echo csrf_field(); ?>
                                                            <button type="submit" class="btn btn-sm btn-outline-success"
                                                                    onclick="return confirm('Are you sure you want to approve this application?')">
                                                                Approve
                                                            </button>
                                                        </form>
                                                    <?php endif; ?>
                                                    
                                                    <?php if($application->status !== 'rejected'): ?>
                                                        <form method="POST" action="<?php echo e(route('admin.applications.reject', $application)); ?>" 
                                                              class="d-inline">
                                                            <?php echo csrf_field(); ?>
                                                            <button type="submit" class="btn btn-sm btn-outline-danger"
                                                                    onclick="return confirm('Are you sure you want to reject this application?')">
                                                                Reject
                                                            </button>
                                                        </form>
                                                    <?php endif; ?>
                                                    
                                                    <form method="POST" action="<?php echo e(route('admin.applications.destroy', $application)); ?>" 
                                                          class="d-inline">
                                                        <?php echo csrf_field(); ?>
                                                        <?php echo method_field('DELETE'); ?>
                                                        <button type="submit" class="btn btn-sm btn-outline-danger"
                                                                onclick="return confirm('Are you sure you want to delete this application?')">
                                                            Delete
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- Pagination -->
                        <div class="d-flex justify-content-center">
                            <?php echo e($applications->links()); ?>

                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <p class="text-muted">No job applications found.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
    // Auto-submit form when filters change
    document.querySelectorAll('#status, #job_id').forEach(function(element) {
        element.addEventListener('change', function() {
            this.form.submit();
        });
    });
</script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\laravel\rectra\resources\views/admin/applications/index.blade.php ENDPATH**/ ?>