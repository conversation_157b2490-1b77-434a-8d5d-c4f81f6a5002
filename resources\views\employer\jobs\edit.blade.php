@extends('layouts.app')

@section('title', 'Edit Job')

@section('content')
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Edit Job</h1>
                <p class="mt-2 text-gray-600">Update your job posting</p>
            </div>
            <a href="{{ route('employer.jobs.show', $job) }}" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Back to Job
            </a>
        </div>
    </div>

    <!-- Job Edit Form -->
    <div class="bg-white shadow-sm rounded-lg">
        <form action="{{ route('employer.jobs.update', $job) }}" method="POST" class="space-y-6 p-6">
            @csrf
            @method('PUT')

            <!-- Basic Information -->
            <div class="space-y-6">
                <h2 class="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2">Basic Information</h2>
                
                <div>
                    <label for="title" class="block text-sm font-medium text-gray-700 mb-2">Job Title</label>
                    <input type="text" id="title" name="title" value="{{ old('title', $job->title) }}" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500" 
                           placeholder="e.g., Senior Software Engineer" required>
                    @error('title')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="category" class="block text-sm font-medium text-gray-700 mb-2">Category</label>
                        <select id="category" name="category" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500" required>
                            <option value="">Select Category</option>
                            <option value="technology" {{ old('category', $job->category) === 'technology' ? 'selected' : '' }}>Technology</option>
                            <option value="marketing" {{ old('category', $job->category) === 'marketing' ? 'selected' : '' }}>Marketing</option>
                            <option value="sales" {{ old('category', $job->category) === 'sales' ? 'selected' : '' }}>Sales</option>
                            <option value="design" {{ old('category', $job->category) === 'design' ? 'selected' : '' }}>Design</option>
                            <option value="finance" {{ old('category', $job->category) === 'finance' ? 'selected' : '' }}>Finance</option>
                            <option value="hr" {{ old('category', $job->category) === 'hr' ? 'selected' : '' }}>Human Resources</option>
                            <option value="operations" {{ old('category', $job->category) === 'operations' ? 'selected' : '' }}>Operations</option>
                            <option value="other" {{ old('category', $job->category) === 'other' ? 'selected' : '' }}>Other</option>
                        </select>
                        @error('category')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="type" class="block text-sm font-medium text-gray-700 mb-2">Job Type</label>
                        <select id="type" name="type" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500" required>
                            <option value="">Select Type</option>
                            <option value="full_time" {{ old('type', $job->type) === 'full_time' ? 'selected' : '' }}>Full Time</option>
                            <option value="part_time" {{ old('type', $job->type) === 'part_time' ? 'selected' : '' }}>Part Time</option>
                            <option value="contract" {{ old('type', $job->type) === 'contract' ? 'selected' : '' }}>Contract</option>
                            <option value="freelance" {{ old('type', $job->type) === 'freelance' ? 'selected' : '' }}>Freelance</option>
                            <option value="internship" {{ old('type', $job->type) === 'internship' ? 'selected' : '' }}>Internship</option>
                        </select>
                        @error('type')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="location" class="block text-sm font-medium text-gray-700 mb-2">Location</label>
                        <input type="text" id="location" name="location" value="{{ old('location', $job->location) }}" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500" 
                               placeholder="e.g., New York, NY or Remote" required>
                        @error('location')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="experience_level" class="block text-sm font-medium text-gray-700 mb-2">Experience Level</label>
                        <select id="experience_level" name="experience_level" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500" required>
                            <option value="">Select Level</option>
                            <option value="entry" {{ old('experience_level', $job->experience_level) === 'entry' ? 'selected' : '' }}>Entry Level</option>
                            <option value="mid" {{ old('experience_level', $job->experience_level) === 'mid' ? 'selected' : '' }}>Mid Level</option>
                            <option value="senior" {{ old('experience_level', $job->experience_level) === 'senior' ? 'selected' : '' }}>Senior Level</option>
                            <option value="executive" {{ old('experience_level', $job->experience_level) === 'executive' ? 'selected' : '' }}>Executive</option>
                        </select>
                        @error('experience_level')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <div>
                    <label for="description" class="block text-sm font-medium text-gray-700 mb-2">Job Description</label>
                    <textarea id="description" name="description" rows="6" 
                              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500" 
                              placeholder="Describe the role, responsibilities, and what you're looking for..." required>{{ old('description', $job->description) }}</textarea>
                    @error('description')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Salary Information -->
            <div class="space-y-6">
                <h2 class="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2">Salary Information</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <label for="salary_min" class="block text-sm font-medium text-gray-700 mb-2">Minimum Salary</label>
                        <input type="number" id="salary_min" name="salary_min" value="{{ old('salary_min', $job->salary_min) }}" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500" 
                               min="0" step="1000" placeholder="50000">
                        @error('salary_min')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="salary_max" class="block text-sm font-medium text-gray-700 mb-2">Maximum Salary</label>
                        <input type="number" id="salary_max" name="salary_max" value="{{ old('salary_max', $job->salary_max) }}" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500" 
                               min="0" step="1000" placeholder="80000">
                        @error('salary_max')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="salary_period" class="block text-sm font-medium text-gray-700 mb-2">Salary Period</label>
                        <select id="salary_period" name="salary_period" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500">
                            <option value="">Select Period</option>
                            <option value="hour" {{ old('salary_period', $job->salary_period) === 'hour' ? 'selected' : '' }}>Per Hour</option>
                            <option value="month" {{ old('salary_period', $job->salary_period) === 'month' ? 'selected' : '' }}>Per Month</option>
                            <option value="year" {{ old('salary_period', $job->salary_period) === 'year' ? 'selected' : '' }}>Per Year</option>
                        </select>
                        @error('salary_period')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Requirements and Benefits -->
            <div class="space-y-6">
                <h2 class="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2">Requirements & Benefits</h2>
                
                <div>
                    <label for="requirements" class="block text-sm font-medium text-gray-700 mb-2">Requirements</label>
                    <textarea id="requirements" name="requirements" rows="4" 
                              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500" 
                              placeholder="Enter each requirement on a new line">{{ old('requirements', is_array($job->requirements) ? implode("\n", $job->requirements) : $job->requirements) }}</textarea>
                    <p class="mt-1 text-sm text-gray-500">Enter each requirement on a new line</p>
                    @error('requirements')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="benefits" class="block text-sm font-medium text-gray-700 mb-2">Benefits</label>
                    <textarea id="benefits" name="benefits" rows="4" 
                              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500" 
                              placeholder="Enter each benefit on a new line">{{ old('benefits', is_array($job->benefits) ? implode("\n", $job->benefits) : $job->benefits) }}</textarea>
                    <p class="mt-1 text-sm text-gray-500">Enter each benefit on a new line</p>
                    @error('benefits')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Job Settings -->
            <div class="space-y-6">
                <h2 class="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2">Job Settings</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="expires_at" class="block text-sm font-medium text-gray-700 mb-2">Expiration Date</label>
                        <input type="date" id="expires_at" name="expires_at" value="{{ old('expires_at', $job->expires_at->format('Y-m-d')) }}" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500" 
                               min="{{ date('Y-m-d') }}">
                        @error('expires_at')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="status" class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                        <select id="status" name="status" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500">
                            <option value="active" {{ old('status', $job->status) === 'active' ? 'selected' : '' }}>Active</option>
                            <option value="paused" {{ old('status', $job->status) === 'paused' ? 'selected' : '' }}>Paused</option>
                            <option value="closed" {{ old('status', $job->status) === 'closed' ? 'selected' : '' }}>Closed</option>
                        </select>
                        @error('status')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <div class="flex items-center">
                    <input type="checkbox" id="is_remote" name="is_remote" value="1" 
                           {{ old('is_remote', $job->is_remote) ? 'checked' : '' }}
                           class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                    <label for="is_remote" class="ml-2 block text-sm text-gray-700">
                        This is a remote position
                    </label>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
                <a href="{{ route('employer.jobs.show', $job) }}" class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                    Cancel
                </a>
                <button type="submit" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500">
                    Update Job
                </button>
            </div>
        </form>
    </div>

    <!-- Delete Job -->
    <div class="mt-8 bg-white shadow-sm rounded-lg p-6 border border-red-200">
        <h3 class="text-lg font-medium text-red-900 mb-2">Delete Job</h3>
        <p class="text-sm text-red-700 mb-4">
            Once you delete this job, all applications and related data will be permanently removed. This action cannot be undone.
        </p>
        <form action="{{ route('employer.jobs.destroy', $job) }}" method="POST" onsubmit="return confirm('Are you sure you want to delete this job? This action cannot be undone.')">
            @csrf
            @method('DELETE')
            <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                </svg>
                Delete Job
            </button>
        </form>
    </div>
</div>
@endsection
