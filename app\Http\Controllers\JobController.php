<?php

namespace App\Http\Controllers;

use App\Models\Job;
use App\Models\JobApplication;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class JobController extends Controller
{
    /**
     * Display a listing of jobs.
     */
    public function index(Request $request)
    {
        $query = Job::with(['employerProfile.user', 'media'])
            ->where('status', 'active')
            ->where('expires_at', '>', now());

        // Search by keywords
        if ($request->filled('search')) {
            $searchTerm = $request->search;
            $query->where(function ($q) use ($searchTerm) {
                $q->where('title', 'like', "%{$searchTerm}%")
                  ->orWhere('description', 'like', "%{$searchTerm}%")
                  ->orWhere('requirements', 'like', "%{$searchTerm}%")
                  ->orWhere('skills', 'like', "%{$searchTerm}%");
            });
        }

        // Filter by location
        if ($request->filled('location')) {
            $query->where('location', 'like', "%{$request->location}%");
        }

        // Filter by job type
        if ($request->filled('type')) {
            $query->where('job_type', $request->type);
        }

        // Filter by experience level
        if ($request->filled('experience_level')) {
            $query->where('experience_level', $request->experience_level);
        }

        // Filter by industry
        if ($request->filled('industry')) {
            $query->where('industry', $request->industry);
        }

        // Filter by company size
        if ($request->filled('company_size')) {
            $query->whereHas('employerProfile', function ($q) use ($request) {
                $q->where('company_size', $request->company_size);
            });
        }

        // Filter by salary range
        if ($request->filled('min_salary')) {
            $query->where('salary_min', '>=', $request->min_salary);
        }
        if ($request->filled('max_salary')) {
            $query->where('salary_max', '<=', $request->max_salary);
        }

        // Filter by remote work
        if ($request->filled('remote')) {
            if ($request->remote === 'true') {
                $query->where('is_remote', true);
            }
        }

        // Filter by featured jobs
        if ($request->filled('featured')) {
            if ($request->featured === 'true') {
                $query->where('is_featured', true);
            }
        }

        // Sort options
        $sortBy = $request->get('sort', 'created_at');
        $sortOrder = $request->get('order', 'desc');

        switch ($sortBy) {
            case 'title':
                $query->orderBy('title', $sortOrder);
                break;
            case 'salary':
                $query->orderBy('salary_max', $sortOrder);
                break;
            case 'location':
                $query->orderBy('location', $sortOrder);
                break;
            case 'company':
                $query->join('employer_profiles', 'job_listings.employer_profile_id', '=', 'employer_profiles.id')
                      ->orderBy('employer_profiles.company_name', $sortOrder)
                      ->select('job_listings.*');
                break;
            default:
                $query->orderBy('created_at', $sortOrder);
        }

        // Get jobs with pagination
        $jobs = $query->paginate(12)->withQueryString();

        // Get filter options for the sidebar
        $industries = Job::select('industry')
            ->where('status', 'active')
            ->distinct()
            ->whereNotNull('industry')
            ->pluck('industry')
            ->sort();

        $locations = Job::select('location')
            ->where('status', 'active')
            ->distinct()
            ->whereNotNull('location')
            ->pluck('location')
            ->sort();

        $jobTypes = ['full-time', 'part-time', 'contract', 'freelance', 'internship'];
        $experienceLevels = ['entry', 'mid', 'senior', 'lead', 'executive'];
        $companySizes = ['1-10', '11-50', '51-200', '201-500', '501-1000', '1000+'];

        // SEO Optimization for jobs listing
        $title = 'Find Jobs';
        $description = 'Browse thousands of job opportunities across various industries and locations.';
        
        if ($request->filled('search')) {
            $title = 'Jobs for "' . $request->search . '"';
            $description = 'Find ' . $request->search . ' jobs and career opportunities.';
        }
        
        if ($request->filled('location')) {
            $title .= ' in ' . $request->location;
            $description .= ' Located in ' . $request->location . '.';
        }
        
        \App\Helpers\SeoHelper::setTitle($title . ' | Rectra')
            ->setDescription($description)
            ->setKeywords(['jobs', 'careers', 'employment', 'job search', 'hiring'])
            ->setUrl(request()->fullUrl())
            ->addStructuredData(\App\Helpers\SeoHelper::generateWebsiteStructuredData());

        return view('jobs.index', compact(
            'jobs',
            'industries',
            'locations',
            'jobTypes',
            'experienceLevels',
            'companySizes'
        ));
    }

    /**
     * Display the specified job.
     */
    public function show(Job $job)
    {
        // Check if job is active and not expired
        if ($job->status !== 'active' || $job->expires_at <= now()) {
            abort(404);
        }

        // Load relationships
        $job->load([
            'employerProfile.user',
            'media',
            'applications' => function ($query) {
                if (Auth::check()) {
                    $query->where('job_seeker_profile_id', Auth::user()->jobSeekerProfile?->id);
                }
            }
        ]);

        // SEO Optimization
        \App\Helpers\SeoHelper::setTitle($job->title . ' at ' . ($job->employerProfile->company_name ?? 'Company') . ' | Rectra')
            ->setDescription(strip_tags(substr($job->description, 0, 160)) . '...')
            ->setKeywords([
                $job->title,
                $job->industry,
                $job->location,
                $job->job_type,
                'job',
                'career',
                'employment'
            ])
            ->setUrl(route('jobs.show', $job))
            ->setType('article')
            ->addStructuredData(\App\Helpers\SeoHelper::generateJobStructuredData($job));

        // Increment view count
        $job->increment('views_count');

        // Check if user has saved this job
        $isSaved = false;
        $hasApplied = false;
        
        if (Auth::check() && Auth::user()->isJobSeeker()) {
            $isSaved = Auth::user()->savedJobs()->where('job_id', $job->id)->exists();
            $hasApplied = $job->applications->isNotEmpty();
        }

        // Get similar jobs
        $similarJobs = Job::with(['employerProfile.user'])
            ->where('id', '!=', $job->id)
            ->where('status', 'active')
            ->where('expires_at', '>', now())
            ->where(function ($query) use ($job) {
                $query->where('industry', $job->industry)
                      ->orWhere('title', 'like', '%' . explode(' ', $job->title)[0] . '%');
            })
            ->limit(6)
            ->get();

        return view('jobs.show', compact('job', 'isSaved', 'hasApplied', 'similarJobs'));
    }

    /**
     * Get search suggestions for autocomplete.
     */
    public function searchSuggestions(Request $request)
    {
        $query = $request->get('q', '');
        
        if (strlen($query) < 2) {
            return response()->json([]);
        }

        // Get job title suggestions
        $jobTitles = Job::where('status', 'active')
            ->where('title', 'like', "%{$query}%")
            ->distinct()
            ->limit(5)
            ->pluck('title')
            ->map(function ($title) {
                return [
                    'type' => 'job_title',
                    'value' => $title,
                    'label' => $title
                ];
            });

        // Get company suggestions
        $companies = DB::table('employer_profiles')
            ->join('job_listings', 'employer_profiles.id', '=', 'job_listings.employer_profile_id')
            ->where('job_listings.status', 'active')
            ->where('employer_profiles.company_name', 'like', "%{$query}%")
            ->distinct()
            ->limit(5)
            ->pluck('employer_profiles.company_name')
            ->map(function ($company) {
                return [
                    'type' => 'company',
                    'value' => $company,
                    'label' => $company
                ];
            });

        // Get location suggestions
        $locations = Job::where('status', 'active')
            ->where('location', 'like', "%{$query}%")
            ->distinct()
            ->limit(5)
            ->pluck('location')
            ->map(function ($location) {
                return [
                    'type' => 'location',
                    'value' => $location,
                    'label' => $location
                ];
            });

        $suggestions = collect()
            ->merge($jobTitles)
            ->merge($companies)
            ->merge($locations)
            ->take(10);

        return response()->json($suggestions);
    }
}