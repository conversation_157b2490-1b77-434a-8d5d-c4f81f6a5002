<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Helpers\SeoHelper;

class SeoServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->singleton('seo', function () {
            return new SeoHelper();
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Reset SEO data on each request
        $this->app->booted(function () {
            SeoHelper::reset();
        });
    }
}