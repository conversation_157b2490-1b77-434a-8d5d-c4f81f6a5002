<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class Resume extends Model implements HasMedia
{
    use HasFactory, InteractsWithMedia;

    protected $fillable = [
        'job_seeker_profile_id',
        'file_name',
        'file_path',
        'file_type',
        'file_size',
        'is_primary',
        'privacy_setting',
        'parsed_data',
    ];

    protected $casts = [
        'is_primary' => 'boolean',
        'parsed_data' => 'array',
    ];

    /**
     * Get the job seeker profile that owns the resume.
     */
    public function jobSeekerProfile()
    {
        return $this->belongsTo(JobSeekerProfile::class);
    }

    /**
     * Get the job applications that use this resume.
     */
    public function jobApplications()
    {
        return $this->hasMany(JobApplication::class);
    }

    /**
     * Set this resume as primary and unset others.
     */
    public function setPrimary(): void
    {
        // First, unset all other resumes as primary for this profile
        $this->jobSeekerProfile->resumes()->update(['is_primary' => false]);
        
        // Then set this one as primary
        $this->update(['is_primary' => true]);
    }

    /**
     * Get the file size in human readable format.
     */
    public function getFileSizeHumanAttribute(): string
    {
        $bytes = $this->file_size;
        $units = ['B', 'KB', 'MB', 'GB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Check if the resume is publicly viewable.
     */
    public function isPublic(): bool
    {
        return $this->privacy_setting === 'public';
    }

    /**
     * Check if the resume is anonymous.
     */
    public function isAnonymous(): bool
    {
        return $this->privacy_setting === 'anonymous';
    }

    /**
     * Get the download URL for the resume.
     */
    public function getDownloadUrl(): string
    {
        return route('resumes.download', $this->id);
    }

    /**
     * Register media collections.
     */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('resume')
            ->acceptsMimeTypes(['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'])
            ->singleFile();
    }

    /**
     * Register media conversions.
     */
    public function registerMediaConversions(Media $media = null): void
    {
        // We could add thumbnail generation for PDFs here if needed
    }
}