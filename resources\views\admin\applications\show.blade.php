@extends('layouts.admin')

@section('title', 'Application Details')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>Application Details</h2>
                <a href="{{ route('admin.applications.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Applications
                </a>
            </div>
            
            <div class="row">
                <!-- Application Info -->
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title">Application Information</h4>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>Application ID</h6>
                                    <p>{{ $application->id }}</p>
                                    
                                    <h6>Status</h6>
                                    <span class="badge bg-{{ $application->getStatusColorAttribute() }} mb-3">
                                        {{ $application->getStatusLabelAttribute() }}
                                    </span>
                                    
                                    <h6>Applied Date</h6>
                                    <p>{{ $application->created_at->format('F d, Y \a\t g:i A') }}</p>
                                    
                                    <h6>Last Updated</h6>
                                    <p>{{ $application->updated_at->format('F d, Y \a\t g:i A') }}</p>
                                </div>
                                
                                <div class="col-md-6">
                                    @if($application->cover_letter)
                                        <h6>Cover Letter</h6>
                                        <div class="border p-3 mb-3" style="max-height: 200px; overflow-y: auto;">
                                            {{ $application->cover_letter }}
                                        </div>
                                    @endif
                                    
                                    @if($application->admin_notes)
                                        <h6>Admin Notes</h6>
                                        <div class="border p-3 mb-3" style="max-height: 150px; overflow-y: auto;">
                                            {{ $application->admin_notes }}
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Job Details -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h4 class="card-title">Job Details</h4>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>Job Title</h6>
                                    <p>{{ $application->job->title ?? 'N/A' }}</p>
                                    
                                    <h6>Company</h6>
                                    <p>{{ $application->job->employerProfile->company_name ?? 'N/A' }}</p>
                                    
                                    <h6>Location</h6>
                                    <p>{{ $application->job->location ?? 'N/A' }}</p>
                                </div>
                                
                                <div class="col-md-6">
                                    <h6>Job Type</h6>
                                    <p>{{ $application->job->job_type ?? 'N/A' }}</p>
                                    
                                    <h6>Experience Level</h6>
                                    <p>{{ $application->job->experience_level ?? 'N/A' }}</p>
                                    
                                    <h6>Salary Range</h6>
                                    <p>
                                        @if($application->job->salary_min && $application->job->salary_max)
                                            ${{ number_format($application->job->salary_min) }} - ${{ number_format($application->job->salary_max) }}
                                        @else
                                            Not specified
                                        @endif
                                    </p>
                                </div>
                            </div>
                            
                            <div class="mt-3">
                                <a href="{{ route('admin.jobs.show', $application->job) }}" class="btn btn-outline-primary">
                                    View Full Job Details
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Applicant Info & Actions -->
                <div class="col-md-4">
                    <!-- Applicant Details -->
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title">Applicant Details</h4>
                        </div>
                        <div class="card-body">
                            <div class="text-center mb-3">
                                @if($application->user && $application->user->avatar)
                                    <img src="{{ $application->user->avatar }}" alt="Avatar" 
                                         class="rounded-circle" width="80" height="80">
                                @else
                                    <div class="bg-secondary rounded-circle d-inline-flex align-items-center justify-content-center" 
                                         style="width: 80px; height: 80px;">
                                        <i class="fas fa-user fa-2x text-white"></i>
                                    </div>
                                @endif
                            </div>
                            
                            <h6>Name</h6>
                            <p>{{ $application->user->name ?? 'N/A' }}</p>
                            
                            <h6>Email</h6>
                            <p>{{ $application->user->email ?? 'N/A' }}</p>
                            
                            @if($application->jobSeekerProfile)
                                <h6>Phone</h6>
                                <p>{{ $application->jobSeekerProfile->phone ?? 'Not provided' }}</p>
                                
                                <h6>Location</h6>
                                <p>{{ $application->jobSeekerProfile->location ?? 'Not provided' }}</p>
                                
                                @if($application->jobSeekerProfile->bio)
                                    <h6>Bio</h6>
                                    <div class="border p-2" style="max-height: 100px; overflow-y: auto;">
                                        {{ $application->jobSeekerProfile->bio }}
                                    </div>
                                @endif
                            @endif
                            
                            <div class="mt-3">
                                <a href="{{ route('admin.users.show', $application->user) }}" class="btn btn-outline-primary btn-sm">
                                    View Full Profile
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Resume -->
                    @if($application->resume)
                        <div class="card mt-4">
                            <div class="card-header">
                                <h4 class="card-title">Resume</h4>
                            </div>
                            <div class="card-body">
                                <p><strong>File:</strong> {{ $application->resume->file_name ?? 'resume.pdf' }}</p>
                                <p><strong>Uploaded:</strong> {{ $application->resume->created_at->format('M d, Y') }}</p>
                                
                                @if($application->resume->file_path)
                                    <a href="{{ Storage::url($application->resume->file_path) }}" 
                                       class="btn btn-outline-primary btn-sm" target="_blank">
                                        <i class="fas fa-download"></i> Download Resume
                                    </a>
                                @endif
                            </div>
                        </div>
                    @endif
                    
                    <!-- Actions -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h4 class="card-title">Actions</h4>
                        </div>
                        <div class="card-body">
                            <!-- Status Update Form -->
                            <form method="POST" action="{{ route('admin.applications.update', $application) }}" class="mb-3">
                                @csrf
                                @method('PUT')
                                
                                <div class="mb-3">
                                    <label for="status" class="form-label">Update Status</label>
                                    <select class="form-select" id="status" name="status" required>
                                        <option value="pending" {{ $application->status == 'pending' ? 'selected' : '' }}>Pending</option>
                                        <option value="reviewing" {{ $application->status == 'reviewing' ? 'selected' : '' }}>Reviewing</option>
                                        <option value="shortlisted" {{ $application->status == 'shortlisted' ? 'selected' : '' }}>Shortlisted</option>
                                        <option value="interviewed" {{ $application->status == 'interviewed' ? 'selected' : '' }}>Interviewed</option>
                                        <option value="offered" {{ $application->status == 'offered' ? 'selected' : '' }}>Offered</option>
                                        <option value="hired" {{ $application->status == 'hired' ? 'selected' : '' }}>Hired</option>
                                        <option value="rejected" {{ $application->status == 'rejected' ? 'selected' : '' }}>Rejected</option>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="admin_notes" class="form-label">Admin Notes</label>
                                    <textarea class="form-control" id="admin_notes" name="admin_notes" rows="3" 
                                              placeholder="Add notes about this application...">{{ $application->admin_notes }}</textarea>
                                </div>
                                
                                <button type="submit" class="btn btn-primary btn-sm w-100">Update Application</button>
                            </form>
                            
                            <!-- Quick Actions -->
                            <div class="d-grid gap-2">
                                @if($application->status !== 'approved')
                                    <form method="POST" action="{{ route('admin.applications.approve', $application) }}">
                                        @csrf
                                        <button type="submit" class="btn btn-success btn-sm w-100"
                                                onclick="return confirm('Are you sure you want to approve this application?')">
                                            <i class="fas fa-check"></i> Approve
                                        </button>
                                    </form>
                                @endif
                                
                                @if($application->status !== 'rejected')
                                    <form method="POST" action="{{ route('admin.applications.reject', $application) }}">
                                        @csrf
                                        <button type="submit" class="btn btn-warning btn-sm w-100"
                                                onclick="return confirm('Are you sure you want to reject this application?')">
                                            <i class="fas fa-times"></i> Reject
                                        </button>
                                    </form>
                                @endif
                                
                                <form method="POST" action="{{ route('admin.applications.destroy', $application) }}">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="btn btn-danger btn-sm w-100"
                                            onclick="return confirm('Are you sure you want to delete this application? This action cannot be undone.')">
                                        <i class="fas fa-trash"></i> Delete
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection