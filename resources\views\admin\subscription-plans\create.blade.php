@extends('layouts.admin')

@section('title', 'Create Subscription Plan')

@section('content')
<div class="max-w-4xl mx-auto">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Create Subscription Plan</h1>
                <p class="mt-2 text-gray-600">Add a new subscription plan for employers</p>
            </div>
            <a href="{{ route('admin.subscription-plans.index') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Back to Plans
            </a>
        </div>
    </div>

    <!-- Plan Creation Form -->
    <div class="bg-white shadow-sm rounded-lg">
        <form action="{{ route('admin.subscription-plans.store') }}" method="POST" class="space-y-6 p-6">
            @csrf

            <!-- Basic Information -->
            <div class="space-y-6">
                <h2 class="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2">Basic Information</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Plan Name</label>
                        <input type="text" id="name" name="name" value="{{ old('name') }}" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500" 
                               placeholder="e.g., Basic Plan" required>
                        @error('name')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="type" class="block text-sm font-medium text-gray-700 mb-2">Plan Type</label>
                        <select id="type" name="type" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500" required>
                            <option value="">Select Type</option>
                            <option value="basic" {{ old('type') === 'basic' ? 'selected' : '' }}>Basic</option>
                            <option value="premium" {{ old('type') === 'premium' ? 'selected' : '' }}>Premium</option>
                            <option value="enterprise" {{ old('type') === 'enterprise' ? 'selected' : '' }}>Enterprise</option>
                        </select>
                        @error('type')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <div>
                    <label for="description" class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                    <textarea id="description" name="description" rows="3" 
                              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500" 
                              placeholder="Brief description of the plan">{{ old('description') }}</textarea>
                    @error('description')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Pricing -->
            <div class="space-y-6">
                <h2 class="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2">Pricing</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <label for="price" class="block text-sm font-medium text-gray-700 mb-2">Price ($)</label>
                        <input type="number" id="price" name="price" value="{{ old('price') }}" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500" 
                               min="0" step="0.01" placeholder="29.99" required>
                        @error('price')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="billing_period" class="block text-sm font-medium text-gray-700 mb-2">Billing Period</label>
                        <select id="billing_period" name="billing_period" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500" required>
                            <option value="">Select Period</option>
                            <option value="monthly" {{ old('billing_period') === 'monthly' ? 'selected' : '' }}>Monthly</option>
                            <option value="yearly" {{ old('billing_period') === 'yearly' ? 'selected' : '' }}>Yearly</option>
                        </select>
                        @error('billing_period')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="trial_days" class="block text-sm font-medium text-gray-700 mb-2">Trial Days</label>
                        <input type="number" id="trial_days" name="trial_days" value="{{ old('trial_days', 0) }}" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500" 
                               min="0" placeholder="14">
                        @error('trial_days')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Features & Limits -->
            <div class="space-y-6">
                <h2 class="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2">Features & Limits</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="job_postings_limit" class="block text-sm font-medium text-gray-700 mb-2">Job Postings Limit</label>
                        <input type="number" id="job_postings_limit" name="job_postings_limit" value="{{ old('job_postings_limit') }}" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500" 
                               min="1" placeholder="10">
                        <p class="mt-1 text-sm text-gray-500">Leave empty for unlimited</p>
                        @error('job_postings_limit')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="featured_jobs_limit" class="block text-sm font-medium text-gray-700 mb-2">Featured Jobs Limit</label>
                        <input type="number" id="featured_jobs_limit" name="featured_jobs_limit" value="{{ old('featured_jobs_limit') }}" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500" 
                               min="0" placeholder="3">
                        @error('featured_jobs_limit')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <div>
                    <label for="features" class="block text-sm font-medium text-gray-700 mb-2">Features</label>
                    <textarea id="features" name="features" rows="4" 
                              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500" 
                              placeholder="Enter each feature on a new line">{{ old('features') }}</textarea>
                    <p class="mt-1 text-sm text-gray-500">Enter each feature on a new line</p>
                    @error('features')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Plan Settings -->
            <div class="space-y-6">
                <h2 class="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2">Plan Settings</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="flex items-center">
                        <input type="checkbox" id="is_active" name="is_active" value="1" 
                               {{ old('is_active', true) ? 'checked' : '' }}
                               class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                        <label for="is_active" class="ml-2 block text-sm text-gray-700">
                            Active (available for purchase)
                        </label>
                    </div>

                    <div class="flex items-center">
                        <input type="checkbox" id="is_popular" name="is_popular" value="1" 
                               {{ old('is_popular') ? 'checked' : '' }}
                               class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                        <label for="is_popular" class="ml-2 block text-sm text-gray-700">
                            Mark as popular
                        </label>
                    </div>
                </div>

                <div>
                    <label for="sort_order" class="block text-sm font-medium text-gray-700 mb-2">Sort Order</label>
                    <input type="number" id="sort_order" name="sort_order" value="{{ old('sort_order', 0) }}" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500" 
                           min="0" placeholder="0">
                    <p class="mt-1 text-sm text-gray-500">Lower numbers appear first</p>
                    @error('sort_order')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Stripe Integration -->
            <div class="space-y-6">
                <h2 class="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2">Stripe Integration</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="stripe_price_id" class="block text-sm font-medium text-gray-700 mb-2">Stripe Price ID</label>
                        <input type="text" id="stripe_price_id" name="stripe_price_id" value="{{ old('stripe_price_id') }}" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500" 
                               placeholder="price_1234567890">
                        <p class="mt-1 text-sm text-gray-500">Create this price in Stripe dashboard first</p>
                        @error('stripe_price_id')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="stripe_product_id" class="block text-sm font-medium text-gray-700 mb-2">Stripe Product ID</label>
                        <input type="text" id="stripe_product_id" name="stripe_product_id" value="{{ old('stripe_product_id') }}" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500" 
                               placeholder="prod_1234567890">
                        @error('stripe_product_id')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
                <a href="{{ route('admin.subscription-plans.index') }}" class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                    Cancel
                </a>
                <button type="submit" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500">
                    Create Plan
                </button>
            </div>
        </form>
    </div>
</div>
@endsection
