<?php

namespace App\Http\Controllers;

use App\Models\Resume;
use App\Models\ResumeTemplate;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class ResumeBuilderController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('role:job_seeker');
    }

    /**
     * Display a listing of the user's resumes.
     */
    public function index()
    {
        $user = Auth::user();
        
        $resumes = $user->resumes()
            ->with('template')
            ->latest()
            ->paginate(12);

        $templates = ResumeTemplate::where('is_active', true)
            ->orderBy('is_premium')
            ->orderBy('name')
            ->get();

        return view('resume-builder.index', compact('resumes', 'templates'));
    }

    /**
     * Show the form for creating a new resume.
     */
    public function create(Request $request)
    {
        $templateId = $request->get('template');
        $template = null;
        
        if ($templateId) {
            $template = ResumeTemplate::where('is_active', true)
                ->findOrFail($templateId);
        }

        $templates = ResumeTemplate::where('is_active', true)
            ->orderBy('is_premium')
            ->orderBy('name')
            ->get();

        $user = Auth::user();
        $profile = $user->jobSeekerProfile;

        // Pre-fill data from user profile if available
        $resumeData = [
            'personal_info' => [
                'full_name' => $user->name,
                'email' => $user->email,
                'phone' => $user->phone,
                'location' => $profile->location ?? '',
                'website' => $profile->website ?? '',
                'linkedin_url' => $profile->linkedin_url ?? '',
                'github_url' => $profile->github_url ?? '',
                'professional_summary' => $profile->bio ?? '',
            ],
            'work_experience' => $profile && $profile->work_experience ? json_decode($profile->work_experience, true) : [],
            'education' => $profile && $profile->education ? json_decode($profile->education, true) : [],
            'skills' => $profile && $profile->skills ? json_decode($profile->skills, true) : [],
            'languages' => $profile && $profile->languages ? json_decode($profile->languages, true) : [],
        ];

        return view('resume-builder.create', compact('templates', 'template', 'resumeData'));
    }

    /**
     * Store a newly created resume.
     */
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'template_id' => 'required|exists:resume_templates,id',
            'personal_info' => 'required|array',
            'personal_info.full_name' => 'required|string|max:255',
            'personal_info.email' => 'required|email|max:255',
            'personal_info.phone' => 'nullable|string|max:20',
            'personal_info.location' => 'nullable|string|max:255',
            'personal_info.website' => 'nullable|url|max:255',
            'personal_info.linkedin_url' => 'nullable|url|max:255',
            'personal_info.github_url' => 'nullable|url|max:255',
            'personal_info.professional_summary' => 'nullable|string|max:1000',
            'work_experience' => 'nullable|array',
            'work_experience.*.job_title' => 'required_with:work_experience|string|max:255',
            'work_experience.*.company' => 'required_with:work_experience|string|max:255',
            'work_experience.*.location' => 'nullable|string|max:255',
            'work_experience.*.start_date' => 'required_with:work_experience|date',
            'work_experience.*.end_date' => 'nullable|date|after:work_experience.*.start_date',
            'work_experience.*.is_current' => 'boolean',
            'work_experience.*.description' => 'nullable|string|max:2000',
            'education' => 'nullable|array',
            'education.*.degree' => 'required_with:education|string|max:255',
            'education.*.institution' => 'required_with:education|string|max:255',
            'education.*.field_of_study' => 'nullable|string|max:255',
            'education.*.start_date' => 'required_with:education|date',
            'education.*.end_date' => 'nullable|date|after:education.*.start_date',
            'education.*.gpa' => 'nullable|numeric|min:0|max:4',
            'education.*.description' => 'nullable|string|max:500',
            'skills' => 'nullable|array',
            'skills.*' => 'string|max:100',
            'languages' => 'nullable|array',
            'languages.*.language' => 'required_with:languages|string|max:100',
            'languages.*.proficiency' => 'required_with:languages|in:beginner,intermediate,advanced,native',
            'certifications' => 'nullable|array',
            'certifications.*.name' => 'required_with:certifications|string|max:255',
            'certifications.*.issuer' => 'required_with:certifications|string|max:255',
            'certifications.*.date_issued' => 'required_with:certifications|date',
            'certifications.*.expiry_date' => 'nullable|date|after:certifications.*.date_issued',
            'certifications.*.credential_id' => 'nullable|string|max:255',
            'certifications.*.url' => 'nullable|url|max:255',
            'projects' => 'nullable|array',
            'projects.*.name' => 'required_with:projects|string|max:255',
            'projects.*.description' => 'required_with:projects|string|max:1000',
            'projects.*.technologies' => 'nullable|array',
            'projects.*.url' => 'nullable|url|max:255',
            'projects.*.github_url' => 'nullable|url|max:255',
            'projects.*.start_date' => 'nullable|date',
            'projects.*.end_date' => 'nullable|date|after:projects.*.start_date',
        ]);

        $user = Auth::user();
        $template = ResumeTemplate::findOrFail($request->template_id);

        // Check if template is premium and user has access
        if ($template->is_premium && !$user->hasPremiumAccess()) {
            return back()->with('error', 'This template requires a premium subscription.');
        }

        $resumeData = [
            'personal_info' => $request->personal_info,
            'work_experience' => $request->work_experience ?? [],
            'education' => $request->education ?? [],
            'skills' => $request->skills ?? [],
            'languages' => $request->languages ?? [],
            'certifications' => $request->certifications ?? [],
            'projects' => $request->projects ?? [],
        ];

        $resume = Resume::create([
            'user_id' => $user->id,
            'template_id' => $template->id,
            'title' => $request->title,
            'slug' => Str::slug($request->title . '-' . Str::random(6)),
            'data' => json_encode($resumeData),
            'is_public' => false,
        ]);

        return redirect()->route('resume-builder.show', $resume)
            ->with('success', 'Resume created successfully!');
    }

    /**
     * Display the specified resume.
     */
    public function show(Resume $resume)
    {
        $this->authorize('view', $resume);
        
        $resume->load('template');
        $resumeData = json_decode($resume->data, true);

        return view('resume-builder.show', compact('resume', 'resumeData'));
    }

    /**
     * Show the form for editing the specified resume.
     */
    public function edit(Resume $resume)
    {
        $this->authorize('update', $resume);
        
        $resume->load('template');
        $resumeData = json_decode($resume->data, true);
        
        $templates = ResumeTemplate::where('is_active', true)
            ->orderBy('is_premium')
            ->orderBy('name')
            ->get();

        return view('resume-builder.edit', compact('resume', 'resumeData', 'templates'));
    }

    /**
     * Update the specified resume.
     */
    public function update(Request $request, Resume $resume)
    {
        $this->authorize('update', $resume);
        
        $request->validate([
            'title' => 'required|string|max:255',
            'template_id' => 'required|exists:resume_templates,id',
            'personal_info' => 'required|array',
            'personal_info.full_name' => 'required|string|max:255',
            'personal_info.email' => 'required|email|max:255',
            // ... (same validation rules as store method)
        ]);

        $template = ResumeTemplate::findOrFail($request->template_id);
        $user = Auth::user();

        // Check if template is premium and user has access
        if ($template->is_premium && !$user->hasPremiumAccess()) {
            return back()->with('error', 'This template requires a premium subscription.');
        }

        $resumeData = [
            'personal_info' => $request->personal_info,
            'work_experience' => $request->work_experience ?? [],
            'education' => $request->education ?? [],
            'skills' => $request->skills ?? [],
            'languages' => $request->languages ?? [],
            'certifications' => $request->certifications ?? [],
            'projects' => $request->projects ?? [],
        ];

        $resume->update([
            'template_id' => $template->id,
            'title' => $request->title,
            'data' => json_encode($resumeData),
        ]);

        return redirect()->route('resume-builder.show', $resume)
            ->with('success', 'Resume updated successfully!');
    }

    /**
     * Duplicate the specified resume.
     */
    public function duplicate(Resume $resume)
    {
        $this->authorize('view', $resume);
        
        $user = Auth::user();
        
        $newResume = Resume::create([
            'user_id' => $user->id,
            'template_id' => $resume->template_id,
            'title' => $resume->title . ' (Copy)',
            'slug' => Str::slug($resume->title . '-copy-' . Str::random(6)),
            'data' => $resume->data,
            'is_public' => false,
        ]);

        return redirect()->route('resume-builder.edit', $newResume)
            ->with('success', 'Resume duplicated successfully!');
    }

    /**
     * Remove the specified resume.
     */
    public function destroy(Resume $resume)
    {
        $this->authorize('delete', $resume);
        
        // Delete any generated PDF files
        if ($resume->pdf_path && Storage::disk('public')->exists($resume->pdf_path)) {
            Storage::disk('public')->delete($resume->pdf_path);
        }

        $resume->delete();

        return redirect()->route('resume-builder.index')
            ->with('success', 'Resume deleted successfully!');
    }

    /**
     * Download resume as PDF.
     */
    public function downloadPdf(Resume $resume)
    {
        $this->authorize('view', $resume);
        
        // Generate PDF if not exists or if resume was updated
        if (!$resume->pdf_path || 
            !Storage::disk('public')->exists($resume->pdf_path) || 
            $resume->updated_at > $resume->pdf_generated_at) {
            $this->generatePdf($resume);
        }

        $filename = Str::slug($resume->title) . '.pdf';
        
        return Storage::disk('public')->download($resume->pdf_path, $filename);
    }

    /**
     * Preview resume.
     */
    public function preview(Resume $resume)
    {
        $this->authorize('view', $resume);
        
        $resume->load('template');
        $resumeData = json_decode($resume->data, true);

        return view('resume-builder.preview', compact('resume', 'resumeData'));
    }

    /**
     * Share resume publicly.
     */
    public function share(Resume $resume)
    {
        $this->authorize('update', $resume);
        
        $resume->update([
            'is_public' => !$resume->is_public,
            'public_url' => $resume->is_public ? null : Str::random(32),
        ]);

        $message = $resume->is_public 
            ? 'Resume is now publicly shareable!' 
            : 'Resume sharing disabled.';

        return back()->with('success', $message);
    }

    /**
     * View public resume.
     */
    public function viewPublic($publicUrl)
    {
        $resume = Resume::where('public_url', $publicUrl)
            ->where('is_public', true)
            ->with(['template', 'user'])
            ->firstOrFail();

        $resumeData = json_decode($resume->data, true);

        return view('resume-builder.public', compact('resume', 'resumeData'));
    }

    /**
     * Get resume templates.
     */
    public function templates()
    {
        $templates = ResumeTemplate::where('is_active', true)
            ->orderBy('is_premium')
            ->orderBy('name')
            ->get();

        return view('resume-builder.templates', compact('templates'));
    }

    /**
     * Preview template.
     */
    public function previewTemplate(ResumeTemplate $template)
    {
        // Sample data for template preview
        $sampleData = [
            'personal_info' => [
                'full_name' => 'John Doe',
                'email' => '<EMAIL>',
                'phone' => '+****************',
                'location' => 'New York, NY',
                'website' => 'https://johndoe.com',
                'linkedin_url' => 'https://linkedin.com/in/johndoe',
                'professional_summary' => 'Experienced software developer with 5+ years of experience in web development and mobile applications.',
            ],
            'work_experience' => [
                [
                    'job_title' => 'Senior Software Developer',
                    'company' => 'Tech Company Inc.',
                    'location' => 'New York, NY',
                    'start_date' => '2020-01-01',
                    'end_date' => null,
                    'is_current' => true,
                    'description' => 'Led development of web applications using React and Node.js. Managed a team of 3 developers.',
                ],
                [
                    'job_title' => 'Software Developer',
                    'company' => 'Startup LLC',
                    'location' => 'San Francisco, CA',
                    'start_date' => '2018-06-01',
                    'end_date' => '2019-12-31',
                    'is_current' => false,
                    'description' => 'Developed mobile applications using React Native and Flutter.',
                ],
            ],
            'education' => [
                [
                    'degree' => 'Bachelor of Science',
                    'institution' => 'University of Technology',
                    'field_of_study' => 'Computer Science',
                    'start_date' => '2014-09-01',
                    'end_date' => '2018-05-31',
                    'gpa' => 3.8,
                ],
            ],
            'skills' => ['JavaScript', 'React', 'Node.js', 'Python', 'SQL', 'Git'],
            'languages' => [
                ['language' => 'English', 'proficiency' => 'native'],
                ['language' => 'Spanish', 'proficiency' => 'intermediate'],
            ],
        ];

        return view('resume-builder.template-preview', compact('template', 'sampleData'));
    }

    /**
     * Generate PDF for resume.
     */
    private function generatePdf(Resume $resume)
    {
        // This would typically use a PDF generation library like DomPDF or Puppeteer
        // For now, this is a placeholder
        
        $resume->load('template');
        $resumeData = json_decode($resume->data, true);
        
        // Generate PDF using the template and data
        // $pdf = PDF::loadView('resume-templates.' . $resume->template->slug, compact('resumeData'));
        
        $filename = 'resumes/' . $resume->slug . '.pdf';
        // $pdf->save(storage_path('app/public/' . $filename));
        
        $resume->update([
            'pdf_path' => $filename,
            'pdf_generated_at' => now(),
        ]);
    }

    /**
     * Auto-save resume data (AJAX).
     */
    public function autoSave(Request $request, Resume $resume)
    {
        $this->authorize('update', $resume);
        
        $request->validate([
            'data' => 'required|array',
        ]);

        $resume->update([
            'data' => json_encode($request->data),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Resume auto-saved',
            'saved_at' => now()->toISOString(),
        ]);
    }

    /**
     * Import resume from file.
     */
    public function import(Request $request)
    {
        $request->validate([
            'resume_file' => 'required|file|mimes:pdf,doc,docx|max:5120',
            'template_id' => 'required|exists:resume_templates,id',
        ]);

        // This would typically use a document parsing service
        // to extract text and structure from the uploaded file
        
        return back()->with('info', 'Resume import feature coming soon.');
    }

    /**
     * Export resume data.
     */
    public function export(Resume $resume, $format = 'json')
    {
        $this->authorize('view', $resume);
        
        $resumeData = json_decode($resume->data, true);
        
        switch ($format) {
            case 'json':
                return response()->json($resumeData);
            case 'xml':
                // Convert to XML format
                return response($this->arrayToXml($resumeData), 200, [
                    'Content-Type' => 'application/xml',
                    'Content-Disposition' => 'attachment; filename="' . $resume->slug . '.xml"',
                ]);
            default:
                return back()->with('error', 'Invalid export format.');
        }
    }

    /**
     * Convert array to XML.
     */
    private function arrayToXml($array, $rootElement = 'resume')
    {
        $xml = new \SimpleXMLElement("<{$rootElement}></{$rootElement}>");
        
        function arrayToXmlRecursive($array, $xml) {
            foreach ($array as $key => $value) {
                if (is_array($value)) {
                    if (is_numeric($key)) {
                        $key = 'item';
                    }
                    $subnode = $xml->addChild($key);
                    arrayToXmlRecursive($value, $subnode);
                } else {
                    $xml->addChild($key, htmlspecialchars($value));
                }
            }
        }
        
        arrayToXmlRecursive($array, $xml);
        
        return $xml->asXML();
    }
}