<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;

class SavedJob extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'job_id',
    ];

    /**
     * Get the user that saved the job.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the job that was saved.
     */
    public function job()
    {
        return $this->belongsTo(Job::class);
    }

    /**
     * Scope a query to filter by user.
     */
    public function scopeForUser(Builder $query, int $userId): Builder
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope a query to filter by job.
     */
    public function scopeForJob(Builder $query, int $jobId): Builder
    {
        return $query->where('job_id', $jobId);
    }

    /**
     * Scope a query to include only active jobs.
     */
    public function scopeActiveJobs(Builder $query): Builder
    {
        return $query->whereHas('job', function ($q) {
            $q->where('status', 'active')
              ->where('expires_at', '>', now());
        });
    }

    /**
     * Scope a query to include only expired jobs.
     */
    public function scopeExpiredJobs(Builder $query): Builder
    {
        return $query->whereHas('job', function ($q) {
            $q->where('status', '!=', 'active')
              ->orWhere('expires_at', '<=', now());
        });
    }

    /**
     * Check if the saved job is still active.
     */
    public function isJobActive(): bool
    {
        return $this->job && 
               $this->job->status === 'active' && 
               $this->job->expires_at > now();
    }

    /**
     * Get the saved job with employer information.
     */
    public function getJobWithEmployer()
    {
        return $this->job()->with('employerProfile')->first();
    }
}