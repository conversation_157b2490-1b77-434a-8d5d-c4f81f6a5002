<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\JobSeekerProfile;
use App\Models\EmployerProfile;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Password;

class ProfileController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display the user's profile.
     */
    public function show()
    {
        $user = Auth::user();
        
        // Load appropriate profile based on user role
        if ($user->role === 'job_seeker') {
            $user->load(['jobSeekerProfile.media']);
        } elseif ($user->role === 'employer') {
            $user->load(['employerProfile.media']);
        }

        return view('profile.show', compact('user'));
    }

    /**
     * Show the form for editing the user's profile.
     */
    public function edit()
    {
        $user = Auth::user();
        
        // Load appropriate profile based on user role
        if ($user->role === 'job_seeker') {
            $user->load(['jobSeekerProfile']);
        } elseif ($user->role === 'employer') {
            $user->load(['employerProfile']);
        }

        return view('profile.edit', compact('user'));
    }

    /**
     * Update the user's profile information.
     */
    public function update(Request $request)
    {
        $user = Auth::user();
        
        // Validate basic user information
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => ['required', 'string', 'email', 'max:255', Rule::unique('users')->ignore($user->id)],
            'phone' => 'nullable|string|max:20',
            'avatar' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        // Update basic user information
        $user->update([
            'name' => $request->name,
            'email' => $request->email,
            'phone' => $request->phone,
        ]);

        // Handle avatar upload
        if ($request->hasFile('avatar')) {
            // Delete old avatar if exists
            if ($user->avatar) {
                Storage::disk('public')->delete($user->avatar);
            }
            
            $avatarPath = $request->file('avatar')->store('avatars', 'public');
            $user->update(['avatar' => $avatarPath]);
        }

        // Update role-specific profile
        if ($user->role === 'job_seeker') {
            $this->updateJobSeekerProfile($request, $user);
        } elseif ($user->role === 'employer') {
            $this->updateEmployerProfile($request, $user);
        }

        return back()->with('success', 'Profile updated successfully.');
    }

    /**
     * Update job seeker specific profile information.
     */
    private function updateJobSeekerProfile(Request $request, User $user)
    {
        $request->validate([
            'title' => 'nullable|string|max:255',
            'bio' => 'nullable|string|max:1000',
            'location' => 'nullable|string|max:255',
            'website' => 'nullable|url|max:255',
            'linkedin_url' => 'nullable|url|max:255',
            'github_url' => 'nullable|url|max:255',
            'experience_level' => 'nullable|in:entry,mid,senior,lead,executive',
            'expected_salary_min' => 'nullable|numeric|min:0',
            'expected_salary_max' => 'nullable|numeric|min:0|gte:expected_salary_min',
            'availability' => 'nullable|in:immediately,2_weeks,1_month,3_months,not_available',
            'skills' => 'nullable|array',
            'skills.*' => 'string|max:50',
            'languages' => 'nullable|array',
            'languages.*' => 'string|max:50',
            'education' => 'nullable|array',
            'education.*.degree' => 'required_with:education|string|max:255',
            'education.*.institution' => 'required_with:education|string|max:255',
            'education.*.field_of_study' => 'nullable|string|max:255',
            'education.*.start_date' => 'required_with:education|date',
            'education.*.end_date' => 'nullable|date|after:education.*.start_date',
            'education.*.description' => 'nullable|string|max:500',
            'work_experience' => 'nullable|array',
            'work_experience.*.job_title' => 'required_with:work_experience|string|max:255',
            'work_experience.*.company' => 'required_with:work_experience|string|max:255',
            'work_experience.*.location' => 'nullable|string|max:255',
            'work_experience.*.start_date' => 'required_with:work_experience|date',
            'work_experience.*.end_date' => 'nullable|date|after:work_experience.*.start_date',
            'work_experience.*.description' => 'nullable|string|max:1000',
            'work_experience.*.is_current' => 'boolean',
            'resume' => 'nullable|file|mimes:pdf,doc,docx|max:5120', // 5MB max
        ]);

        $profileData = [
            'title' => $request->title,
            'bio' => $request->bio,
            'location' => $request->location,
            'website' => $request->website,
            'linkedin_url' => $request->linkedin_url,
            'github_url' => $request->github_url,
            'experience_level' => $request->experience_level,
            'expected_salary_min' => $request->expected_salary_min,
            'expected_salary_max' => $request->expected_salary_max,
            'availability' => $request->availability,
            'skills' => $request->skills ? json_encode($request->skills) : null,
            'languages' => $request->languages ? json_encode($request->languages) : null,
            'education' => $request->education ? json_encode($request->education) : null,
            'work_experience' => $request->work_experience ? json_encode($request->work_experience) : null,
        ];

        // Handle resume upload
        if ($request->hasFile('resume')) {
            $profile = $user->jobSeekerProfile;
            
            // Delete old resume if exists
            if ($profile && $profile->resume_path) {
                Storage::disk('public')->delete($profile->resume_path);
            }
            
            $resumePath = $request->file('resume')->store('resumes', 'public');
            $profileData['resume_path'] = $resumePath;
            $profileData['resume_name'] = $request->file('resume')->getClientOriginalName();
        }

        // Update or create job seeker profile
        $user->jobSeekerProfile()->updateOrCreate(
            ['user_id' => $user->id],
            $profileData
        );
    }

    /**
     * Update employer specific profile information.
     */
    private function updateEmployerProfile(Request $request, User $user)
    {
        $request->validate([
            'company_name' => 'required|string|max:255',
            'company_description' => 'nullable|string|max:2000',
            'company_size' => 'nullable|in:1-10,11-50,51-200,201-500,501-1000,1000+',
            'industry' => 'nullable|string|max:255',
            'founded_year' => 'nullable|integer|min:1800|max:' . date('Y'),
            'website' => 'nullable|url|max:255',
            'linkedin_url' => 'nullable|url|max:255',
            'twitter_url' => 'nullable|url|max:255',
            'facebook_url' => 'nullable|url|max:255',
            'address' => 'nullable|string|max:500',
            'city' => 'nullable|string|max:255',
            'state' => 'nullable|string|max:255',
            'country' => 'nullable|string|max:255',
            'postal_code' => 'nullable|string|max:20',
            'contact_person' => 'nullable|string|max:255',
            'contact_email' => 'nullable|email|max:255',
            'contact_phone' => 'nullable|string|max:20',
            'company_logo' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'company_banner' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:5120',
        ]);

        $profileData = [
            'company_name' => $request->company_name,
            'company_description' => $request->company_description,
            'company_size' => $request->company_size,
            'industry' => $request->industry,
            'founded_year' => $request->founded_year,
            'website' => $request->website,
            'linkedin_url' => $request->linkedin_url,
            'twitter_url' => $request->twitter_url,
            'facebook_url' => $request->facebook_url,
            'address' => $request->address,
            'city' => $request->city,
            'state' => $request->state,
            'country' => $request->country,
            'postal_code' => $request->postal_code,
            'contact_person' => $request->contact_person,
            'contact_email' => $request->contact_email,
            'contact_phone' => $request->contact_phone,
        ];

        $profile = $user->employerProfile;

        // Handle company logo upload
        if ($request->hasFile('company_logo')) {
            // Delete old logo if exists
            if ($profile && $profile->company_logo) {
                Storage::disk('public')->delete($profile->company_logo);
            }
            
            $logoPath = $request->file('company_logo')->store('company-logos', 'public');
            $profileData['company_logo'] = $logoPath;
        }

        // Handle company banner upload
        if ($request->hasFile('company_banner')) {
            // Delete old banner if exists
            if ($profile && $profile->company_banner) {
                Storage::disk('public')->delete($profile->company_banner);
            }
            
            $bannerPath = $request->file('company_banner')->store('company-banners', 'public');
            $profileData['company_banner'] = $bannerPath;
        }

        // Update or create employer profile
        $user->employerProfile()->updateOrCreate(
            ['user_id' => $user->id],
            $profileData
        );
    }

    /**
     * Show the form for changing password.
     */
    public function editPassword()
    {
        return view('profile.change-password');
    }

    /**
     * Update the user's password.
     */
    public function updatePassword(Request $request)
    {
        $request->validate([
            'current_password' => 'required',
            'password' => ['required', 'confirmed', Password::defaults()],
        ]);

        $user = Auth::user();

        // Verify current password
        if (!Hash::check($request->current_password, $user->password)) {
            return back()->withErrors([
                'current_password' => 'The current password is incorrect.'
            ]);
        }

        // Update password
        $user->update([
            'password' => Hash::make($request->password)
        ]);

        return back()->with('success', 'Password updated successfully.');
    }

    /**
     * Show the form for account settings.
     */
    public function settings()
    {
        $user = Auth::user();
        return view('profile.settings', compact('user'));
    }

    /**
     * Update account settings.
     */
    public function updateSettings(Request $request)
    {
        $request->validate([
            'email_notifications' => 'boolean',
            'job_alerts' => 'boolean',
            'marketing_emails' => 'boolean',
            'profile_visibility' => 'in:public,private,employers_only',
            'timezone' => 'string|max:50',
            'language' => 'string|max:10',
        ]);

        $user = Auth::user();
        
        $settings = [
            'email_notifications' => $request->boolean('email_notifications'),
            'job_alerts' => $request->boolean('job_alerts'),
            'marketing_emails' => $request->boolean('marketing_emails'),
            'profile_visibility' => $request->profile_visibility ?? 'public',
            'timezone' => $request->timezone,
            'language' => $request->language ?? 'en',
        ];

        $user->update(['settings' => json_encode($settings)]);

        return back()->with('success', 'Settings updated successfully.');
    }

    /**
     * Download user's resume.
     */
    public function downloadResume()
    {
        $user = Auth::user();
        
        if ($user->role !== 'job_seeker') {
            abort(403, 'Only job seekers can download resumes.');
        }

        $profile = $user->jobSeekerProfile;
        
        if (!$profile || !$profile->resume_path) {
            return back()->with('error', 'No resume found.');
        }

        if (!Storage::disk('public')->exists($profile->resume_path)) {
            return back()->with('error', 'Resume file not found.');
        }

        return Storage::disk('public')->download(
            $profile->resume_path,
            $profile->resume_name ?? 'resume.pdf'
        );
    }

    /**
     * Delete user's resume.
     */
    public function deleteResume()
    {
        $user = Auth::user();
        
        if ($user->role !== 'job_seeker') {
            abort(403, 'Only job seekers can delete resumes.');
        }

        $profile = $user->jobSeekerProfile;
        
        if (!$profile || !$profile->resume_path) {
            return back()->with('error', 'No resume found.');
        }

        // Delete file from storage
        if (Storage::disk('public')->exists($profile->resume_path)) {
            Storage::disk('public')->delete($profile->resume_path);
        }

        // Update profile
        $profile->update([
            'resume_path' => null,
            'resume_name' => null,
        ]);

        return back()->with('success', 'Resume deleted successfully.');
    }

    /**
     * Delete user account.
     */
    public function destroy(Request $request)
    {
        $request->validate([
            'password' => 'required',
            'confirmation' => 'required|in:DELETE',
        ]);

        $user = Auth::user();

        // Verify password
        if (!Hash::check($request->password, $user->password)) {
            return back()->withErrors([
                'password' => 'The password is incorrect.'
            ]);
        }

        // Clean up user data
        $this->cleanupUserData($user);

        // Delete the user
        $user->delete();

        // Logout and redirect
        Auth::logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect('/')->with('success', 'Your account has been deleted successfully.');
    }

    /**
     * Clean up user data before deletion.
     */
    private function cleanupUserData(User $user)
    {
        // Delete uploaded files
        if ($user->avatar) {
            Storage::disk('public')->delete($user->avatar);
        }

        if ($user->role === 'job_seeker' && $user->jobSeekerProfile) {
            $profile = $user->jobSeekerProfile;
            
            if ($profile->resume_path) {
                Storage::disk('public')->delete($profile->resume_path);
            }
        }

        if ($user->role === 'employer' && $user->employerProfile) {
            $profile = $user->employerProfile;
            
            if ($profile->company_logo) {
                Storage::disk('public')->delete($profile->company_logo);
            }
            
            if ($profile->company_banner) {
                Storage::disk('public')->delete($profile->company_banner);
            }
        }

        // Additional cleanup can be added here
        // e.g., anonymize job applications, remove from saved jobs, etc.
    }
}