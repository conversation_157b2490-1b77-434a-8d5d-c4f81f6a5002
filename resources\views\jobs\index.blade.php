@extends('layouts.app')

@section('title', 'Find Jobs')
@section('description', 'Browse thousands of job opportunities from top companies. Find your perfect job with advanced search and filtering options.')

@section('content')
<div class="bg-white">
    <!-- Search Header -->
    <div class="bg-gradient-to-r from-blue-600 to-indigo-700 py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-8">
                <h1 class="text-3xl md:text-4xl font-bold text-white mb-4">Find Your Dream Job</h1>
                <p class="text-xl text-blue-100">Discover opportunities that match your skills and aspirations</p>
            </div>
            
            <!-- Advanced Search Form -->
            <form method="GET" action="{{ route('jobs.index') }}" class="bg-white p-6 rounded-2xl shadow-2xl">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                    <div>
                        <label for="search" class="block text-sm font-medium text-gray-700 mb-2">Job Title or Keywords</label>
                        <input type="text" name="search" id="search" value="{{ request('search') }}" 
                               placeholder="e.g. Software Engineer" 
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                    
                    <div>
                        <label for="location" class="block text-sm font-medium text-gray-700 mb-2">Location</label>
                        <input type="text" name="location" id="location" value="{{ request('location') }}" 
                               placeholder="City, state, or remote" 
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                    
                    <div>
                        <label for="type" class="block text-sm font-medium text-gray-700 mb-2">Job Type</label>
                        <select name="type" id="type" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="">All Types</option>
                            <option value="full-time" {{ request('type') === 'full-time' ? 'selected' : '' }}>Full-time</option>
                            <option value="part-time" {{ request('type') === 'part-time' ? 'selected' : '' }}>Part-time</option>
                            <option value="contract" {{ request('type') === 'contract' ? 'selected' : '' }}>Contract</option>
                            <option value="freelance" {{ request('type') === 'freelance' ? 'selected' : '' }}>Freelance</option>
                            <option value="internship" {{ request('type') === 'internship' ? 'selected' : '' }}>Internship</option>
                        </select>
                    </div>
                    
                    <div>
                        <label for="salary_min" class="block text-sm font-medium text-gray-700 mb-2">Min Salary</label>
                        <select name="salary_min" id="salary_min" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="">Any Salary</option>
                            <option value="30000" {{ request('salary_min') === '30000' ? 'selected' : '' }}>$30,000+</option>
                            <option value="50000" {{ request('salary_min') === '50000' ? 'selected' : '' }}>$50,000+</option>
                            <option value="75000" {{ request('salary_min') === '75000' ? 'selected' : '' }}>$75,000+</option>
                            <option value="100000" {{ request('salary_min') === '100000' ? 'selected' : '' }}>$100,000+</option>
                            <option value="150000" {{ request('salary_min') === '150000' ? 'selected' : '' }}>$150,000+</option>
                        </select>
                    </div>
                </div>
                
                <div class="flex flex-col sm:flex-row gap-4 items-end">
                    <div class="flex-1">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Filters</label>
                        <div class="flex flex-wrap gap-2">
                            <label class="inline-flex items-center">
                                <input type="checkbox" name="remote" value="1" {{ request('remote') ? 'checked' : '' }} class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                <span class="ml-2 text-sm text-gray-700">Remote OK</span>
                            </label>
                            <label class="inline-flex items-center">
                                <input type="checkbox" name="featured" value="1" {{ request('featured') ? 'checked' : '' }} class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                <span class="ml-2 text-sm text-gray-700">Featured Only</span>
                            </label>
                        </div>
                    </div>
                    
                    <div class="flex gap-2">
                        <button type="submit" class="bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors duration-200 flex items-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                            Search
                        </button>
                        <a href="{{ route('jobs.index') }}" class="bg-gray-200 text-gray-700 px-6 py-3 rounded-lg font-semibold hover:bg-gray-300 transition-colors duration-200">
                            Clear
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Results Section -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="flex flex-col lg:flex-row gap-8">
            <!-- Sidebar Filters -->
            <div class="lg:w-1/4">
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 sticky top-4">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Refine Results</h3>
                    
                    <!-- Company Size -->
                    <div class="mb-6">
                        <h4 class="text-sm font-medium text-gray-700 mb-3">Company Size</h4>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                <span class="ml-2 text-sm text-gray-600">Startup (1-10)</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                <span class="ml-2 text-sm text-gray-600">Small (11-50)</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                <span class="ml-2 text-sm text-gray-600">Medium (51-200)</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                <span class="ml-2 text-sm text-gray-600">Large (201+)</span>
                            </label>
                        </div>
                    </div>
                    
                    <!-- Experience Level -->
                    <div class="mb-6">
                        <h4 class="text-sm font-medium text-gray-700 mb-3">Experience Level</h4>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                <span class="ml-2 text-sm text-gray-600">Entry Level</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                <span class="ml-2 text-sm text-gray-600">Mid Level</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                <span class="ml-2 text-sm text-gray-600">Senior Level</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                <span class="ml-2 text-sm text-gray-600">Executive</span>
                            </label>
                        </div>
                    </div>
                    
                    <!-- Industry -->
                    <div>
                        <h4 class="text-sm font-medium text-gray-700 mb-3">Industry</h4>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                <span class="ml-2 text-sm text-gray-600">Technology</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                <span class="ml-2 text-sm text-gray-600">Healthcare</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                <span class="ml-2 text-sm text-gray-600">Finance</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                <span class="ml-2 text-sm text-gray-600">Education</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                <span class="ml-2 text-sm text-gray-600">Marketing</span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Job Listings -->
            <div class="lg:w-3/4">
                <!-- Results Header -->
                <div class="flex justify-between items-center mb-6">
                    <div>
                        <h2 class="text-2xl font-bold text-gray-900">Job Opportunities</h2>
                        <p class="text-gray-600">{{ $jobs->total() ?? 0 }} jobs found</p>
                    </div>
                    
                    <div class="flex items-center space-x-4">
                        <label for="sort" class="text-sm font-medium text-gray-700">Sort by:</label>
                        <select name="sort" id="sort" onchange="this.form.submit()" class="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="newest" {{ request('sort') === 'newest' ? 'selected' : '' }}>Newest First</option>
                            <option value="oldest" {{ request('sort') === 'oldest' ? 'selected' : '' }}>Oldest First</option>
                            <option value="salary_high" {{ request('sort') === 'salary_high' ? 'selected' : '' }}>Salary: High to Low</option>
                            <option value="salary_low" {{ request('sort') === 'salary_low' ? 'selected' : '' }}>Salary: Low to High</option>
                            <option value="relevance" {{ request('sort') === 'relevance' ? 'selected' : '' }}>Most Relevant</option>
                        </select>
                    </div>
                </div>
                
                <!-- Job Cards -->
                <div class="space-y-4">
                    @forelse($jobs ?? [] as $job)
                        <div class="bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300 border border-gray-200 p-6">
                            <div class="flex justify-between items-start mb-4">
                                <div class="flex items-start space-x-4">
                                    <!-- Company Logo -->
                                    <div class="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center flex-shrink-0">
                                        @if($job->company_logo)
                                            <img src="{{ $job->company_logo }}" alt="{{ $job->company_name }}" class="w-full h-full object-cover rounded-lg">
                                        @else
                                            <span class="text-xl font-semibold text-gray-600">{{ substr($job->company_name ?? 'C', 0, 1) }}</span>
                                        @endif
                                    </div>
                                    
                                    <!-- Job Info -->
                                    <div class="flex-1">
                                        <div class="flex items-center space-x-2 mb-2">
                                            <h3 class="text-xl font-semibold text-gray-900 hover:text-blue-600">
                                                <a href="{{ route('jobs.show', $job) }}">{{ $job->title }}</a>
                                            </h3>
                                            @if($job->is_featured)
                                                <span class="bg-yellow-100 text-yellow-800 text-xs font-medium px-2 py-1 rounded-full">Featured</span>
                                            @endif
                                            @if($job->is_remote)
                                                <span class="bg-green-100 text-green-800 text-xs font-medium px-2 py-1 rounded-full">Remote</span>
                                            @endif
                                        </div>
                                        
                                        <p class="text-lg text-gray-700 mb-2">{{ $job->company_name }}</p>
                                        
                                        <div class="flex flex-wrap items-center gap-4 text-sm text-gray-600">
                                            <div class="flex items-center">
                                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                </svg>
                                                {{ $job->location }}
                                            </div>
                                            
                                            <div class="flex items-center">
                                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                </svg>
                                                {{ ucfirst($job->type) }}
                                            </div>
                                            
                                            @if($job->salary_min && $job->salary_max)
                                                <div class="flex items-center">
                                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                                    </svg>
                                                    ${{ number_format($job->salary_min) }} - ${{ number_format($job->salary_max) }}
                                                </div>
                                            @endif
                                            
                                            <span>{{ $job->created_at->diffForHumans() }}</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Actions -->
                                <div class="flex items-center space-x-2">
                                    @auth
                                        <button class="p-2 text-gray-400 hover:text-red-500 transition-colors duration-200" title="Save Job">
                                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                                            </svg>
                                        </button>
                                    @endauth
                                    
                                    <a href="{{ route('jobs.show', $job) }}" class="bg-blue-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors duration-200">
                                        View Details
                                    </a>
                                </div>
                            </div>
                            
                            <!-- Job Description Preview -->
                            <p class="text-gray-600 line-clamp-2 mb-4">{{ Str::limit($job->description, 200) }}</p>
                            
                            <!-- Skills/Tags -->
                            @if($job->skills)
                                <div class="flex flex-wrap gap-2">
                                    @foreach(array_slice($job->skills, 0, 5) as $skill)
                                        <span class="bg-blue-50 text-blue-700 text-xs font-medium px-2 py-1 rounded-full">{{ $skill }}</span>
                                    @endforeach
                                    @if(count($job->skills) > 5)
                                        <span class="text-xs text-gray-500">+{{ count($job->skills) - 5 }} more</span>
                                    @endif
                                </div>
                            @endif
                        </div>
                    @empty
                        <div class="text-center py-12">
                            <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V8a2 2 0 012-2V6"></path>
                            </svg>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">No jobs found</h3>
                            <p class="text-gray-600 mb-4">Try adjusting your search criteria or browse all available positions.</p>
                            <a href="{{ route('jobs.index') }}" class="text-blue-600 hover:text-blue-700 font-medium">View all jobs</a>
                        </div>
                    @endforelse
                </div>
                
                <!-- Pagination -->
                @if(isset($jobs) && $jobs->hasPages())
                    <div class="mt-8">
                        {{ $jobs->appends(request()->query())->links() }}
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection