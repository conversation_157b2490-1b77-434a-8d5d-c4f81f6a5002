<?php

namespace App\Livewire\Admin;

use App\Models\User;
use Livewire\Component;
use Livewire\WithPagination;

class UsersTable extends Component
{
    use WithPagination;

    public $search = '';
    public $role = '';
    public $status = '';
    public $perPage = 10;
    public $sortField = 'created_at';
    public $sortDirection = 'desc';
    public $selectedUsers = [];
    public $selectAll = false;
    public $showFilters = true;

    protected $queryString = [
        'search' => ['except' => ''],
        'role' => ['except' => ''],
        'status' => ['except' => ''],
        'page' => ['except' => 1],
    ];

    protected $listeners = ['refreshComponent' => '$refresh'];

    public function mount()
    {
        $this->search = request('search', '');
        $this->role = request('role', '');
        $this->status = request('status', '');
    }

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function updatingRole()
    {
        $this->resetPage();
    }

    public function toggleFilters()
    {
        $this->showFilters = !$this->showFilters;
    }

    public function updatingStatus()
    {
        $this->resetPage();
    }

    public function sortBy($field)
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortDirection = 'asc';
        }
        $this->sortField = $field;
    }

    public function updatedSelectAll($value)
    {
        if ($value) {
            $this->selectedUsers = $this->users->pluck('id')->toArray();
        } else {
            $this->selectedUsers = [];
        }
    }

    public function getUsersProperty()
    {
        return User::query()
            ->when($this->search, function ($query) {
                $query->where(function ($q) {
                    $q->where('name', 'like', '%' . $this->search . '%')
                      ->orWhere('email', 'like', '%' . $this->search . '%')
                      ->orWhere('id', 'like', '%' . $this->search . '%');
                });
            })
            ->when($this->role, function ($query) {
                $query->where('role', $this->role);
            })
            ->when($this->status, function ($query) {
                if ($this->status === 'verified') {
                    $query->whereNotNull('email_verified_at');
                } elseif ($this->status === 'unverified') {
                    $query->whereNull('email_verified_at');
                }
            })
            ->orderBy($this->sortField, $this->sortDirection)
            ->paginate($this->perPage);
    }

    public function getStatusCountsProperty()
    {
        return [
            'all' => User::count(),
            'active' => User::whereNotNull('email_verified_at')->count(),
            'unverified' => User::whereNull('email_verified_at')->count(),
        ];
    }

    public function getRolesProperty()
    {
        return User::distinct()->pluck('role')->filter()->toArray();
    }

    public function getStatusesProperty()
    {
        return ['verified', 'unverified'];
    }

    public function clearFilters()
    {
        $this->search = '';
        $this->role = '';
        $this->status = '';
        $this->resetPage();
    }

    public function bulkDelete()
    {
        if (!empty($this->selectedUsers)) {
            User::whereIn('id', $this->selectedUsers)->delete();
            $this->selectedUsers = [];
            $this->selectAll = false;
            session()->flash('success', 'Selected users deleted successfully.');
        }
    }

    public function activateUser($userId)
    {
        $user = User::find($userId);
        if ($user) {
            $user->email_verified_at = now();
            $user->save();
            session()->flash('success', 'User activated successfully.');
        }
    }

    public function render()
    {
        return view('livewire.admin.users-table', [
            'users' => $this->users,
            'statusCounts' => $this->statusCounts,
            'roles' => $this->roles,
            'statuses' => $this->statuses,
        ]);
    }
}
