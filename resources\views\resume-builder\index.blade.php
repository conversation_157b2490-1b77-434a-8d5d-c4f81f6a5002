@extends('layouts.app')

@section('title', 'Resume Builder')

@section('content')
<div class="min-h-screen bg-gray-50 py-8">
    <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold leading-tight tracking-tight text-gray-900">Resume Builder</h1>
            <p class="mt-2 text-lg text-gray-600">Create professional resumes that get you hired</p>
        </div>

        <!-- Quick Stats -->
        <div class="mb-8 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <svg class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z" />
                            </svg>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Total Resumes</dt>
                                <dd class="text-lg font-medium text-gray-900">{{ $resumeStats['total'] ?? 0 }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <svg class="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Completed</dt>
                                <dd class="text-lg font-medium text-gray-900">{{ $resumeStats['completed'] ?? 0 }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <svg class="h-6 w-6 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">In Progress</dt>
                                <dd class="text-lg font-medium text-gray-900">{{ $resumeStats['in_progress'] ?? 0 }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <svg class="h-6 w-6 text-purple-600" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5M16.5 12L12 16.5m0 0L7.5 12m4.5 4.5V3" />
                            </svg>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Downloads</dt>
                                <dd class="text-lg font-medium text-gray-900">{{ $resumeStats['downloads'] ?? 0 }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Content -->
            <div class="lg:col-span-2 space-y-8">
                <!-- Create New Resume -->
                <div class="bg-white shadow rounded-lg">
                    <div class="px-6 py-5 border-b border-gray-200">
                        <h3 class="text-lg leading-6 font-medium text-gray-900">Create New Resume</h3>
                        <p class="mt-1 text-sm text-gray-500">Start building your professional resume with our easy-to-use templates</p>
                    </div>
                    <div class="px-6 py-5">
                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                            <a href="{{ route('resume-builder.create') }}" class="group relative bg-gradient-to-r from-blue-500 to-blue-600 p-6 rounded-lg text-white hover:from-blue-600 hover:to-blue-700 transition-all duration-200">
                                <div class="flex items-center">
                                    <svg class="h-8 w-8 mr-3" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
                                    </svg>
                                    <div>
                                        <h4 class="text-lg font-semibold">Start from Scratch</h4>
                                        <p class="text-blue-100 text-sm">Build your resume step by step</p>
                                    </div>
                                </div>
                            </a>
                            
                            <button class="group relative bg-gradient-to-r from-green-500 to-green-600 p-6 rounded-lg text-white hover:from-green-600 hover:to-green-700 transition-all duration-200">
                                <div class="flex items-center">
                                    <svg class="h-8 w-8 mr-3" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5m-13.5-9L12 3m0 0l4.5 4.5M12 3v13.5" />
                                    </svg>
                                    <div>
                                        <h4 class="text-lg font-semibold">Import Resume</h4>
                                        <p class="text-green-100 text-sm">Upload existing resume to edit</p>
                                    </div>
                                </div>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Recent Resumes -->
                <div class="bg-white shadow rounded-lg">
                    <div class="px-6 py-5 border-b border-gray-200">
                        <div class="flex items-center justify-between">
                            <h3 class="text-lg leading-6 font-medium text-gray-900">Your Resumes</h3>
                            @if($resumes->count() > 0)
                                <a href="#" class="text-sm text-blue-600 hover:text-blue-500">View all</a>
                            @endif
                        </div>
                    </div>
                    <div class="px-6 py-5">
                        @if($resumes->count() > 0)
                            <div class="space-y-4">
                                @foreach($resumes as $resume)
                                    <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:border-gray-300 transition-colors">
                                        <div class="flex items-center space-x-4">
                                            <div class="flex-shrink-0">
                                                <div class="w-12 h-16 bg-gray-100 rounded border-2 border-gray-200 flex items-center justify-center">
                                                    <svg class="w-6 h-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z" />
                                                    </svg>
                                                </div>
                                            </div>
                                            <div class="flex-1">
                                                <h4 class="text-sm font-medium text-gray-900">{{ $resume->title ?? 'Untitled Resume' }}</h4>
                                                <p class="text-sm text-gray-500">{{ $resume->template ?? 'Default Template' }}</p>
                                                <div class="flex items-center mt-1 space-x-4">
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                                        @if($resume->status === 'completed') bg-green-100 text-green-800
                                                        @elseif($resume->status === 'in_progress') bg-yellow-100 text-yellow-800
                                                        @else bg-gray-100 text-gray-800 @endif">
                                                        {{ ucfirst(str_replace('_', ' ', $resume->status)) }}
                                                    </span>
                                                    <span class="text-xs text-gray-500">Updated {{ $resume->updated_at->diffForHumans() }}</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="flex items-center space-x-2">
                                            <a href="{{ route('resume-builder.edit', $resume) }}" class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                                <svg class="-ml-0.5 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L10.582 16.07a4.5 4.5 0 01-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 011.13-1.897l8.932-8.931zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0115.75 21H5.25A2.25 2.25 0 013 18.75V8.25A2.25 2.25 0 015.25 6H10" />
                                                </svg>
                                                Edit
                                            </a>
                                            @if($resume->status === 'completed')
                                                <a href="{{ route('resume-builder.download', $resume) }}" class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                                    <svg class="-ml-0.5 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" d="M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5M16.5 12L12 16.5m0 0L7.5 12m4.5 4.5V3" />
                                                    </svg>
                                                    Download
                                                </a>
                                            @endif
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        @else
                            <div class="text-center py-12">
                                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z" />
                                </svg>
                                <h3 class="mt-2 text-sm font-medium text-gray-900">No resumes yet</h3>
                                <p class="mt-1 text-sm text-gray-500">Get started by creating your first professional resume.</p>
                                <div class="mt-6">
                                    <a href="{{ route('resume-builder.create') }}" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                        <svg class="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
                                        </svg>
                                        Create Resume
                                    </a>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="lg:col-span-1 space-y-6">
                <!-- Templates -->
                <div class="bg-white shadow rounded-lg">
                    <div class="px-6 py-5 border-b border-gray-200">
                        <h3 class="text-lg leading-6 font-medium text-gray-900">Popular Templates</h3>
                        <p class="mt-1 text-sm text-gray-500">Choose from our professional designs</p>
                    </div>
                    <div class="px-6 py-5">
                        <div class="grid grid-cols-2 gap-3">
                            <div class="group cursor-pointer">
                                <div class="aspect-[3/4] bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg border-2 border-blue-200 group-hover:border-blue-300 transition-colors flex items-center justify-center">
                                    <div class="text-center">
                                        <div class="w-8 h-1 bg-blue-300 mx-auto mb-2"></div>
                                        <div class="w-6 h-1 bg-blue-200 mx-auto mb-1"></div>
                                        <div class="w-10 h-1 bg-blue-200 mx-auto mb-2"></div>
                                        <div class="w-4 h-1 bg-blue-200 mx-auto"></div>
                                    </div>
                                </div>
                                <p class="mt-2 text-xs text-center text-gray-600">Modern</p>
                            </div>
                            
                            <div class="group cursor-pointer">
                                <div class="aspect-[3/4] bg-gradient-to-br from-green-50 to-green-100 rounded-lg border-2 border-green-200 group-hover:border-green-300 transition-colors flex items-center justify-center">
                                    <div class="text-center">
                                        <div class="w-8 h-1 bg-green-300 mx-auto mb-2"></div>
                                        <div class="w-6 h-1 bg-green-200 mx-auto mb-1"></div>
                                        <div class="w-10 h-1 bg-green-200 mx-auto mb-2"></div>
                                        <div class="w-4 h-1 bg-green-200 mx-auto"></div>
                                    </div>
                                </div>
                                <p class="mt-2 text-xs text-center text-gray-600">Classic</p>
                            </div>
                            
                            <div class="group cursor-pointer">
                                <div class="aspect-[3/4] bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg border-2 border-purple-200 group-hover:border-purple-300 transition-colors flex items-center justify-center">
                                    <div class="text-center">
                                        <div class="w-8 h-1 bg-purple-300 mx-auto mb-2"></div>
                                        <div class="w-6 h-1 bg-purple-200 mx-auto mb-1"></div>
                                        <div class="w-10 h-1 bg-purple-200 mx-auto mb-2"></div>
                                        <div class="w-4 h-1 bg-purple-200 mx-auto"></div>
                                    </div>
                                </div>
                                <p class="mt-2 text-xs text-center text-gray-600">Creative</p>
                            </div>
                            
                            <div class="group cursor-pointer">
                                <div class="aspect-[3/4] bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg border-2 border-gray-200 group-hover:border-gray-300 transition-colors flex items-center justify-center">
                                    <div class="text-center">
                                        <div class="w-8 h-1 bg-gray-300 mx-auto mb-2"></div>
                                        <div class="w-6 h-1 bg-gray-200 mx-auto mb-1"></div>
                                        <div class="w-10 h-1 bg-gray-200 mx-auto mb-2"></div>
                                        <div class="w-4 h-1 bg-gray-200 mx-auto"></div>
                                    </div>
                                </div>
                                <p class="mt-2 text-xs text-center text-gray-600">Minimal</p>
                            </div>
                        </div>
                        <div class="mt-4">
                            <a href="#" class="text-sm text-blue-600 hover:text-blue-500">View all templates →</a>
                        </div>
                    </div>
                </div>

                <!-- Tips -->
                <div class="bg-white shadow rounded-lg">
                    <div class="px-6 py-5 border-b border-gray-200">
                        <h3 class="text-lg leading-6 font-medium text-gray-900">Resume Tips</h3>
                    </div>
                    <div class="px-6 py-5">
                        <div class="space-y-4">
                            <div class="flex items-start space-x-3">
                                <div class="flex-shrink-0">
                                    <div class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                                        <svg class="w-3 h-3 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                                        </svg>
                                    </div>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-900 font-medium">Keep it concise</p>
                                    <p class="text-xs text-gray-500">Aim for 1-2 pages maximum</p>
                                </div>
                            </div>
                            
                            <div class="flex items-start space-x-3">
                                <div class="flex-shrink-0">
                                    <div class="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center">
                                        <svg class="w-3 h-3 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                                        </svg>
                                    </div>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-900 font-medium">Use action verbs</p>
                                    <p class="text-xs text-gray-500">Start bullet points with strong verbs</p>
                                </div>
                            </div>
                            
                            <div class="flex items-start space-x-3">
                                <div class="flex-shrink-0">
                                    <div class="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center">
                                        <svg class="w-3 h-3 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                                        </svg>
                                    </div>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-900 font-medium">Quantify achievements</p>
                                    <p class="text-xs text-gray-500">Include numbers and percentages</p>
                                </div>
                            </div>
                            
                            <div class="flex items-start space-x-3">
                                <div class="flex-shrink-0">
                                    <div class="w-6 h-6 bg-yellow-100 rounded-full flex items-center justify-center">
                                        <svg class="w-3 h-3 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                                        </svg>
                                    </div>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-900 font-medium">Tailor for each job</p>
                                    <p class="text-xs text-gray-500">Customize for specific positions</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Resources -->
                <div class="bg-white shadow rounded-lg">
                    <div class="px-6 py-5 border-b border-gray-200">
                        <h3 class="text-lg leading-6 font-medium text-gray-900">Resources</h3>
                    </div>
                    <div class="px-6 py-5">
                        <div class="space-y-3">
                            <a href="#" class="block text-sm text-blue-600 hover:text-blue-500">
                                📝 Resume Writing Guide
                            </a>
                            <a href="#" class="block text-sm text-blue-600 hover:text-blue-500">
                                💼 Cover Letter Templates
                            </a>
                            <a href="#" class="block text-sm text-blue-600 hover:text-blue-500">
                                🎯 Interview Preparation
                            </a>
                            <a href="#" class="block text-sm text-blue-600 hover:text-blue-500">
                                📊 Salary Negotiation Tips
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection