<?php

use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Schedule;

Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote');

// Schedule job alerts
Schedule::command('job-alerts:send --frequency=daily')
    ->dailyAt('09:00')
    ->description('Send daily job alerts to users');

Schedule::command('job-alerts:send --frequency=weekly')
    ->weeklyOn(1, '09:00') // Monday at 9 AM
    ->description('Send weekly job alerts to users');
