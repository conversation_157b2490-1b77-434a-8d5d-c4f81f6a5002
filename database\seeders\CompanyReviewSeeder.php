<?php

namespace Database\Seeders;

use App\Models\CompanyReview;
use App\Models\EmployerProfile;
use App\Models\User;
use Illuminate\Database\Seeder;

class CompanyReviewSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $employerProfiles = EmployerProfile::all();
        $jobSeekers = User::where('role', 'job_seeker')->get();
        
        if ($employerProfiles->isEmpty() || $jobSeekers->isEmpty()) {
            $this->command->warn('No employer profiles or job seekers found. Please run UserSeeder first.');
            return;
        }

        foreach ($employerProfiles as $employer) {
            // Each company gets 0-15 reviews
            $reviewCount = fake()->numberBetween(0, 15);
            $reviewers = $jobSeekers->random(min($reviewCount, $jobSeekers->count()));
            
            foreach ($reviewers as $reviewer) {
                // Check if user already reviewed this company
                $existingReview = CompanyReview::where('employer_profile_id', $employer->id)
                    ->where('user_id', $reviewer->id)
                    ->first();
                    
                if ($existingReview) {
                    continue;
                }

                $overallRating = fake()->numberBetween(1, 5);
                $reviewData = $this->generateReviewData($employer, $overallRating);
                
                CompanyReview::create([
                    'employer_profile_id' => $employer->id,
                    'user_id' => $reviewer->id,
                    'overall_rating' => $overallRating,
                    'work_life_balance_rating' => fake()->numberBetween(max(1, $overallRating - 1), min(5, $overallRating + 1)),
                    'compensation_rating' => fake()->numberBetween(max(1, $overallRating - 1), min(5, $overallRating + 1)),
                    'career_growth_rating' => fake()->numberBetween(max(1, $overallRating - 1), min(5, $overallRating + 1)),
                    'management_rating' => fake()->numberBetween(max(1, $overallRating - 1), min(5, $overallRating + 1)),
                    'culture_rating' => fake()->numberBetween(max(1, $overallRating - 1), min(5, $overallRating + 1)),
                    'title' => $reviewData['title'],
                    'review_text' => $reviewData['review_text'],
                    'pros' => $reviewData['pros'],
                    'cons' => $reviewData['cons'],
                    'advice_to_management' => fake()->optional(0.6)->paragraph(),
                    'employment_status' => fake()->randomElement(['current', 'former']),
                    'job_title' => fake()->jobTitle(),
                    'years_at_company' => fake()->randomFloat(1, 0.5, 8.0),
                    'is_anonymous' => fake()->boolean(70),
                    'is_approved' => fake()->boolean(90),
                    'helpful_count' => fake()->numberBetween(0, 25),
                    'created_at' => fake()->dateTimeBetween('-2 years', 'now'),
                ]);
            }
        }

        $this->command->info('Company reviews created successfully!');
    }

    private function generateReviewData($employer, $rating)
    {
        $companyName = $employer->company_name;
        
        if ($rating >= 4) {
            return $this->generatePositiveReview($companyName);
        } elseif ($rating >= 3) {
            return $this->generateNeutralReview($companyName);
        } else {
            return $this->generateNegativeReview($companyName);
        }
    }

    private function generatePositiveReview($companyName)
    {
        $titles = [
            "Great place to work!",
            "Excellent company culture",
            "Amazing team and growth opportunities",
            "Highly recommend {$companyName}",
            "Best job I've ever had",
            "Outstanding work environment",
            "Fantastic company to grow your career"
        ];

        $reviews = [
            "I've been working at {$companyName} for over a year now and it's been an incredible experience. The team is supportive, management is transparent, and there are plenty of opportunities for professional growth. The work-life balance is excellent and the company truly cares about its employees.",
            "{$companyName} has exceeded all my expectations. The collaborative environment, competitive compensation, and innovative projects make this an ideal workplace. Management is approachable and genuinely invested in employee success.",
            "Working at {$companyName} has been transformative for my career. The company provides excellent training, mentorship opportunities, and a clear path for advancement. The culture is inclusive and the work is meaningful."
        ];

        $pros = [
            "Excellent work-life balance",
            "Competitive salary and benefits",
            "Supportive management team",
            "Great learning opportunities",
            "Collaborative work environment",
            "Clear career progression path",
            "Innovative and challenging projects",
            "Flexible working arrangements"
        ];

        $cons = [
            "Sometimes the pace can be fast",
            "Limited parking space",
            "Could use more team building events",
            "Office could be more modern",
            "Sometimes meetings run long"
        ];

        return [
            'title' => fake()->randomElement($titles),
            'review_text' => fake()->randomElement($reviews),
            'pros' => fake()->randomElements($pros, fake()->numberBetween(3, 6)),
            'cons' => fake()->randomElements($cons, fake()->numberBetween(1, 3))
        ];
    }

    private function generateNeutralReview($companyName)
    {
        $titles = [
            "Decent place to work",
            "Mixed experience at {$companyName}",
            "Good company with room for improvement",
            "Average workplace",
            "Okay experience overall"
        ];

        $reviews = [
            "My experience at {$companyName} has been mixed. There are some great aspects like the team collaboration and interesting projects, but there are also areas that need improvement such as communication and work-life balance. Overall, it's a decent place to work.",
            "{$companyName} is an okay company to work for. The pay is fair and the work is interesting, but management could be more supportive and there could be better growth opportunities. It's not bad, but not exceptional either.",
            "Working at {$companyName} has its ups and downs. The company has potential and some good people, but there are organizational challenges that impact the work experience. With some improvements, it could be a great place to work."
        ];

        $pros = [
            "Decent compensation",
            "Interesting work projects",
            "Good team members",
            "Stable company",
            "Learning opportunities available",
            "Flexible schedule sometimes"
        ];

        $cons = [
            "Limited career advancement",
            "Communication could be better",
            "Work-life balance needs improvement",
            "Management style varies",
            "Benefits could be better",
            "Office environment needs updating",
            "Processes can be inefficient"
        ];

        return [
            'title' => fake()->randomElement($titles),
            'review_text' => fake()->randomElement($reviews),
            'pros' => fake()->randomElements($pros, fake()->numberBetween(2, 4)),
            'cons' => fake()->randomElements($cons, fake()->numberBetween(3, 5))
        ];
    }

    private function generateNegativeReview($companyName)
    {
        $titles = [
            "Not recommended",
            "Disappointing experience",
            "Poor management and culture",
            "Would not work here again",
            "Needs major improvements",
            "Stressful work environment"
        ];

        $reviews = [
            "Unfortunately, my experience at {$companyName} was quite disappointing. The management style is outdated, there's little support for professional development, and the work-life balance is poor. The company needs significant improvements to create a better work environment.",
            "I cannot recommend {$companyName} as a good place to work. The culture is toxic, management is unresponsive to employee concerns, and there are limited opportunities for growth. The high turnover rate speaks for itself.",
            "Working at {$companyName} was a challenging experience for all the wrong reasons. Poor communication, unrealistic expectations, and lack of work-life balance made it difficult to perform well or feel satisfied with the job."
        ];

        $pros = [
            "Some good colleagues",
            "Stable paycheck",
            "Learning experience (what not to do)",
            "Good location",
            "Some interesting projects"
        ];

        $cons = [
            "Poor management",
            "No work-life balance",
            "Limited growth opportunities",
            "Toxic work culture",
            "Poor communication",
            "Unrealistic expectations",
            "High turnover rate",
            "Outdated processes",
            "Lack of employee support",
            "Below market compensation"
        ];

        return [
            'title' => fake()->randomElement($titles),
            'review_text' => fake()->randomElement($reviews),
            'pros' => fake()->randomElements($pros, fake()->numberBetween(1, 3)),
            'cons' => fake()->randomElements($cons, fake()->numberBetween(4, 7))
        ];
    }
}