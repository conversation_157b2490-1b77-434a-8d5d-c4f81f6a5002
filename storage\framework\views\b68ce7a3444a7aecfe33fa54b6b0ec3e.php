<?php $__env->startSection('title', 'Page Not Found'); ?>

<?php $__env->startSection('content'); ?>
<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8 text-center">
        <div>
            <div class="mx-auto h-32 w-32 text-purple-600">
                <svg fill="none" viewBox="0 0 24 24" stroke="currentColor" class="w-full h-full">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.29-1.009-5.824-2.562M15 6.306a7.962 7.962 0 00-6 0m6 0V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6a2 2 0 01-2 2H8a2 2 0 01-2-2V8a2 2 0 012-2V6"></path>
                </svg>
            </div>
            <h2 class="mt-6 text-6xl font-extrabold text-gray-900">404</h2>
            <h3 class="mt-2 text-3xl font-bold text-gray-900">Page not found</h3>
            <p class="mt-2 text-sm text-gray-600">
                Sorry, we couldn't find the page you're looking for.
            </p>
        </div>
        
        <div class="mt-8 space-y-4">
            <div class="flex justify-center space-x-4">
                <a href="<?php echo e(route('home')); ?>" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500">
                    <svg class="mr-2 -ml-1 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                    </svg>
                    Go home
                </a>
                
                <button onclick="history.back()" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500">
                    <svg class="mr-2 -ml-1 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Go back
                </a>
            </div>
            
            <div class="mt-6">
                <p class="text-sm text-gray-500">
                    If you think this is an error, please 
                    <a href="mailto:<EMAIL>" class="text-purple-600 hover:text-purple-500">contact support</a>.
                </p>
            </div>
        </div>

        <!-- Helpful Links -->
        <div class="mt-8 border-t border-gray-200 pt-8">
            <h4 class="text-sm font-medium text-gray-900 mb-4">You might be looking for:</h4>
            <div class="grid grid-cols-1 gap-2 text-sm">
                <a href="<?php echo e(route('jobs.index')); ?>" class="text-purple-600 hover:text-purple-500">Browse Jobs</a>
                <a href="<?php echo e(route('courses.index')); ?>" class="text-purple-600 hover:text-purple-500">View Courses</a>
                <?php if(auth()->guard()->check()): ?>
                    <?php if(auth()->user()->role === 'job_seeker'): ?>
                        <a href="<?php echo e(route('dashboard')); ?>" class="text-purple-600 hover:text-purple-500">My Dashboard</a>
                        <a href="<?php echo e(route('course-enrollments.index')); ?>" class="text-purple-600 hover:text-purple-500">My Learning</a>
                    <?php elseif(auth()->user()->role === 'employer'): ?>
                        <a href="<?php echo e(route('employer.dashboard')); ?>" class="text-purple-600 hover:text-purple-500">Employer Dashboard</a>
                        <a href="<?php echo e(route('employer.jobs.index')); ?>" class="text-purple-600 hover:text-purple-500">My Jobs</a>
                    <?php elseif(auth()->user()->role === 'admin'): ?>
                        <a href="<?php echo e(route('admin.dashboard')); ?>" class="text-purple-600 hover:text-purple-500">Admin Dashboard</a>
                    <?php endif; ?>
                <?php else: ?>
                    <a href="<?php echo e(route('login')); ?>" class="text-purple-600 hover:text-purple-500">Sign In</a>
                    <a href="<?php echo e(route('register')); ?>" class="text-purple-600 hover:text-purple-500">Create Account</a>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\laravel\rectra\resources\views/errors/404.blade.php ENDPATH**/ ?>