@extends('layouts.app')

@section('title', $job->title . ' at ' . $job->company_name)
@section('description', Str::limit($job->description, 160))

@section('content')
<div class="bg-white">
    <!-- Job Header -->
    <div class="bg-gradient-to-r from-blue-600 to-indigo-700 py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                <div class="flex items-start space-x-6">
                    <!-- Company Logo -->
                    <div class="w-20 h-20 bg-white rounded-xl flex items-center justify-center flex-shrink-0 shadow-lg">
                        @if($job->company_logo)
                            <img src="{{ $job->company_logo }}" alt="{{ $job->company_name }}" class="w-full h-full object-cover rounded-xl">
                        @else
                            <span class="text-2xl font-bold text-gray-600">{{ substr($job->company_name ?? 'C', 0, 1) }}</span>
                        @endif
                    </div>
                    
                    <!-- Job Info -->
                    <div class="text-white">
                        <div class="flex items-center space-x-3 mb-2">
                            <h1 class="text-3xl md:text-4xl font-bold">{{ $job->title }}</h1>
                            @if($job->is_featured)
                                <span class="bg-yellow-400 text-yellow-900 text-sm font-medium px-3 py-1 rounded-full">Featured</span>
                            @endif
                        </div>
                        
                        <h2 class="text-xl text-blue-100 mb-4">{{ $job->company_name }}</h2>
                        
                        <div class="flex flex-wrap items-center gap-6 text-blue-100">
                            <div class="flex items-center">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                                {{ $job->location }}
                                @if($job->is_remote)
                                    <span class="ml-2 bg-green-500 text-white text-xs font-medium px-2 py-1 rounded-full">Remote</span>
                                @endif
                            </div>
                            
                            <div class="flex items-center">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                {{ ucfirst($job->type) }}
                            </div>
                            
                            @if($job->salary_min && $job->salary_max)
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                    </svg>
                                    ${{ number_format($job->salary_min) }} - ${{ number_format($job->salary_max) }}
                                </div>
                            @endif
                            
                            <div class="flex items-center">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 0V6a2 2 0 012-2h4a2 2 0 012 2v1m-6 0h8m-9 0a1 1 0 00-1 1v10a1 1 0 001 1h10a1 1 0 001-1V8a1 1 0 00-1-1H7z"></path>
                                </svg>
                                {{ $job->experience_level ?? 'Not specified' }}
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Action Buttons -->
                <div class="mt-6 lg:mt-0 flex flex-col sm:flex-row gap-3">
                    @auth
                        @if(auth()->user()->isJobSeeker())
                            <button id="saveJobBtn" class="bg-white/20 text-white border border-white/30 px-6 py-3 rounded-lg font-semibold hover:bg-white/30 transition-colors duration-200 flex items-center justify-center">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                                </svg>
                                Save Job
                            </button>
                            
                            <button id="applyBtn" class="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors duration-200 flex items-center justify-center">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                                </svg>
                                Apply Now
                            </button>
                        @endif
                    @else
                        <a href="{{ route('login') }}" class="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors duration-200 text-center">
                            Sign in to Apply
                        </a>
                    @endauth
                </div>
            </div>
        </div>
    </div>
    
    <!-- Job Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Content -->
            <div class="lg:col-span-2">
                <!-- Job Description -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-8 mb-8">
                    <h3 class="text-2xl font-bold text-gray-900 mb-6">Job Description</h3>
                    <div class="prose prose-blue max-w-none">
                        {!! nl2br(e($job->description)) !!}
                    </div>
                </div>
                
                <!-- Requirements -->
                @if($job->requirements)
                    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-8 mb-8">
                        <h3 class="text-2xl font-bold text-gray-900 mb-6">Requirements</h3>
                        <div class="prose prose-blue max-w-none">
                            {!! nl2br(e($job->requirements)) !!}
                        </div>
                    </div>
                @endif
                
                <!-- Benefits -->
                @if($job->benefits)
                    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-8 mb-8">
                        <h3 class="text-2xl font-bold text-gray-900 mb-6">Benefits & Perks</h3>
                        <div class="prose prose-blue max-w-none">
                            {!! nl2br(e($job->benefits)) !!}
                        </div>
                    </div>
                @endif
                
                <!-- Skills -->
                @if($job->skills && count($job->skills) > 0)
                    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
                        <h3 class="text-2xl font-bold text-gray-900 mb-6">Required Skills</h3>
                        <div class="flex flex-wrap gap-3">
                            @foreach($job->skills as $skill)
                                <span class="bg-blue-50 text-blue-700 px-4 py-2 rounded-full text-sm font-medium">{{ $skill }}</span>
                            @endforeach
                        </div>
                    </div>
                @endif
            </div>
            
            <!-- Sidebar -->
            <div class="lg:col-span-1">
                <!-- Quick Apply Card -->
                @auth
                    @if(auth()->user()->isJobSeeker())
                        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6 sticky top-4">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Apply</h3>
                            <p class="text-gray-600 mb-4">Apply with your Rectra profile in just one click.</p>
                            
                            <button class="w-full bg-blue-600 text-white py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors duration-200 mb-3">
                                Apply with Profile
                            </button>
                            
                            <button class="w-full border border-gray-300 text-gray-700 py-3 rounded-lg font-semibold hover:bg-gray-50 transition-colors duration-200">
                                Upload Custom Resume
                            </button>
                        </div>
                    @endif
                @endauth
                
                <!-- Job Details -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Job Details</h3>
                    
                    <div class="space-y-4">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Job Type</span>
                            <span class="font-medium text-gray-900">{{ ucfirst($job->type) }}</span>
                        </div>
                        
                        <div class="flex justify-between">
                            <span class="text-gray-600">Experience</span>
                            <span class="font-medium text-gray-900">{{ $job->experience_level ?? 'Not specified' }}</span>
                        </div>
                        
                        @if($job->salary_min && $job->salary_max)
                            <div class="flex justify-between">
                                <span class="text-gray-600">Salary Range</span>
                                <span class="font-medium text-gray-900">${{ number_format($job->salary_min) }} - ${{ number_format($job->salary_max) }}</span>
                            </div>
                        @endif
                        
                        <div class="flex justify-between">
                            <span class="text-gray-600">Remote Work</span>
                            <span class="font-medium text-gray-900">{{ $job->is_remote ? 'Yes' : 'No' }}</span>
                        </div>
                        
                        <div class="flex justify-between">
                            <span class="text-gray-600">Posted</span>
                            <span class="font-medium text-gray-900">{{ $job->created_at->diffForHumans() }}</span>
                        </div>
                        
                        @if($job->application_deadline)
                            <div class="flex justify-between">
                                <span class="text-gray-600">Deadline</span>
                                <span class="font-medium text-gray-900">{{ $job->application_deadline->format('M j, Y') }}</span>
                            </div>
                        @endif
                        
                        <div class="flex justify-between">
                            <span class="text-gray-600">Applications</span>
                            <span class="font-medium text-gray-900">{{ $job->applications_count ?? 0 }}</span>
                        </div>
                    </div>
                </div>
                
                <!-- Company Info -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">About {{ $job->company_name }}</h3>
                    
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mr-4">
                            @if($job->company_logo)
                                <img src="{{ $job->company_logo }}" alt="{{ $job->company_name }}" class="w-full h-full object-cover rounded-lg">
                            @else
                                <span class="text-lg font-semibold text-gray-600">{{ substr($job->company_name ?? 'C', 0, 1) }}</span>
                            @endif
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-900">{{ $job->company_name }}</h4>
                            <p class="text-sm text-gray-600">{{ $job->company_industry ?? 'Technology' }}</p>
                        </div>
                    </div>
                    
                    @if($job->company_description)
                        <p class="text-gray-600 mb-4">{{ Str::limit($job->company_description, 200) }}</p>
                    @endif
                    
                    <div class="space-y-2 text-sm">
                        @if($job->company_size)
                            <div class="flex justify-between">
                                <span class="text-gray-600">Company Size</span>
                                <span class="font-medium text-gray-900">{{ $job->company_size }}</span>
                            </div>
                        @endif
                        
                        @if($job->company_website)
                            <div class="flex justify-between">
                                <span class="text-gray-600">Website</span>
                                <a href="{{ $job->company_website }}" target="_blank" class="text-blue-600 hover:text-blue-700 font-medium">Visit Site</a>
                            </div>
                        @endif
                    </div>
                    
                    <a href="{{ route('companies.show', $job->company_id ?? 1) }}" class="mt-4 w-full border border-gray-300 text-gray-700 py-2 rounded-lg font-medium hover:bg-gray-50 transition-colors duration-200 text-center block">
                        View Company Profile
                    </a>
                </div>
                
                <!-- Similar Jobs -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Similar Jobs</h3>
                    
                    <div class="space-y-4">
                        @forelse($similarJobs ?? [] as $similarJob)
                            <div class="border-b border-gray-100 pb-4 last:border-b-0 last:pb-0">
                                <h4 class="font-medium text-gray-900 hover:text-blue-600">
                                    <a href="{{ route('jobs.show', $similarJob) }}">{{ $similarJob->title }}</a>
                                </h4>
                                <p class="text-sm text-gray-600">{{ $similarJob->company_name }}</p>
                                <p class="text-sm text-gray-500">{{ $similarJob->location }}</p>
                            </div>
                        @empty
                            <p class="text-gray-500 text-sm">No similar jobs found.</p>
                        @endforelse
                    </div>
                    
                    <a href="{{ route('jobs.index', ['search' => $job->title]) }}" class="mt-4 text-blue-600 hover:text-blue-700 text-sm font-medium">
                        View more similar jobs →
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Application Modal -->
@auth
    @if(auth()->user()->isJobSeeker())
        <div id="applicationModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
            <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
                <div class="mt-3">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-semibold text-gray-900">Apply for {{ $job->title }}</h3>
                        <button id="closeModal" class="text-gray-400 hover:text-gray-600">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                    
                    <form id="applicationForm" class="space-y-4">
                        @csrf
                        <input type="hidden" name="job_id" value="{{ $job->id }}">
                        
                        <div>
                            <label for="cover_letter" class="block text-sm font-medium text-gray-700 mb-2">Cover Letter</label>
                            <textarea name="cover_letter" id="cover_letter" rows="6" 
                                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                      placeholder="Tell us why you're perfect for this role..."></textarea>
                        </div>
                        
                        <div>
                            <label for="resume" class="block text-sm font-medium text-gray-700 mb-2">Resume</label>
                            <select name="resume_id" id="resume" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option value="">Use profile resume</option>
                                <!-- Resume options would be populated here -->
                            </select>
                        </div>
                        
                        <div class="flex justify-end space-x-3 pt-4">
                            <button type="button" id="cancelApplication" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                                Cancel
                            </button>
                            <button type="submit" class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                                Submit Application
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    @endif
@endauth

@push('scripts')
<script>
    // Application modal functionality
    document.addEventListener('DOMContentLoaded', function() {
        const applyBtn = document.getElementById('applyBtn');
        const modal = document.getElementById('applicationModal');
        const closeModal = document.getElementById('closeModal');
        const cancelBtn = document.getElementById('cancelApplication');
        
        if (applyBtn && modal) {
            applyBtn.addEventListener('click', function() {
                modal.classList.remove('hidden');
            });
            
            [closeModal, cancelBtn].forEach(btn => {
                if (btn) {
                    btn.addEventListener('click', function() {
                        modal.classList.add('hidden');
                    });
                }
            });
            
            // Close modal when clicking outside
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    modal.classList.add('hidden');
                }
            });
        }
        
        // Save job functionality
        const saveBtn = document.getElementById('saveJobBtn');
        if (saveBtn) {
            saveBtn.addEventListener('click', function() {
                // Add save job functionality here
                console.log('Save job clicked');
            });
        }
    });
</script>
@endpush
@endsection