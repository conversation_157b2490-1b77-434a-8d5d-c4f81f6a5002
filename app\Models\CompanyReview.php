<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;

class CompanyReview extends Model
{
    use HasFactory;

    protected $fillable = [
        'job_seeker_profile_id',
        'employer_profile_id',
        'rating',
        'title',
        'body',
        'pros',
        'cons',
        'employment_status',
        'job_title',
        'is_approved',
        'is_anonymous',
        'approved_at',
    ];

    protected $casts = [
        'pros' => 'array',
        'cons' => 'array',
        'is_approved' => 'boolean',
        'is_anonymous' => 'boolean',
        'approved_at' => 'datetime',
    ];

    /**
     * Get the job seeker profile that wrote the review.
     */
    public function jobSeekerProfile()
    {
        return $this->belongsTo(JobSeekerProfile::class);
    }

    /**
     * Get the employer profile being reviewed.
     */
    public function employerProfile()
    {
        return $this->belongsTo(EmployerProfile::class);
    }

    /**
     * Scope a query to only include approved reviews.
     */
    public function scopeApproved(Builder $query): Builder
    {
        return $query->where('is_approved', true);
    }

    /**
     * Scope a query to only include pending reviews.
     */
    public function scopePending(Builder $query): Builder
    {
        return $query->where('is_approved', false);
    }

    /**
     * Scope a query to filter by rating.
     */
    public function scopeRating(Builder $query, int $rating): Builder
    {
        return $query->where('rating', $rating);
    }

    /**
     * Scope a query to filter by minimum rating.
     */
    public function scopeMinRating(Builder $query, int $minRating): Builder
    {
        return $query->where('rating', '>=', $minRating);
    }

    /**
     * Approve the review.
     */
    public function approve(): void
    {
        $this->update([
            'is_approved' => true,
            'approved_at' => now(),
        ]);
    }

    /**
     * Reject the review.
     */
    public function reject(): void
    {
        $this->update([
            'is_approved' => false,
            'approved_at' => null,
        ]);
    }

    /**
     * Get the reviewer's name (anonymous if needed).
     */
    public function getReviewerNameAttribute(): string
    {
        if ($this->is_anonymous) {
            return 'Anonymous';
        }

        return $this->jobSeekerProfile->user->name ?? 'Unknown';
    }

    /**
     * Get the rating as stars.
     */
    public function getRatingStarsAttribute(): string
    {
        return str_repeat('★', $this->rating) . str_repeat('☆', 5 - $this->rating);
    }

    /**
     * Get the rating color class.
     */
    public function getRatingColorAttribute(): string
    {
        return match($this->rating) {
            1, 2 => 'text-red-500',
            3 => 'text-yellow-500',
            4, 5 => 'text-green-500',
            default => 'text-gray-500'
        };
    }

    /**
     * Check if the review is positive (4-5 stars).
     */
    public function isPositive(): bool
    {
        return $this->rating >= 4;
    }

    /**
     * Check if the review is negative (1-2 stars).
     */
    public function isNegative(): bool
    {
        return $this->rating <= 2;
    }

    /**
     * Check if the review is neutral (3 stars).
     */
    public function isNeutral(): bool
    {
        return $this->rating === 3;
    }
}