<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->call([
            // Core data seeders
            SubscriptionPlanSeeder::class,
            
            // User and profile seeders
            UserSeeder::class,
            SubscriptionSeeder::class,
            
            // Course related seeders
            CourseSeeder::class,
            CourseEnrollmentSeeder::class,
            
            // Job related seeders
            JobSeeder::class,
            ResumeSeeder::class,
            JobApplicationSeeder::class,
            
            // User interaction seeders
            SavedJobSeeder::class,
            SavedSearchSeeder::class,
            CompanyReviewSeeder::class,
        ]);

        $this->command->info('All seeders completed successfully!');
        $this->command->info('You can now test the application with realistic data.');
    }
}
