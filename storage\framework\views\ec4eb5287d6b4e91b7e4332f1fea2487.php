<?php $__env->startSection('title', 'Course Analytics'); ?>

<?php $__env->startSection('content'); ?>
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Course Analytics</h1>
            <p class="text-gray-600">Detailed insights into course performance and enrollments</p>
        </div>
        <div class="flex items-center space-x-4">
            <!-- Period Filter -->
            <form method="GET" class="flex items-center space-x-2">
                <select name="period" onchange="this.form.submit()" class="rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500">
                    <option value="7" <?php echo e($period == '7' ? 'selected' : ''); ?>>Last 7 days</option>
                    <option value="30" <?php echo e($period == '30' ? 'selected' : ''); ?>>Last 30 days</option>
                    <option value="90" <?php echo e($period == '90' ? 'selected' : ''); ?>>Last 90 days</option>
                    <option value="365" <?php echo e($period == '365' ? 'selected' : ''); ?>>Last year</option>
                </select>
            </form>
            
            <!-- Export Button -->
            <a href="<?php echo e(route('admin.analytics.export', 'courses')); ?>?period=<?php echo e($period); ?>" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                Export CSV
            </a>
        </div>
    </div>

    <!-- Key Metrics -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Total Courses</dt>
                        <dd class="text-lg font-medium text-gray-900"><?php echo e(number_format($courseStats['total_courses'])); ?></dd>
                    </dl>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">New Courses</dt>
                        <dd class="text-lg font-medium text-gray-900"><?php echo e(number_format($courseStats['new_courses'])); ?></dd>
                    </dl>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Published</dt>
                        <dd class="text-lg font-medium text-gray-900"><?php echo e(number_format($courseStats['published_courses'])); ?></dd>
                    </dl>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Total Enrollments</dt>
                        <dd class="text-lg font-medium text-gray-900"><?php echo e(number_format($courseStats['enrollment_stats']['total_enrollments'])); ?></dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Enrollment Growth Chart -->
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Enrollment Trend</h3>
            <div class="h-64">
                <canvas id="enrollmentGrowthChart"></canvas>
            </div>
        </div>

        <!-- Courses by Category Chart -->
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Courses by Category</h3>
            <div class="h-64">
                <canvas id="courseCategoryChart"></canvas>
            </div>
        </div>
    </div>

    <!-- Additional Charts -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Courses by Level Chart -->
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Courses by Difficulty Level</h3>
            <div class="h-64">
                <canvas id="courseLevelChart"></canvas>
            </div>
        </div>

        <!-- Popular Courses -->
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Most Popular Courses</h3>
            <div class="space-y-3">
                <?php $__currentLoopData = $courseStats['popular_courses']->take(10); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $course): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="flex items-center justify-between">
                    <div class="flex-1 min-w-0">
                        <p class="text-sm font-medium text-gray-900 truncate"><?php echo e($course->title); ?></p>
                    </div>
                    <div class="ml-2 flex-shrink-0">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            <?php echo e(number_format($course->enrollments_count)); ?> enrollments
                        </span>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
    </div>

    <!-- Detailed Statistics -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Category Breakdown -->
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Category Distribution</h3>
            <div class="space-y-3">
                <?php $__currentLoopData = $courseStats['courses_by_category']->take(10); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category => $count): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-gray-900"><?php echo e($category ?: 'Uncategorized'); ?></span>
                    <span class="text-sm text-gray-500"><?php echo e(number_format($count)); ?> courses</span>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>

        <!-- Enrollment Statistics -->
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Enrollment Statistics</h3>
            <div class="space-y-4">
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-gray-900">Total Enrollments</span>
                    <span class="text-sm text-gray-500"><?php echo e(number_format($courseStats['enrollment_stats']['total_enrollments'])); ?></span>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-gray-900">New Enrollments</span>
                    <span class="text-sm text-gray-500"><?php echo e(number_format($courseStats['enrollment_stats']['new_enrollments'])); ?></span>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-gray-900">Completed Enrollments</span>
                    <span class="text-sm text-gray-500"><?php echo e(number_format($courseStats['enrollment_stats']['completed_enrollments'])); ?></span>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-gray-900">Completion Rate</span>
                    <span class="text-sm text-gray-500">
                        <?php
                            $completionRate = $courseStats['enrollment_stats']['total_enrollments'] > 0 
                                ? ($courseStats['enrollment_stats']['completed_enrollments'] / $courseStats['enrollment_stats']['total_enrollments']) * 100 
                                : 0;
                        ?>
                        <?php echo e(number_format($completionRate, 1)); ?>%
                    </span>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-gray-900">Average per Course</span>
                    <span class="text-sm text-gray-500">
                        <?php echo e($courseStats['total_courses'] > 0 ? number_format($courseStats['enrollment_stats']['total_enrollments'] / $courseStats['total_courses'], 1) : 0); ?>

                    </span>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Enrollment Growth Chart
const enrollmentGrowthCtx = document.getElementById('enrollmentGrowthChart').getContext('2d');
const enrollmentGrowthChart = new Chart(enrollmentGrowthCtx, {
    type: 'line',
    data: {
        labels: <?php echo json_encode($courseStats['enrollment_stats']['enrollment_growth']->pluck('date')); ?>,
        datasets: [{
            label: 'New Enrollments',
            data: <?php echo json_encode($courseStats['enrollment_stats']['enrollment_growth']->pluck('count')); ?>,
            borderColor: 'rgb(59, 130, 246)',
            backgroundColor: 'rgba(59, 130, 246, 0.1)',
            tension: 0.1
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// Course Category Chart
const courseCategoryCtx = document.getElementById('courseCategoryChart').getContext('2d');
const courseCategoryChart = new Chart(courseCategoryCtx, {
    type: 'doughnut',
    data: {
        labels: <?php echo json_encode($courseStats['courses_by_category']->keys()); ?>,
        datasets: [{
            data: <?php echo json_encode($courseStats['courses_by_category']->values()); ?>,
            backgroundColor: [
                'rgb(59, 130, 246)',
                'rgb(16, 185, 129)',
                'rgb(139, 92, 246)',
                'rgb(245, 158, 11)',
                'rgb(239, 68, 68)',
                'rgb(107, 114, 128)',
                'rgb(236, 72, 153)',
                'rgb(34, 197, 94)'
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false
    }
});

// Course Level Chart
const courseLevelCtx = document.getElementById('courseLevelChart').getContext('2d');
const courseLevelChart = new Chart(courseLevelCtx, {
    type: 'bar',
    data: {
        labels: <?php echo json_encode($courseStats['courses_by_level']->keys()); ?>,
        datasets: [{
            label: 'Courses',
            data: <?php echo json_encode($courseStats['courses_by_level']->values()); ?>,
            backgroundColor: 'rgba(139, 92, 246, 0.8)'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\laravel\rectra\resources\views/admin/analytics/courses.blade.php ENDPATH**/ ?>