<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class JobSeekerProfile extends Model implements HasMedia
{
    use HasFactory, InteractsWithMedia;

    protected $fillable = [
        'user_id',
        'summary',
        'phone_number',
        'location',
        'website',
        'linkedin_url',
        'github_url',
        'skills',
        'experience_level',
        'desired_salary_min',
        'desired_salary_max',
        'open_to_remote',
        'profile_completed',
    ];

    protected $casts = [
        'skills' => 'array',
        'desired_salary_min' => 'decimal:2',
        'desired_salary_max' => 'decimal:2',
        'open_to_remote' => 'boolean',
        'profile_completed' => 'boolean',
    ];

    /**
     * Get the user that owns the profile.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the resumes for the profile.
     */
    public function resumes()
    {
        return $this->hasMany(Resume::class);
    }

    /**
     * Get the primary resume.
     */
    public function primaryResume()
    {
        return $this->hasOne(Resume::class)->where('is_primary', true);
    }

    /**
     * Get the job applications for the profile.
     */
    public function jobApplications()
    {
        return $this->hasMany(JobApplication::class);
    }

    /**
     * Get the company reviews written by this profile.
     */
    public function companyReviews()
    {
        return $this->hasMany(CompanyReview::class);
    }

    /**
     * Check if profile is complete.
     */
    public function isComplete(): bool
    {
        return !empty($this->summary) && 
               !empty($this->phone_number) && 
               !empty($this->location) && 
               !empty($this->skills) && 
               !empty($this->experience_level);
    }

    /**
     * Mark profile as completed.
     */
    public function markAsCompleted(): void
    {
        $this->update(['profile_completed' => $this->isComplete()]);
    }
}