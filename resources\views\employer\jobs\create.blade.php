@extends('layouts.app')

@section('title', 'Create New Job')

@section('content')
<div class="mx-auto max-w-4xl px-4 sm:px-6 lg:px-8 py-8">
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold leading-7 text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight">Create New Job</h1>
                <p class="mt-1 text-sm text-gray-500">Fill out the form below to post a new job opportunity.</p>
            </div>
            <a href="{{ route('employer.jobs.index') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                <svg class="-ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M10.5 19.5L3 12m0 0l7.5-7.5M3 12h18" />
                </svg>
                Back to Jobs
            </a>
        </div>
    </div>

    <!-- Job Creation Form -->
    <div class="bg-white shadow-sm ring-1 ring-gray-900/5 sm:rounded-xl">
        <form action="{{ route('employer.jobs.store') }}" method="POST" enctype="multipart/form-data">
            @csrf
            
            <div class="px-4 py-6 sm:p-8">
                <div class="grid max-w-2xl grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
                    
                    <!-- Job Title -->
                    <div class="sm:col-span-6">
                        <label for="title" class="block text-sm font-medium leading-6 text-gray-900">Job Title *</label>
                        <div class="mt-2">
                            <input type="text" name="title" id="title" value="{{ old('title') }}" required
                                class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6"
                                placeholder="e.g. Senior Software Engineer">
                        </div>
                        @error('title')
                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Job Type -->
                    <div class="sm:col-span-3">
                        <label for="job_type" class="block text-sm font-medium leading-6 text-gray-900">Job Type *</label>
                        <div class="mt-2">
                            <select name="job_type" id="job_type" required
                                class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6">
                                <option value="">Select job type</option>
                                <option value="full-time" {{ old('job_type') == 'full-time' ? 'selected' : '' }}>Full-time</option>
                                <option value="part-time" {{ old('job_type') == 'part-time' ? 'selected' : '' }}>Part-time</option>
                                <option value="contract" {{ old('job_type') == 'contract' ? 'selected' : '' }}>Contract</option>
                                <option value="freelance" {{ old('job_type') == 'freelance' ? 'selected' : '' }}>Freelance</option>
                                <option value="internship" {{ old('job_type') == 'internship' ? 'selected' : '' }}>Internship</option>
                            </select>
                        </div>
                        @error('job_type')
                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Work Mode -->
                    <div class="sm:col-span-3">
                        <label for="work_mode" class="block text-sm font-medium leading-6 text-gray-900">Work Mode *</label>
                        <div class="mt-2">
                            <select name="work_mode" id="work_mode" required
                                class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6">
                                <option value="">Select work mode</option>
                                <option value="remote" {{ old('work_mode') == 'remote' ? 'selected' : '' }}>Remote</option>
                                <option value="onsite" {{ old('work_mode') == 'onsite' ? 'selected' : '' }}>On-site</option>
                                <option value="hybrid" {{ old('work_mode') == 'hybrid' ? 'selected' : '' }}>Hybrid</option>
                            </select>
                        </div>
                        @error('work_mode')
                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Location -->
                    <div class="sm:col-span-6">
                        <label for="location" class="block text-sm font-medium leading-6 text-gray-900">Location *</label>
                        <div class="mt-2">
                            <input type="text" name="location" id="location" value="{{ old('location') }}" required
                                class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6"
                                placeholder="e.g. New York, NY or Remote">
                        </div>
                        @error('location')
                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Salary Range -->
                    <div class="sm:col-span-3">
                        <label for="salary_min" class="block text-sm font-medium leading-6 text-gray-900">Minimum Salary</label>
                        <div class="mt-2">
                            <input type="number" name="salary_min" id="salary_min" value="{{ old('salary_min') }}"
                                class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6"
                                placeholder="50000">
                        </div>
                        @error('salary_min')
                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div class="sm:col-span-3">
                        <label for="salary_max" class="block text-sm font-medium leading-6 text-gray-900">Maximum Salary</label>
                        <div class="mt-2">
                            <input type="number" name="salary_max" id="salary_max" value="{{ old('salary_max') }}"
                                class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6"
                                placeholder="80000">
                        </div>
                        @error('salary_max')
                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Experience Level -->
                    <div class="sm:col-span-3">
                        <label for="experience_level" class="block text-sm font-medium leading-6 text-gray-900">Experience Level *</label>
                        <div class="mt-2">
                            <select name="experience_level" id="experience_level" required
                                class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6">
                                <option value="">Select experience level</option>
                                <option value="entry" {{ old('experience_level') == 'entry' ? 'selected' : '' }}>Entry Level</option>
                                <option value="mid" {{ old('experience_level') == 'mid' ? 'selected' : '' }}>Mid Level</option>
                                <option value="senior" {{ old('experience_level') == 'senior' ? 'selected' : '' }}>Senior Level</option>
                                <option value="lead" {{ old('experience_level') == 'lead' ? 'selected' : '' }}>Lead/Principal</option>
                                <option value="executive" {{ old('experience_level') == 'executive' ? 'selected' : '' }}>Executive</option>
                            </select>
                        </div>
                        @error('experience_level')
                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Expires At -->
                    <div class="sm:col-span-3">
                        <label for="expires_at" class="block text-sm font-medium leading-6 text-gray-900">Application Deadline</label>
                        <div class="mt-2">
                            <input type="date" name="expires_at" id="expires_at" value="{{ old('expires_at') }}"
                                class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6"
                                min="{{ date('Y-m-d') }}">
                        </div>
                        @error('expires_at')
                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Job Description -->
                    <div class="sm:col-span-6">
                        <label for="description" class="block text-sm font-medium leading-6 text-gray-900">Job Description *</label>
                        <div class="mt-2">
                            <textarea name="description" id="description" rows="6" required
                                class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6"
                                placeholder="Describe the role, responsibilities, and what you're looking for in a candidate...">{{ old('description') }}</textarea>
                        </div>
                        @error('description')
                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Requirements -->
                    <div class="sm:col-span-6">
                        <label for="requirements" class="block text-sm font-medium leading-6 text-gray-900">Requirements</label>
                        <div class="mt-2">
                            <textarea name="requirements" id="requirements" rows="4"
                                class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6"
                                placeholder="List the required skills, qualifications, and experience...">{{ old('requirements') }}</textarea>
                        </div>
                        @error('requirements')
                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Benefits -->
                    <div class="sm:col-span-6">
                        <label for="benefits" class="block text-sm font-medium leading-6 text-gray-900">Benefits</label>
                        <div class="mt-2">
                            <textarea name="benefits" id="benefits" rows="3"
                                class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6"
                                placeholder="Health insurance, retirement plans, flexible hours, etc...">{{ old('benefits') }}</textarea>
                        </div>
                        @error('benefits')
                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Featured Job -->
                    <div class="sm:col-span-6">
                        <div class="flex items-center">
                            <input type="checkbox" name="is_featured" id="is_featured" value="1" {{ old('is_featured') ? 'checked' : '' }}
                                class="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-600">
                            <label for="is_featured" class="ml-3 text-sm font-medium text-gray-900">
                                Feature this job (additional cost may apply)
                            </label>
                        </div>
                        <p class="mt-1 text-sm text-gray-500">Featured jobs appear at the top of search results and get more visibility.</p>
                        @error('is_featured')
                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex items-center justify-end gap-x-6 border-t border-gray-900/10 px-4 py-4 sm:px-8">
                <a href="{{ route('employer.jobs.index') }}" class="text-sm font-semibold leading-6 text-gray-900">Cancel</a>
                <button type="submit" class="rounded-md bg-blue-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600">
                    Create Job
                </button>
            </div>
        </form>
    </div>

    <!-- Help Section -->
    <div class="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
        <div class="flex">
            <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                </svg>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-blue-800">Tips for creating effective job posts</h3>
                <div class="mt-2 text-sm text-blue-700">
                    <ul class="list-disc list-inside space-y-1">
                        <li>Use clear, descriptive job titles that candidates will search for</li>
                        <li>Include specific requirements and qualifications</li>
                        <li>Highlight unique benefits and company culture</li>
                        <li>Set realistic salary ranges to attract qualified candidates</li>
                        <li>Proofread your posting for spelling and grammar errors</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    // Auto-resize textareas
    document.querySelectorAll('textarea').forEach(textarea => {
        textarea.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = this.scrollHeight + 'px';
        });
    });

    // Salary validation
    const salaryMin = document.getElementById('salary_min');
    const salaryMax = document.getElementById('salary_max');
    
    function validateSalary() {
        const min = parseInt(salaryMin.value) || 0;
        const max = parseInt(salaryMax.value) || 0;
        
        if (min > 0 && max > 0 && min >= max) {
            salaryMax.setCustomValidity('Maximum salary must be greater than minimum salary');
        } else {
            salaryMax.setCustomValidity('');
        }
    }
    
    salaryMin.addEventListener('input', validateSalary);
    salaryMax.addEventListener('input', validateSalary);
</script>
@endpush