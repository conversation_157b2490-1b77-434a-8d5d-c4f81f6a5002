<?php $__env->startSection('title', $course->title); ?>
<?php $__env->startSection('description', $course->description); ?>

<?php $__env->startSection('content'); ?>
<div class="bg-white">
    <!-- Course Header -->
    <div class="bg-gradient-to-r from-purple-600 to-indigo-700">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Course Info -->
                <div class="lg:col-span-2">
                    <div class="flex items-center space-x-2 mb-4">
                        <span class="bg-purple-100 text-purple-800 text-sm font-medium px-3 py-1 rounded-full"><?php echo e(ucfirst($course->level ?? 'Beginner')); ?></span>
                        <span class="bg-white bg-opacity-20 text-white text-sm font-medium px-3 py-1 rounded-full"><?php echo e($course->category ?? 'General'); ?></span>
                        <?php if($course->is_featured): ?>
                            <span class="bg-yellow-400 text-yellow-900 text-sm font-medium px-3 py-1 rounded-full">Featured</span>
                        <?php endif; ?>
                    </div>

                    <h1 class="text-3xl md:text-4xl font-bold text-white mb-4"><?php echo e($course->title); ?></h1>
                    <p class="text-xl text-purple-100 mb-6"><?php echo e($course->description); ?></p>

                    <!-- Course Stats -->
                    <div class="flex flex-wrap items-center gap-6 text-white">
                        <div class="flex items-center">
                            <div class="flex text-yellow-400 mr-2">
                                <?php for($i = 1; $i <= 5; $i++): ?>
                                    <svg class="w-5 h-5 <?php echo e($i <= ($course->rating ?? 4.5) ? 'fill-current' : 'text-gray-300'); ?>" viewBox="0 0 20 20">
                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                    </svg>
                                <?php endfor; ?>
                            </div>
                            <span class="font-medium"><?php echo e(number_format($course->rating ?? 4.5, 1)); ?> (<?php echo e($course->reviews_count ?? 0); ?> reviews)</span>
                        </div>

                        <div class="flex items-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"></path>
                            </svg>
                            <span class="font-medium"><?php echo e($course->students_count ?? 0); ?> students</span>
                        </div>

                        <div class="flex items-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <span class="font-medium"><?php echo e($course->duration ?? 0); ?> hours</span>
                        </div>

                        <div class="flex items-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                            </svg>
                            <span class="font-medium"><?php echo e($course->lessons_count ?? 0); ?> lessons</span>
                        </div>
                    </div>
                </div>

                <!-- Course Preview/Enrollment Card -->
                <div class="lg:col-span-1">
                    <div class="bg-white rounded-2xl shadow-2xl p-6 sticky top-4">
                        <!-- Course Thumbnail/Video -->
                        <div class="h-48 bg-gray-200 rounded-xl mb-6 relative overflow-hidden">
                            <?php if($course->thumbnail): ?>
                                <img src="<?php echo e($course->thumbnail); ?>" alt="<?php echo e($course->title); ?>" class="w-full h-full object-cover">
                            <?php else: ?>
                                <div class="w-full h-full bg-gradient-to-br from-purple-500 to-indigo-600 flex items-center justify-center">
                                    <svg class="w-16 h-16 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                            <?php endif; ?>

                            <!-- Play Button for Preview -->
                            <?php if($course->preview_video): ?>
                                <button class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30 hover:bg-opacity-40 transition-all duration-200" onclick="openPreviewModal()">
                                    <div class="w-16 h-16 bg-white rounded-full flex items-center justify-center">
                                        <svg class="w-8 h-8 text-purple-600 ml-1" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M8 5v10l8-5-8-5z"/>
                                        </svg>
                                    </div>
                                </button>
                            <?php endif; ?>
                        </div>

                        <!-- Price -->
                        <div class="text-center mb-6">
                            <?php if($course->price > 0): ?>
                                <div class="text-3xl font-bold text-gray-900">$<?php echo e($course->price); ?></div>
                                <?php if($course->original_price && $course->original_price > $course->price): ?>
                                    <div class="text-lg text-gray-500 line-through">$<?php echo e($course->original_price); ?></div>
                                    <div class="text-sm text-green-600 font-medium">Save $<?php echo e($course->original_price - $course->price); ?></div>
                                <?php endif; ?>
                            <?php else: ?>
                                <div class="text-3xl font-bold text-green-600">Free</div>
                            <?php endif; ?>
                        </div>

                        <!-- Enrollment Actions -->
                        <?php if(auth()->guard()->check()): ?>
                            <?php if(auth()->user()->courseEnrollments()->where('course_id', $course->id)->exists()): ?>
                                <div class="space-y-3">
                                    <a href="<?php echo e(route('courses.learn', $course)); ?>" class="w-full bg-green-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors duration-200 text-center block">
                                        Continue Learning
                                    </a>
                                    <div class="text-sm text-gray-600 text-center">
                                        <?php
                                            $enrollment = auth()->user()->courseEnrollments()->where('course_id', $course->id)->first();
                                            $progress = $enrollment ? $enrollment->progress_percentage : 0;
                                        ?>
                                        Progress: <?php echo e($progress); ?>% complete
                                    </div>
                                </div>
                            <?php else: ?>
                                <form action="<?php echo e(route('courses.enroll', $course)); ?>" method="POST" class="space-y-3">
                                    <?php echo csrf_field(); ?>
                                    <button type="submit" class="w-full bg-purple-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-purple-700 transition-colors duration-200">
                                        <?php if($course->price > 0): ?>
                                            Enroll Now
                                        <?php else: ?>
                                            Enroll for Free
                                        <?php endif; ?>
                                    </button>
                                </form>
                            <?php endif; ?>
                        <?php else: ?>
                            <div class="space-y-3">
                                <a href="<?php echo e(route('login')); ?>" class="w-full bg-purple-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-purple-700 transition-colors duration-200 text-center block">
                                    Login to Enroll
                                </a>
                                <a href="<?php echo e(route('register')); ?>" class="w-full border-2 border-purple-600 text-purple-600 px-6 py-3 rounded-lg font-semibold hover:bg-purple-50 transition-colors duration-200 text-center block">
                                    Create Account
                                </a>
                            </div>
                        <?php endif; ?>

                        <!-- Course Features -->
                        <div class="mt-6 space-y-3">
                            <div class="flex items-center text-sm text-gray-600">
                                <svg class="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                Lifetime access
                            </div>

                            <?php if($course->has_certificate): ?>
                                <div class="flex items-center text-sm text-gray-600">
                                    <svg class="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    Certificate of completion
                                </div>
                            <?php endif; ?>

                            <div class="flex items-center text-sm text-gray-600">
                                <svg class="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                                </svg>
                                Mobile and desktop access
                            </div>

                            <div class="flex items-center text-sm text-gray-600">
                                <svg class="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>
                                </svg>
                                Shareable certificate
                            </div>
                        </div>

                        <!-- 30-day money back guarantee -->
                        <?php if($course->price > 0): ?>
                            <div class="mt-6 p-4 bg-green-50 rounded-lg">
                                <div class="flex items-center text-sm text-green-800">
                                    <svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                                    </svg>
                                    30-day money-back guarantee
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Course Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Content -->
            <div class="lg:col-span-2 space-y-8">
                <!-- Course Description -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                    <h2 class="text-2xl font-bold text-gray-900 mb-4">About This Course</h2>
                    <div class="prose max-w-none text-gray-600">
                        <?php echo nl2br(e($course->description)); ?>

                    </div>
                </div>

                <!-- What You'll Learn -->
                <?php if($course->learning_objectives): ?>
                    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                        <h2 class="text-2xl font-bold text-gray-900 mb-4">What You'll Learn</h2>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <?php $__currentLoopData = json_decode($course->learning_objectives, true) ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $objective): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="flex items-start">
                                    <svg class="w-5 h-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    <span class="text-gray-700"><?php echo e($objective); ?></span>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Course Curriculum -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">Course Curriculum</h2>

                    <div class="space-y-4">
                        <?php $__empty_1 = true; $__currentLoopData = $course->lessons ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $lesson): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <div class="border border-gray-200 rounded-lg overflow-hidden">
                                <div class="flex items-center justify-between p-4 bg-gray-50 hover:bg-gray-100 transition-colors duration-200 cursor-pointer" onclick="toggleLesson(<?php echo e($index); ?>)">
                                    <div class="flex items-center">
                                        <span class="bg-purple-100 text-purple-800 text-sm font-medium px-2 py-1 rounded mr-3"><?php echo e($index + 1); ?></span>
                                        <div>
                                            <h3 class="font-semibold text-gray-900"><?php echo e($lesson->title); ?></h3>
                                            <div class="flex items-center text-sm text-gray-600 mt-1">
                                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                </svg>
                                                <?php echo e($lesson->duration ?? 0); ?> min

                                                <?php if($lesson->is_preview): ?>
                                                    <span class="ml-2 bg-green-100 text-green-800 text-xs font-medium px-2 py-1 rounded">Preview</span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="flex items-center">
                                        <?php if($lesson->is_preview): ?>
                                            <button class="text-purple-600 hover:text-purple-700 font-medium text-sm mr-3">
                                                Preview
                                            </button>
                                        <?php endif; ?>

                                        <svg class="w-5 h-5 text-gray-400 transform transition-transform duration-200 lesson-chevron-<?php echo e($index); ?>" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                        </svg>
                                    </div>
                                </div>

                                <div class="lesson-content-<?php echo e($index); ?> hidden p-4 border-t border-gray-200">
                                    <p class="text-gray-600 mb-4"><?php echo e($lesson->description); ?></p>

                                    <?php if($lesson->resources && count($lesson->resources) > 0): ?>
                                        <div class="space-y-2">
                                            <h4 class="font-medium text-gray-900">Resources:</h4>
                                            <?php $__currentLoopData = $lesson->resources; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $resource): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <div class="flex items-center text-sm text-gray-600">
                                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                                    </svg>
                                                    <?php echo e(is_array($resource) ? ($resource['name'] ?? $resource['title'] ?? 'Resource') : $resource); ?>

                                                </div>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <div class="text-center py-8 text-gray-500">
                                <svg class="w-12 h-12 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                </svg>
                                <p>Course curriculum will be available soon.</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Requirements -->
                <?php if($course->prerequisites && count($course->prerequisites) > 0): ?>
                    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                        <h2 class="text-2xl font-bold text-gray-900 mb-4">Requirements</h2>
                        <ul class="space-y-2">
                            <?php $__currentLoopData = $course->prerequisites ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $requirement): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <li class="flex items-start">
                                    <svg class="w-5 h-5 text-gray-400 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                    </svg>
                                    <span class="text-gray-700"><?php echo e(is_array($requirement) ? ($requirement['name'] ?? $requirement['title'] ?? 'Requirement') : $requirement); ?></span>
                                </li>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </ul>
                    </div>
                <?php endif; ?>

                <!-- Instructor -->
                <?php if($course->instructor): ?>
                    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                        <h2 class="text-2xl font-bold text-gray-900 mb-6">Your Instructor</h2>

                        <div class="flex items-start space-x-4">
                            <div class="w-16 h-16 bg-gray-300 rounded-full flex-shrink-0 overflow-hidden">
                                <?php if($course->instructor->avatar): ?>
                                    <img src="<?php echo e($course->instructor->avatar); ?>" alt="<?php echo e($course->instructor->name); ?>" class="w-full h-full object-cover">
                                <?php else: ?>
                                    <div class="w-full h-full bg-purple-500 flex items-center justify-center">
                                        <span class="text-white font-semibold text-lg"><?php echo e(substr($course->instructor->name, 0, 1)); ?></span>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <div class="flex-1">
                                <h3 class="text-xl font-semibold text-gray-900 mb-2"><?php echo e($course->instructor->name); ?></h3>
                                <p class="text-gray-600 mb-4"><?php echo e($course->instructor->bio ?? 'Experienced instructor and industry professional.'); ?></p>

                                <div class="flex flex-wrap gap-4 text-sm text-gray-600">
                                    <div class="flex items-center">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"></path>
                                        </svg>
                                        <?php echo e($course->instructor->students_count ?? 0); ?> students
                                    </div>

                                    <div class="flex items-center">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                        </svg>
                                        <?php echo e($course->instructor->courses_count ?? 1); ?> courses
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Sidebar -->
            <div class="lg:col-span-1">
                <!-- Related Courses -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Related Courses</h3>

                    <div class="space-y-4">
                        <?php for($i = 0; $i < 3; $i++): ?>
                            <div class="flex space-x-3">
                                <div class="w-16 h-12 bg-gradient-to-br from-purple-500 to-indigo-600 rounded flex-shrink-0"></div>
                                <div class="flex-1 min-w-0">
                                    <h4 class="text-sm font-medium text-gray-900 truncate">Advanced Web Development</h4>
                                    <p class="text-xs text-gray-600">$49.99</p>
                                    <div class="flex items-center mt-1">
                                        <div class="flex text-yellow-400">
                                            <?php for($j = 0; $j < 5; $j++): ?>
                                                <svg class="w-3 h-3 fill-current" viewBox="0 0 20 20">
                                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                                </svg>
                                            <?php endfor; ?>
                                        </div>
                                        <span class="text-xs text-gray-600 ml-1">(4.8)</span>
                                    </div>
                                </div>
                            </div>
                        <?php endfor; ?>
                    </div>

                    <a href="<?php echo e(route('courses.index')); ?>" class="mt-4 text-purple-600 hover:text-purple-700 text-sm font-medium block">
                        View all courses →
                    </a>
                </div>

                <!-- Course Category -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Category</h3>
                    <div class="flex flex-wrap gap-2">
                        <span class="bg-purple-100 text-purple-700 text-sm px-3 py-1 rounded-full">
                            <?php echo e(ucfirst($course->category)); ?>

                        </span>
                        <span class="bg-blue-100 text-blue-700 text-sm px-3 py-1 rounded-full">
                            <?php echo e(ucfirst($course->difficulty_level)); ?>

                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Preview Video Modal -->
<?php if($course->preview_video): ?>
    <div id="previewModal" class="fixed inset-0 bg-black bg-opacity-75 hidden z-50 flex items-center justify-center p-4">
        <div class="bg-white rounded-xl max-w-4xl w-full max-h-full overflow-hidden">
            <div class="flex justify-between items-center p-4 border-b">
                <h3 class="text-lg font-semibold">Course Preview</h3>
                <button onclick="closePreviewModal()" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            <div class="p-4">
                <video controls class="w-full h-auto">
                    <source src="<?php echo e($course->preview_video); ?>" type="video/mp4">
                    Your browser does not support the video tag.
                </video>
            </div>
        </div>
    </div>
<?php endif; ?>

<script>
    function toggleLesson(index) {
        const content = document.querySelector(`.lesson-content-${index}`);
        const chevron = document.querySelector(`.lesson-chevron-${index}`);

        if (content.classList.contains('hidden')) {
            content.classList.remove('hidden');
            chevron.classList.add('rotate-180');
        } else {
            content.classList.add('hidden');
            chevron.classList.remove('rotate-180');
        }
    }

    function openPreviewModal() {
        document.getElementById('previewModal').classList.remove('hidden');
        document.body.style.overflow = 'hidden';
    }

    function closePreviewModal() {
        document.getElementById('previewModal').classList.add('hidden');
        document.body.style.overflow = 'auto';

        // Pause video when modal is closed
        const video = document.querySelector('#previewModal video');
        if (video) {
            video.pause();
        }
    }

    // Close modal when clicking outside
    document.getElementById('previewModal')?.addEventListener('click', function(e) {
        if (e.target === this) {
            closePreviewModal();
        }
    });

    // Close modal with Escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closePreviewModal();
        }
    });
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\laravel\rectra\resources\views/courses/show.blade.php ENDPATH**/ ?>