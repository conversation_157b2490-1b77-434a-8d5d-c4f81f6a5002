@extends('layouts.app')

@section('title', 'Dashboard')
@section('description', 'Your personal dashboard - manage your job applications, courses, and career progress.')

@section('content')
<div class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <div class="bg-white shadow-sm">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Welcome back, {{ auth()->user()->name }}!</h1>
                    <p class="text-gray-600 mt-1">Here's what's happening with your career journey</p>
                </div>
                
                <div class="flex items-center space-x-4">
                    @if(auth()->user()->isJobSeeker())
                        <a href="{{ route('jobs.index') }}" class="bg-purple-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-purple-700 transition-colors duration-200">
                            Browse Jobs
                        </a>
                    @elseif(auth()->user()->isEmployer())
                        <a href="{{ route('jobs.create') }}" class="bg-purple-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-purple-700 transition-colors duration-200">
                            Post a Job
                        </a>
                    @endif
                    
                    <a href="{{ route('courses.index') }}" class="border border-purple-600 text-purple-600 px-4 py-2 rounded-lg font-medium hover:bg-purple-50 transition-colors duration-200">
                        Browse Courses
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Quick Stats -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            @if(auth()->user()->isJobSeeker())
                <!-- Job Applications -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Applications</p>
                            <p class="text-2xl font-bold text-gray-900">{{ auth()->user()->jobSeekerProfile->jobApplications()->count() ?? 0 }}</p>
                        </div>
                    </div>
                </div>
                
                <!-- Saved Jobs -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Saved Jobs</p>
                            <p class="text-2xl font-bold text-gray-900">{{ auth()->user()->savedJobs()->count() ?? 0 }}</p>
                        </div>
                    </div>
                </div>
            @elseif(auth()->user()->isEmployer())
                <!-- Posted Jobs -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V8a2 2 0 012-2V6"></path>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Posted Jobs</p>
                            <p class="text-2xl font-bold text-gray-900">{{ auth()->user()->employerProfile->jobs()->count() ?? 0 }}</p>
                        </div>
                    </div>
                </div>
                
                <!-- Total Applications -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"></path>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Applications Received</p>
                            <p class="text-2xl font-bold text-gray-900">{{ auth()->user()->employerProfile->jobs()->withCount('jobApplications')->get()->sum('job_applications_count') ?? 0 }}</p>
                        </div>
                    </div>
                </div>
            @endif
            
            <!-- Course Enrollments -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Enrolled Courses</p>
                        <p class="text-2xl font-bold text-gray-900">{{ auth()->user()->courseEnrollments()->count() ?? 0 }}</p>
                    </div>
                </div>
            </div>
            
            <!-- Profile Completion -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Profile Complete</p>
                        <p class="text-2xl font-bold text-gray-900">{{ rand(65, 95) }}%</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Content -->
            <div class="lg:col-span-2 space-y-8">
                @if(auth()->user()->isJobSeeker())
                    <!-- Recent Applications -->
                    <div class="bg-white rounded-xl shadow-sm border border-gray-200">
                        <div class="p-6 border-b border-gray-200">
                            <div class="flex items-center justify-between">
                                <h2 class="text-lg font-semibold text-gray-900">Recent Applications</h2>
                                <a href="{{ route('applications.index') }}" class="text-purple-600 hover:text-purple-700 text-sm font-medium">
                                    View all
                                </a>
                            </div>
                        </div>
                        
                        <div class="divide-y divide-gray-200">
                            @forelse(auth()->user()->jobSeekerProfile->jobApplications()->with('job')->latest()->take(5)->get() ?? [] as $application)
                                <div class="p-6 hover:bg-gray-50 transition-colors duration-200">
                                    <div class="flex items-center justify-between">
                                        <div class="flex-1">
                                            <h3 class="font-medium text-gray-900">
                                                <a href="{{ route('jobs.show', $application->job) }}" class="hover:text-purple-600">
                                                    {{ $application->job->title }}
                                                </a>
                                            </h3>
                                            <p class="text-sm text-gray-600 mt-1">{{ $application->job->company_name }}</p>
                                            <p class="text-xs text-gray-500 mt-1">Applied {{ $application->created_at->diffForHumans() }}</p>
                                        </div>
                                        
                                        <div class="flex items-center space-x-4">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                                @if($application->status === 'pending') bg-yellow-100 text-yellow-800
                                                @elseif($application->status === 'reviewed') bg-blue-100 text-blue-800
                                                @elseif($application->status === 'shortlisted') bg-green-100 text-green-800
                                                @elseif($application->status === 'rejected') bg-red-100 text-red-800
                                                @else bg-gray-100 text-gray-800 @endif">
                                                {{ ucfirst($application->status) }}
                                            </span>
                                            
                                            <a href="{{ route('applications.show', $application) }}" class="text-purple-600 hover:text-purple-700">
                                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                                </svg>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            @empty
                                <div class="p-6 text-center">
                                    <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                    <h3 class="text-lg font-medium text-gray-900 mb-2">No applications yet</h3>
                                    <p class="text-gray-600 mb-4">Start applying to jobs that match your skills and interests.</p>
                                    <a href="{{ route('jobs.index') }}" class="text-purple-600 hover:text-purple-700 font-medium">
                                        Browse Jobs
                                    </a>
                                </div>
                            @endforelse
                        </div>
                    </div>
                @elseif(auth()->user()->isEmployer())
                    <!-- Recent Job Posts -->
                    <div class="bg-white rounded-xl shadow-sm border border-gray-200">
                        <div class="p-6 border-b border-gray-200">
                            <div class="flex items-center justify-between">
                                <h2 class="text-lg font-semibold text-gray-900">Your Job Posts</h2>
                                <a href="{{ route('employer.jobs.index') }}" class="text-purple-600 hover:text-purple-700 text-sm font-medium">
                                    Manage all
                                </a>
                            </div>
                        </div>
                        
                        <div class="divide-y divide-gray-200">
                            @forelse(auth()->user()->employerProfile->jobs()->withCount('jobApplications')->latest()->take(5)->get() ?? [] as $job)
                                <div class="p-6 hover:bg-gray-50 transition-colors duration-200">
                                    <div class="flex items-center justify-between">
                                        <div class="flex-1">
                                            <h3 class="font-medium text-gray-900">
                                                <a href="{{ route('jobs.show', $job) }}" class="hover:text-purple-600">
                                                    {{ $job->title }}
                                                </a>
                                            </h3>
                                            <p class="text-sm text-gray-600 mt-1">{{ $job->location }} • {{ ucfirst($job->type) }}</p>
                                            <p class="text-xs text-gray-500 mt-1">Posted {{ $job->created_at->diffForHumans() }}</p>
                                        </div>
                                        
                                        <div class="flex items-center space-x-4">
                                            <div class="text-center">
                                                <p class="text-lg font-semibold text-gray-900">{{ $job->job_applications_count }}</p>
                                                <p class="text-xs text-gray-600">Applications</p>
                                            </div>
                                            
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                                @if($job->status === 'active') bg-green-100 text-green-800
                                                @elseif($job->status === 'paused') bg-yellow-100 text-yellow-800
                                                @elseif($job->status === 'closed') bg-red-100 text-red-800
                                                @else bg-gray-100 text-gray-800 @endif">
                                                {{ ucfirst($job->status) }}
                                            </span>
                                            
                                            <a href="{{ route('employer.jobs.show', $job) }}" class="text-purple-600 hover:text-purple-700">
                                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                                </svg>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            @empty
                                <div class="p-6 text-center">
                                    <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V8a2 2 0 012-2V6"></path>
                                    </svg>
                                    <h3 class="text-lg font-medium text-gray-900 mb-2">No job posts yet</h3>
                                    <p class="text-gray-600 mb-4">Start posting jobs to find the perfect candidates.</p>
                                    <a href="{{ route('jobs.create') }}" class="text-purple-600 hover:text-purple-700 font-medium">
                                        Post Your First Job
                                    </a>
                                </div>
                            @endforelse
                        </div>
                    </div>
                @endif
                
                <!-- Course Progress -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200">
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex items-center justify-between">
                            <h2 class="text-lg font-semibold text-gray-900">Course Progress</h2>
                            <a href="{{ route('courses.my-learning') }}" class="text-purple-600 hover:text-purple-700 text-sm font-medium">
                                View all
                            </a>
                        </div>
                    </div>
                    
                    <div class="divide-y divide-gray-200">
                        @forelse(auth()->user()->courseEnrollments()->with('course')->latest()->take(3)->get() ?? [] as $enrollment)
                            <div class="p-6">
                                <div class="flex items-center justify-between mb-3">
                                    <div class="flex-1">
                                        <h3 class="font-medium text-gray-900">
                                            <a href="{{ route('courses.show', $enrollment->course) }}" class="hover:text-purple-600">
                                                {{ $enrollment->course->title }}
                                            </a>
                                        </h3>
                                        <p class="text-sm text-gray-600 mt-1">{{ $enrollment->course->instructor ?? 'Rectra Team' }}</p>
                                    </div>
                                    
                                    <div class="text-right">
                                        <p class="text-sm font-medium text-gray-900">{{ $enrollment->progress_percentage }}%</p>
                                        <p class="text-xs text-gray-500">{{ $enrollment->status === 'completed' ? 'Completed' : 'In Progress' }}</p>
                                    </div>
                                </div>
                                
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-purple-600 h-2 rounded-full transition-all duration-300" style="width: {{ $enrollment->progress_percentage }}%"></div>
                                </div>
                                
                                <div class="flex items-center justify-between mt-3">
                                    <span class="text-xs text-gray-500">Last accessed {{ $enrollment->updated_at->diffForHumans() }}</span>
                                    
                                    @if($enrollment->status !== 'completed')
                                        <a href="{{ route('courses.learn', $enrollment->course) }}" class="text-purple-600 hover:text-purple-700 text-sm font-medium">
                                            Continue
                                        </a>
                                    @else
                                        <span class="text-green-600 text-sm font-medium flex items-center">
                                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                            Completed
                                        </span>
                                    @endif
                                </div>
                            </div>
                        @empty
                            <div class="p-6 text-center">
                                <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                </svg>
                                <h3 class="text-lg font-medium text-gray-900 mb-2">No courses enrolled</h3>
                                <p class="text-gray-600 mb-4">Start learning with our professional courses to advance your career.</p>
                                <a href="{{ route('courses.index') }}" class="text-purple-600 hover:text-purple-700 font-medium">
                                    Browse Courses
                                </a>
                            </div>
                        @endforelse
                    </div>
                </div>
            </div>
            
            <!-- Sidebar -->
            <div class="lg:col-span-1 space-y-6">
                <!-- Quick Actions -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
                    
                    <div class="space-y-3">
                        @if(auth()->user()->isJobSeeker())
                            <a href="{{ route('profile.edit') }}" class="flex items-center p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                                <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                    <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <p class="font-medium text-gray-900">Update Profile</p>
                                    <p class="text-sm text-gray-600">Keep your profile current</p>
                                </div>
                            </a>
                            
                            <a href="{{ route('resume.builder') }}" class="flex items-center p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                                <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                                    <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <p class="font-medium text-gray-900">Build Resume</p>
                                    <p class="text-sm text-gray-600">Create a professional resume</p>
                                </div>
                            </a>
                            
                            <a href="{{ route('saved-jobs.index') }}" class="flex items-center p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                                <div class="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center mr-3">
                                    <svg class="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <p class="font-medium text-gray-900">Saved Jobs</p>
                                    <p class="text-sm text-gray-600">View your saved positions</p>
                                </div>
                            </a>
                        @elseif(auth()->user()->isEmployer())
                            <a href="{{ route('jobs.create') }}" class="flex items-center p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                                <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                    <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                    </svg>
                                </div>
                                <div>
                                    <p class="font-medium text-gray-900">Post New Job</p>
                                    <p class="text-sm text-gray-600">Find the perfect candidate</p>
                                </div>
                            </a>
                            
                            <a href="{{ route('employer.applications.index') }}" class="flex items-center p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                                <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                                    <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <p class="font-medium text-gray-900">Review Applications</p>
                                    <p class="text-sm text-gray-600">Manage candidate applications</p>
                                </div>
                            </a>
                            
                            <a href="{{ route('employer.profile.edit') }}" class="flex items-center p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                                <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                                    <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                    </svg>
                                </div>
                                <div>
                                    <p class="font-medium text-gray-900">Company Profile</p>
                                    <p class="text-sm text-gray-600">Update company information</p>
                                </div>
                            </a>
                        @endif
                    </div>
                </div>
                
                <!-- Recommended Courses -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Recommended for You</h3>
                    
                    <div class="space-y-4">
                        @for($i = 0; $i < 3; $i++)
                            <div class="flex space-x-3">
                                <div class="w-16 h-12 bg-gradient-to-br from-purple-500 to-indigo-600 rounded flex-shrink-0"></div>
                                <div class="flex-1 min-w-0">
                                    <h4 class="text-sm font-medium text-gray-900 truncate">{{ ['Advanced JavaScript', 'Data Analysis with Python', 'Digital Marketing Fundamentals'][$i] }}</h4>
                                    <p class="text-xs text-gray-600">{{ ['$79.99', 'Free', '$49.99'][$i] }}</p>
                                    <div class="flex items-center mt-1">
                                        <div class="flex text-yellow-400">
                                            @for($j = 0; $j < 5; $j++)
                                                <svg class="w-3 h-3 fill-current" viewBox="0 0 20 20">
                                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                                </svg>
                                            @endfor
                                        </div>
                                        <span class="text-xs text-gray-600 ml-1">(4.{{ rand(5, 9) }})</span>
                                    </div>
                                </div>
                            </div>
                        @endfor
                    </div>
                    
                    <a href="{{ route('courses.index') }}" class="mt-4 text-purple-600 hover:text-purple-700 text-sm font-medium block">
                        Explore all courses →
                    </a>
                </div>
                
                <!-- Recent Activity -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h3>
                    
                    <div class="space-y-4">
                        <div class="flex items-start space-x-3">
                            <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0">
                                <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <div class="flex-1 min-w-0">
                                <p class="text-sm text-gray-900">Profile updated successfully</p>
                                <p class="text-xs text-gray-500">2 hours ago</p>
                            </div>
                        </div>
                        
                        <div class="flex items-start space-x-3">
                            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                                <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                </svg>
                            </div>
                            <div class="flex-1 min-w-0">
                                <p class="text-sm text-gray-900">Enrolled in new course</p>
                                <p class="text-xs text-gray-500">1 day ago</p>
                            </div>
                        </div>
                        
                        <div class="flex items-start space-x-3">
                            <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0">
                                <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                            </div>
                            <div class="flex-1 min-w-0">
                                <p class="text-sm text-gray-900">Applied to Software Engineer position</p>
                                <p class="text-xs text-gray-500">3 days ago</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection