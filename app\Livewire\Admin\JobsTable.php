<?php

namespace App\Livewire\Admin;

use App\Models\Job;
use Livewire\Component;
use Livewire\WithPagination;

class JobsTable extends Component
{
    use WithPagination;

    public $search = '';
    public $status = '';
    public $jobType = '';
    public $location = '';
    public $perPage = 10;
    public $sortField = 'created_at';
    public $sortDirection = 'desc';
    public $selectedJobs = [];
    public $selectAll = false;
    public $showFilters = true;

    protected $queryString = [
        'search' => ['except' => ''],
        'status' => ['except' => ''],
        'jobType' => ['except' => ''],
        'location' => ['except' => ''],
        'page' => ['except' => 1],
    ];

    protected $listeners = ['refreshComponent' => '$refresh'];

    public function mount()
    {
        $this->search = request('search', '');
        $this->status = request('status', '');
        $this->jobType = request('jobType', '');
        $this->location = request('location', '');
    }

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function updatingStatus()
    {
        $this->resetPage();
    }

    public function updatingJobType()
    {
        $this->resetPage();
    }

    public function updatingLocation()
    {
        $this->resetPage();
    }

    public function updatingPerPage()
    {
        $this->resetPage();
    }

    public function toggleFilters()
    {
        $this->showFilters = !$this->showFilters;
    }

    public function sortBy($field)
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortDirection = 'asc';
        }
        $this->sortField = $field;
    }

    public function updatedSelectAll($value)
    {
        if ($value) {
            $this->selectedJobs = $this->jobs->pluck('id')->toArray();
        } else {
            $this->selectedJobs = [];
        }
    }

    public function getJobsProperty()
    {
        return Job::query()
            ->with(['employerProfile.user'])
            ->when($this->search, function ($query) {
                $query->where(function ($q) {
                    $q->where('title', 'like', '%' . $this->search . '%')
                      ->orWhere('description', 'like', '%' . $this->search . '%')
                      ->orWhere('location', 'like', '%' . $this->search . '%')
                      ->orWhereHas('employerProfile.user', function ($userQuery) {
                          $userQuery->where('name', 'like', '%' . $this->search . '%');
                      });
                });
            })
            ->when($this->status, function ($query) {
                $query->where('status', $this->status);
            })
            ->when($this->jobType, function ($query) {
                $query->where('job_type', $this->jobType);
            })
            ->when($this->location, function ($query) {
                $query->where('location', 'like', '%' . $this->location . '%');
            })
            ->orderBy($this->sortField, $this->sortDirection)
            ->paginate($this->perPage);
    }

    public function getStatusCountsProperty()
    {
        return [
            'all' => Job::count(),
            'active' => Job::where('status', 'active')->count(),
            'draft' => Job::where('status', 'draft')->count(),
            'closed' => Job::where('status', 'closed')->count(),
        ];
    }

    public function getJobTypesProperty()
    {
        return Job::distinct()->pluck('job_type')->filter()->toArray();
    }

    public function getStatusesProperty()
    {
        return ['draft', 'active', 'paused', 'closed', 'archived'];
    }

    public function clearFilters()
    {
        $this->search = '';
        $this->status = '';
        $this->jobType = '';
        $this->location = '';
        $this->resetPage();
    }

    public function bulkDelete()
    {
        if (!empty($this->selectedJobs)) {
            Job::whereIn('id', $this->selectedJobs)->delete();
            $this->selectedJobs = [];
            $this->selectAll = false;
            session()->flash('success', 'Selected jobs deleted successfully.');
        }
    }

    public function approveJob($jobId)
    {
        $job = Job::find($jobId);
        if ($job) {
            $job->status = 'active';
            $job->save();
            session()->flash('success', 'Job approved successfully.');
        }
    }

    public function render()
    {
        return view('livewire.admin.jobs-table', [
            'jobs' => $this->jobs,
            'statusCounts' => $this->statusCounts,
            'jobTypes' => $this->jobTypes,
            'statuses' => $this->statuses,
        ]);
    }
}
