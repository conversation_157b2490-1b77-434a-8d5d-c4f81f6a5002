<?php

namespace Database\Seeders;

use App\Models\Resume;
use App\Models\JobSeekerProfile;
use Illuminate\Database\Seeder;

class ResumeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $jobSeekerProfiles = JobSeekerProfile::all();
        
        if ($jobSeekerProfiles->isEmpty()) {
            $this->command->warn('No job seeker profiles found. Please run UserSeeder first.');
            return;
        }

        foreach ($jobSeekerProfiles as $profile) {
            // Check if profile already has resumes
            if ($profile->resumes()->count() > 0) {
                continue;
            }

            // Create 1-3 resumes per job seeker
            $resumeCount = fake()->numberBetween(1, 3);
            
            for ($i = 0; $i < $resumeCount; $i++) {
                $isDefault = $i === 0; // First resume is default
                
                Resume::create([
                    'job_seeker_profile_id' => $profile->id,
                    'file_name' => $this->generateResumeFileName($profile, $i),
                    'file_path' => $this->generateResumeFilePath($profile, $i),
                    'file_type' => 'pdf',
                    'file_size' => fake()->numberBetween(50000, 500000),
                    'is_primary' => $isDefault,
                    'privacy_setting' => fake()->randomElement(['public', 'private', 'anonymous']),
                    'parsed_data' => json_encode($this->generateParsedData($profile)),
                    'created_at' => fake()->dateTimeBetween('-6 months', 'now'),
                ]);
            }
        }
    }

    private function generateResumeFileName($profile, $index)
    {
        $baseName = str_replace(' ', '_', strtolower($profile->user->name));
        $suffix = $index > 0 ? "_" . ($index + 1) : "";
        return $baseName . "_resume" . $suffix . ".pdf";
    }

    private function generateResumeFilePath($profile, $index)
    {
        $fileName = $this->generateResumeFileName($profile, $index);
        return "resumes/" . $profile->id . "/" . $fileName;
    }

    private function generateParsedData($profile)
    {
        $skills = is_array($profile->skills) ? $profile->skills : ['Communication', 'Problem Solving', 'Teamwork'];
        $experienceLevel = $profile->experience_level ?? 'mid';
        
        // Generate work experience based on experience level
        $experienceYears = match($experienceLevel) {
            'entry' => fake()->numberBetween(0, 2),
            'mid' => fake()->numberBetween(2, 5),
            'senior' => fake()->numberBetween(5, 10),
            'executive' => fake()->numberBetween(10, 20),
            default => fake()->numberBetween(2, 5)
        };

        $experiences = [];
        $currentYear = date('Y');
        
        for ($i = 0; $i < min($experienceYears, 4); $i++) {
            $startYear = $currentYear - $experienceYears + $i;
            $endYear = $i === 0 ? 'Present' : $startYear + fake()->numberBetween(1, 3);
            
            $experiences[] = [
                'title' => $this->generateJobTitle($skills),
                'company' => fake()->company(),
                'location' => fake()->city() . ', ' . fake()->stateAbbr(),
                'start_date' => $startYear,
                'end_date' => $endYear,
                'description' => $this->generateJobDescription($skills),
                'achievements' => $this->generateAchievements()
            ];
        }

        // Generate education
        $education = [
            [
                'degree' => fake()->randomElement([
                    'Bachelor of Science in Computer Science',
                    'Bachelor of Arts in Business Administration',
                    'Bachelor of Science in Engineering',
                    'Master of Business Administration',
                    'Bachelor of Arts in Marketing',
                    'Bachelor of Science in Information Technology'
                ]),
                'school' => fake()->randomElement([
                    'State University',
                    'Tech Institute',
                    'Community College',
                    'Metropolitan University',
                    'National University',
                    'City College'
                ]),
                'location' => fake()->city() . ', ' . fake()->stateAbbr(),
                'graduation_year' => fake()->numberBetween(2010, 2023),
                'gpa' => fake()->optional(0.7)->randomFloat(2, 3.0, 4.0)
            ]
        ];

        // Generate certifications
        $certifications = [];
        if (fake()->boolean(60)) {
            $certifications = fake()->randomElements([
                'AWS Certified Solutions Architect',
                'Google Cloud Professional',
                'Microsoft Azure Fundamentals',
                'Project Management Professional (PMP)',
                'Certified Scrum Master',
                'Google Analytics Certified',
                'HubSpot Content Marketing Certified',
                'Salesforce Administrator',
                'CompTIA Security+',
                'Oracle Certified Professional'
            ], fake()->numberBetween(1, 3));
        }

        // Generate projects
        $projects = [];
        if (fake()->boolean(70)) {
            for ($i = 0; $i < fake()->numberBetween(1, 3); $i++) {
                $projects[] = [
                    'name' => $this->generateProjectName(),
                    'description' => $this->generateProjectDescription($skills),
                    'technologies' => fake()->randomElements($skills, min(count($skills), fake()->numberBetween(2, 5))),
                    'url' => fake()->optional(0.6)->url()
                ];
            }
        }

        return [
            'personal_info' => [
                'name' => $profile->user->name,
                'email' => $profile->user->email,
                'phone' => $profile->phone_number,
                'location' => $profile->location,
                'website' => $profile->website,
                'linkedin' => $profile->linkedin_url,
                'github' => $profile->github_url
            ],
            'professional_summary' => $profile->summary ?: $this->generateProfessionalSummary($experienceLevel, $skills),
            'skills' => [
                'technical' => array_filter($skills, fn($skill) => in_array($skill, [
                    'PHP', 'Laravel', 'JavaScript', 'React', 'Vue.js', 'Node.js', 'Python',
                    'Java', 'C#', 'Ruby', 'Go', 'Swift', 'Kotlin', 'TypeScript', 'Angular',
                    'Docker', 'Kubernetes', 'AWS', 'Azure', 'MySQL', 'PostgreSQL', 'MongoDB'
                ])),
                'soft' => array_filter($skills, fn($skill) => in_array($skill, [
                    'Communication', 'Leadership', 'Problem Solving', 'Teamwork',
                    'Project Management', 'Time Management', 'Critical Thinking'
                ])),
                'other' => array_diff($skills, array_merge(
                    array_filter($skills, fn($skill) => in_array($skill, [
                        'PHP', 'Laravel', 'JavaScript', 'React', 'Vue.js', 'Node.js', 'Python',
                        'Java', 'C#', 'Ruby', 'Go', 'Swift', 'Kotlin', 'TypeScript', 'Angular',
                        'Docker', 'Kubernetes', 'AWS', 'Azure', 'MySQL', 'PostgreSQL', 'MongoDB'
                    ])),
                    array_filter($skills, fn($skill) => in_array($skill, [
                        'Communication', 'Leadership', 'Problem Solving', 'Teamwork',
                        'Project Management', 'Time Management', 'Critical Thinking'
                    ]))
                ))
            ],
            'experience' => $experiences,
            'education' => $education,
            'certifications' => $certifications,
            'projects' => $projects,
            'languages' => fake()->optional(0.4)->randomElements([
                'English (Native)', 'Spanish (Conversational)', 'French (Basic)',
                'German (Intermediate)', 'Mandarin (Basic)', 'Japanese (Basic)'
            ], fake()->numberBetween(1, 3))
        ];
    }

    private function generateJobTitle($skills)
    {
        $techSkills = array_intersect($skills, [
            'PHP', 'Laravel', 'JavaScript', 'React', 'Vue.js', 'Node.js', 'Python', 'Java'
        ]);
        
        if (!empty($techSkills)) {
            return fake()->randomElement([
                'Software Developer', 'Frontend Developer', 'Backend Developer',
                'Full Stack Developer', 'Web Developer', 'Software Engineer'
            ]);
        }
        
        return fake()->randomElement([
            'Marketing Specialist', 'Business Analyst', 'Project Coordinator',
            'Sales Representative', 'Customer Success Manager', 'Operations Specialist'
        ]);
    }

    private function generateJobDescription($skills)
    {
        $descriptions = [
            "Developed and maintained web applications using modern technologies and best practices.",
            "Collaborated with cross-functional teams to deliver high-quality software solutions.",
            "Implemented new features and optimized existing systems for better performance.",
            "Managed client relationships and ensured project deliverables met requirements.",
            "Led team initiatives and contributed to strategic planning and execution.",
            "Analyzed business requirements and translated them into technical specifications."
        ];
        
        return fake()->randomElements($descriptions, fake()->numberBetween(2, 4));
    }

    private function generateAchievements()
    {
        $achievements = [
            "Increased system performance by 40% through code optimization",
            "Successfully delivered 15+ projects on time and within budget",
            "Reduced customer response time by 50% through process improvements",
            "Led a team of 5 developers on a critical product launch",
            "Implemented automated testing that reduced bugs by 60%",
            "Achieved 95% customer satisfaction rating",
            "Generated $100K+ in additional revenue through new feature development",
            "Mentored 3 junior developers and improved team productivity"
        ];
        
        return fake()->randomElements($achievements, fake()->numberBetween(1, 3));
    }

    private function generateProjectName()
    {
        $projectTypes = ['E-commerce Platform', 'Task Management App', 'Social Media Dashboard', 
                        'Analytics Tool', 'Mobile App', 'Web Portal', 'API Service', 'CRM System'];
        return fake()->randomElement($projectTypes);
    }

    private function generateProjectDescription($skills)
    {
        return "Developed a comprehensive solution that demonstrates proficiency in " . 
               implode(', ', array_slice($skills, 0, 3)) . 
               ". The project showcased modern development practices and delivered measurable business value.";
    }

    private function generateProfessionalSummary($experienceLevel, $skills)
    {
        $summaries = [
            'entry' => "Motivated and detail-oriented professional with a passion for " . implode(', ', array_slice($skills, 0, 3)) . ". Eager to contribute to a dynamic team and grow technical expertise.",
            'mid' => "Experienced professional with " . fake()->numberBetween(3, 5) . "+ years in " . implode(', ', array_slice($skills, 0, 3)) . ". Proven track record of delivering quality solutions and collaborating effectively with teams.",
            'senior' => "Senior professional with " . fake()->numberBetween(6, 10) . "+ years of expertise in " . implode(', ', array_slice($skills, 0, 3)) . ". Strong leadership skills and experience mentoring teams to achieve business objectives.",
            'executive' => "Executive-level professional with " . fake()->numberBetween(10, 15) . "+ years of strategic leadership in " . implode(', ', array_slice($skills, 0, 3)) . ". Proven ability to drive organizational growth and innovation."
        ];
        
        return $summaries[$experienceLevel] ?? $summaries['mid'];
    }
}