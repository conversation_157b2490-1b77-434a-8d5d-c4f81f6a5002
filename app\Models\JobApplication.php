<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;

class JobApplication extends Model
{
    use HasFactory;

    protected $fillable = [
        'job_id',
        'job_seeker_profile_id',
        'resume_id',
        'cover_letter',
        'status',
        'employer_notes',
        'viewed_at',
        'status_updated_at',
    ];

    protected $casts = [
        'viewed_at' => 'datetime',
        'status_updated_at' => 'datetime',
    ];

    /**
     * Get the job that this application is for.
     */
    public function job()
    {
        return $this->belongsTo(Job::class);
    }

    /**
     * Get the job seeker profile that made this application.
     */
    public function jobSeekerProfile()
    {
        return $this->belongsTo(JobSeekerProfile::class);
    }

    /**
     * Get the resume used for this application.
     */
    public function resume()
    {
        return $this->belongsTo(Resume::class);
    }

    /**
     * Get the user that made this application through the job seeker profile.
     */
    public function user()
    {
        return $this->hasOneThrough(User::class, JobSeekerProfile::class, 'id', 'id', 'job_seeker_profile_id', 'user_id');
    }

    /**
     * Scope a query to only include applications with a specific status.
     */
    public function scopeStatus(Builder $query, string $status): Builder
    {
        return $query->where('status', $status);
    }

    /**
     * Scope a query to only include viewed applications.
     */
    public function scopeViewed(Builder $query): Builder
    {
        return $query->whereNotNull('viewed_at');
    }

    /**
     * Scope a query to only include unviewed applications.
     */
    public function scopeUnviewed(Builder $query): Builder
    {
        return $query->whereNull('viewed_at');
    }

    /**
     * Mark the application as viewed.
     */
    public function markAsViewed(): void
    {
        if (!$this->viewed_at) {
            $this->update(['viewed_at' => now()]);
        }
    }

    /**
     * Update the application status.
     */
    public function updateStatus(string $status, ?string $notes = null): void
    {
        $this->update([
            'status' => $status,
            'employer_notes' => $notes ?? $this->employer_notes,
            'status_updated_at' => now(),
        ]);
    }

    /**
     * Check if the application is in a final state.
     */
    public function isFinal(): bool
    {
        return in_array($this->status, ['rejected', 'hired']);
    }

    /**
     * Get the status badge color.
     */
    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            'submitted' => 'gray',
            'viewed' => 'blue',
            'in_review' => 'yellow',
            'shortlisted' => 'purple',
            'interviewed' => 'indigo',
            'rejected' => 'red',
            'hired' => 'green',
            default => 'gray'
        };
    }

    /**
     * Get the human-readable status.
     */
    public function getStatusLabelAttribute(): string
    {
        return match($this->status) {
            'submitted' => 'Submitted',
            'viewed' => 'Viewed by Employer',
            'in_review' => 'In Review',
            'shortlisted' => 'Shortlisted',
            'interviewed' => 'Interviewed',
            'rejected' => 'Rejected',
            'hired' => 'Hired',
            default => ucfirst($this->status)
        };
    }
}