<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class Course extends Model implements HasMedia
{
    use HasFactory, InteractsWithMedia;

    protected $fillable = [
        'title',
        'slug',
        'description',
        'short_description',
        'thumbnail_path',
        'category',
        'is_premium',
        'price',
        'difficulty_level',
        'estimated_duration',
        'learning_outcomes',
        'prerequisites',
        'is_published',
        'enrollments_count',
        'rating',
        'reviews_count',
    ];

    protected $casts = [
        'is_premium' => 'boolean',
        'price' => 'decimal:2',
        'learning_outcomes' => 'array',
        'prerequisites' => 'array',
        'is_published' => 'boolean',
        'rating' => 'decimal:2',
    ];

    /**
     * Get the lessons for the course.
     */
    public function lessons()
    {
        return $this->hasMany(Lesson::class)->orderBy('order');
    }

    /**
     * Get the enrollments for the course.
     */
    public function enrollments()
    {
        return $this->hasMany(CourseEnrollment::class);
    }

    /**
     * Get the users enrolled in this course.
     */
    public function enrolledUsers()
    {
        return $this->belongsToMany(User::class, 'course_enrollments')
                    ->withPivot(['progress', 'completed_at', 'certificate_issued_at'])
                    ->withTimestamps();
    }

    /**
     * Scope a query to only include published courses.
     */
    public function scopePublished(Builder $query): Builder
    {
        return $query->where('is_published', true);
    }

    /**
     * Scope a query to only include free courses.
     */
    public function scopeFree(Builder $query): Builder
    {
        return $query->where('is_premium', false);
    }

    /**
     * Scope a query to only include premium courses.
     */
    public function scopePremium(Builder $query): Builder
    {
        return $query->where('is_premium', true);
    }

    /**
     * Scope a query to filter by category.
     */
    public function scopeCategory(Builder $query, string $category): Builder
    {
        return $query->where('category', $category);
    }

    /**
     * Scope a query to filter by difficulty level.
     */
    public function scopeDifficulty(Builder $query, string $level): Builder
    {
        return $query->where('difficulty_level', $level);
    }

    /**
     * Check if the course is free.
     */
    public function isFree(): bool
    {
        return !$this->is_premium;
    }

    /**
     * Get the total duration of all lessons.
     */
    public function getTotalDurationAttribute(): int
    {
        return $this->lessons->sum('duration') ?? $this->estimated_duration ?? 0;
    }

    /**
     * Get the number of lessons.
     */
    public function getLessonsCountAttribute(): int
    {
        return $this->lessons->count();
    }

    /**
     * Check if a user is enrolled in this course.
     */
    public function isUserEnrolled(User $user): bool
    {
        return $this->enrollments()->where('user_id', $user->id)->exists();
    }

    /**
     * Enroll a user in this course.
     */
    public function enrollUser(User $user): CourseEnrollment
    {
        if ($this->isUserEnrolled($user)) {
            return $this->enrollments()->where('user_id', $user->id)->first();
        }

        $enrollment = $this->enrollments()->create([
            'user_id' => $user->id,
            'progress' => 0,
        ]);

        $this->increment('enrollments_count');

        return $enrollment;
    }

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName(): string
    {
        return 'slug';
    }

    /**
     * Register media collections.
     */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('thumbnail')
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/webp'])
            ->singleFile();
    }

    /**
     * Register media conversions.
     */
    public function registerMediaConversions(Media $media = null): void
    {
        $this->addMediaConversion('thumb')
            ->width(300)
            ->height(200)
            ->sharpen(10);
    }
}