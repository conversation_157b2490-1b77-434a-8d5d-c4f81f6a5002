<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\JobSeekerProfile;
use App\Models\EmployerProfile;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;
use Laravel\Sanctum\PersonalAccessToken;

class AuthController extends Controller
{
    /**
     * Register a new user.
     */
    public function register(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'role' => 'required|in:job_seeker,employer',
            'phone' => 'nullable|string|max:20',
            'date_of_birth' => 'nullable|date|before:today',
            'gender' => 'nullable|in:male,female,other',
            'location' => 'nullable|string|max:255',
            // Job seeker specific fields
            'skills' => 'nullable|array',
            'experience_level' => 'nullable|in:entry,junior,mid,senior,executive',
            'desired_salary_min' => 'nullable|numeric|min:0',
            'desired_salary_max' => 'nullable|numeric|min:0|gte:desired_salary_min',
            'job_preferences' => 'nullable|array',
            // Employer specific fields
            'company_name' => 'required_if:role,employer|string|max:255',
            'company_size' => 'nullable|in:1-10,11-50,51-200,201-500,501-1000,1000+',
            'industry' => 'nullable|string|max:255',
            'company_description' => 'nullable|string|max:2000',
            'website' => 'nullable|url|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Create user
            $user = User::create([
                'name' => $request->name,
                'email' => $request->email,
                'password' => Hash::make($request->password),
                'role' => $request->role,
                'phone' => $request->phone,
                'date_of_birth' => $request->date_of_birth,
                'gender' => $request->gender,
                'location' => $request->location,
            ]);

            // Create role-specific profile
            if ($request->role === 'job_seeker') {
                JobSeekerProfile::create([
                    'user_id' => $user->id,
                    'skills' => $request->skills ? json_encode($request->skills) : null,
                    'experience_level' => $request->experience_level,
                    'desired_salary_min' => $request->desired_salary_min,
                    'desired_salary_max' => $request->desired_salary_max,
                    'job_preferences' => $request->job_preferences ? json_encode($request->job_preferences) : null,
                ]);
            } elseif ($request->role === 'employer') {
                EmployerProfile::create([
                    'user_id' => $user->id,
                    'company_name' => $request->company_name,
                    'company_size' => $request->company_size,
                    'industry' => $request->industry,
                    'description' => $request->company_description,
                    'website' => $request->website,
                ]);
            }

            // Create access token
            $token = $user->createToken('auth_token')->plainTextToken;

            return response()->json([
                'success' => true,
                'message' => 'User registered successfully',
                'data' => [
                    'user' => $user->load($request->role === 'job_seeker' ? 'jobSeekerProfile' : 'employerProfile'),
                    'access_token' => $token,
                    'token_type' => 'Bearer',
                ]
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Registration failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Login user.
     */
    public function login(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'password' => 'required|string',
            'device_name' => 'nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = User::where('email', $request->email)->first();

        if (!$user || !Hash::check($request->password, $user->password)) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid credentials'
            ], 401);
        }

        // Check if user is suspended
        if ($user->suspended_at && (!$user->suspended_until || $user->suspended_until > now())) {
            return response()->json([
                'success' => false,
                'message' => 'Account is suspended',
                'suspended_until' => $user->suspended_until,
                'suspension_reason' => $user->suspension_reason
            ], 403);
        }

        // Update last login
        $user->update([
            'last_login_at' => now(),
            'login_count' => $user->login_count + 1,
        ]);

        // Create access token
        $deviceName = $request->device_name ?? 'API Token';
        $token = $user->createToken($deviceName)->plainTextToken;

        return response()->json([
            'success' => true,
            'message' => 'Login successful',
            'data' => [
                'user' => $user->load($user->role === 'job_seeker' ? 'jobSeekerProfile' : 'employerProfile'),
                'access_token' => $token,
                'token_type' => 'Bearer',
            ]
        ]);
    }

    /**
     * Get authenticated user.
     */
    public function me(Request $request)
    {
        $user = $request->user();
        $user->load($user->role === 'job_seeker' ? 'jobSeekerProfile' : 'employerProfile');

        return response()->json([
            'success' => true,
            'data' => [
                'user' => $user
            ]
        ]);
    }

    /**
     * Update user profile.
     */
    public function updateProfile(Request $request)
    {
        $user = $request->user();

        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|string|max:255',
            'phone' => 'sometimes|nullable|string|max:20',
            'date_of_birth' => 'sometimes|nullable|date|before:today',
            'gender' => 'sometimes|nullable|in:male,female,other',
            'location' => 'sometimes|nullable|string|max:255',
            'bio' => 'sometimes|nullable|string|max:1000',
            // Job seeker specific fields
            'skills' => 'sometimes|nullable|array',
            'experience_level' => 'sometimes|nullable|in:entry,junior,mid,senior,executive',
            'desired_salary_min' => 'sometimes|nullable|numeric|min:0',
            'desired_salary_max' => 'sometimes|nullable|numeric|min:0|gte:desired_salary_min',
            'job_preferences' => 'sometimes|nullable|array',
            // Employer specific fields
            'company_name' => 'sometimes|string|max:255',
            'company_size' => 'sometimes|nullable|in:1-10,11-50,51-200,201-500,501-1000,1000+',
            'industry' => 'sometimes|nullable|string|max:255',
            'company_description' => 'sometimes|nullable|string|max:2000',
            'website' => 'sometimes|nullable|url|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Update user basic info
            $userFields = ['name', 'phone', 'date_of_birth', 'gender', 'location', 'bio'];
            $userData = $request->only($userFields);
            if (!empty($userData)) {
                $user->update($userData);
            }

            // Update role-specific profile
            if ($user->role === 'job_seeker' && $user->jobSeekerProfile) {
                $profileFields = ['skills', 'experience_level', 'desired_salary_min', 'desired_salary_max', 'job_preferences'];
                $profileData = $request->only($profileFields);
                
                if (isset($profileData['skills'])) {
                    $profileData['skills'] = json_encode($profileData['skills']);
                }
                if (isset($profileData['job_preferences'])) {
                    $profileData['job_preferences'] = json_encode($profileData['job_preferences']);
                }
                
                if (!empty($profileData)) {
                    $user->jobSeekerProfile->update($profileData);
                }
            } elseif ($user->role === 'employer' && $user->employerProfile) {
                $profileFields = ['company_name', 'company_size', 'industry', 'website'];
                $profileData = $request->only($profileFields);
                
                if ($request->has('company_description')) {
                    $profileData['description'] = $request->company_description;
                }
                
                if (!empty($profileData)) {
                    $user->employerProfile->update($profileData);
                }
            }

            $user->refresh();
            $user->load($user->role === 'job_seeker' ? 'jobSeekerProfile' : 'employerProfile');

            return response()->json([
                'success' => true,
                'message' => 'Profile updated successfully',
                'data' => [
                    'user' => $user
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Profile update failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Change password.
     */
    public function changePassword(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'current_password' => 'required|string',
            'new_password' => 'required|string|min:8|confirmed',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = $request->user();

        if (!Hash::check($request->current_password, $user->password)) {
            return response()->json([
                'success' => false,
                'message' => 'Current password is incorrect'
            ], 400);
        }

        $user->update([
            'password' => Hash::make($request->new_password)
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Password changed successfully'
        ]);
    }

    /**
     * Logout user (revoke current token).
     */
    public function logout(Request $request)
    {
        $request->user()->currentAccessToken()->delete();

        return response()->json([
            'success' => true,
            'message' => 'Logged out successfully'
        ]);
    }

    /**
     * Logout from all devices (revoke all tokens).
     */
    public function logoutAll(Request $request)
    {
        $request->user()->tokens()->delete();

        return response()->json([
            'success' => true,
            'message' => 'Logged out from all devices successfully'
        ]);
    }

    /**
     * Get user's active sessions/tokens.
     */
    public function sessions(Request $request)
    {
        $tokens = $request->user()->tokens()->get()->map(function ($token) {
            return [
                'id' => $token->id,
                'name' => $token->name,
                'last_used_at' => $token->last_used_at,
                'created_at' => $token->created_at,
                'is_current' => $token->id === request()->user()->currentAccessToken()->id,
            ];
        });

        return response()->json([
            'success' => true,
            'data' => [
                'sessions' => $tokens
            ]
        ]);
    }

    /**
     * Revoke a specific token/session.
     */
    public function revokeSession(Request $request, $tokenId)
    {
        $token = $request->user()->tokens()->find($tokenId);

        if (!$token) {
            return response()->json([
                'success' => false,
                'message' => 'Session not found'
            ], 404);
        }

        $token->delete();

        return response()->json([
            'success' => true,
            'message' => 'Session revoked successfully'
        ]);
    }

    /**
     * Refresh token (create new token and revoke current one).
     */
    public function refreshToken(Request $request)
    {
        $user = $request->user();
        $currentToken = $request->user()->currentAccessToken();
        $deviceName = $currentToken->name;

        // Create new token
        $newToken = $user->createToken($deviceName)->plainTextToken;

        // Revoke current token
        $currentToken->delete();

        return response()->json([
            'success' => true,
            'message' => 'Token refreshed successfully',
            'data' => [
                'access_token' => $newToken,
                'token_type' => 'Bearer',
            ]
        ]);
    }

    /**
     * Delete user account.
     */
    public function deleteAccount(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'password' => 'required|string',
            'confirmation' => 'required|in:DELETE_MY_ACCOUNT',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = $request->user();

        if (!Hash::check($request->password, $user->password)) {
            return response()->json([
                'success' => false,
                'message' => 'Password is incorrect'
            ], 400);
        }

        try {
            // Revoke all tokens
            $user->tokens()->delete();

            // Soft delete user (this will cascade to related models if configured)
            $user->delete();

            return response()->json([
                'success' => true,
                'message' => 'Account deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Account deletion failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Send email verification.
     */
    public function sendEmailVerification(Request $request)
    {
        $user = $request->user();

        if ($user->hasVerifiedEmail()) {
            return response()->json([
                'success' => false,
                'message' => 'Email already verified'
            ], 400);
        }

        $user->sendEmailVerificationNotification();

        return response()->json([
            'success' => true,
            'message' => 'Verification email sent successfully'
        ]);
    }

    /**
     * Verify email.
     */
    public function verifyEmail(Request $request)
    {
        $user = User::find($request->route('id'));

        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'User not found'
            ], 404);
        }

        if (!hash_equals((string) $request->route('hash'), sha1($user->getEmailForVerification()))) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid verification link'
            ], 400);
        }

        if ($user->hasVerifiedEmail()) {
            return response()->json([
                'success' => false,
                'message' => 'Email already verified'
            ], 400);
        }

        $user->markEmailAsVerified();

        return response()->json([
            'success' => true,
            'message' => 'Email verified successfully'
        ]);
    }
}