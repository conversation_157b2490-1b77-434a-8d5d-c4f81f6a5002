<?php

namespace App\Livewire;

use App\Models\Resume;
use App\Models\User;
use Livewire\Component;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class ResumeBuilder extends Component
{
    public $resume;
    public $resumeId;
    
    // Personal Information
    public $full_name = '';
    public $email = '';
    public $phone = '';
    public $address = '';
    public $linkedin = '';
    public $website = '';
    public $summary = '';
    
    // Experience
    public $experiences = [];
    
    // Education
    public $education = [];
    
    // Skills
    public $skills = [];
    
    // Certifications
    public $certifications = [];
    
    // Languages
    public $languages = [];
    
    // Template
    public $template = 'modern';
    public $availableTemplates = [
        'modern' => 'Modern',
        'classic' => 'Classic',
        'creative' => 'Creative',
        'minimal' => 'Minimal'
    ];
    
    public $currentStep = 1;
    public $totalSteps = 6;
    
    protected $rules = [
        'full_name' => 'required|string|max:255',
        'email' => 'required|email|max:255',
        'phone' => 'nullable|string|max:20',
        'address' => 'nullable|string|max:500',
        'linkedin' => 'nullable|url|max:255',
        'website' => 'nullable|url|max:255',
        'summary' => 'nullable|string|max:1000',
        'experiences.*.company' => 'required|string|max:255',
        'experiences.*.position' => 'required|string|max:255',
        'experiences.*.start_date' => 'required|date',
        'experiences.*.end_date' => 'nullable|date|after_or_equal:experiences.*.start_date',
        'experiences.*.description' => 'nullable|string|max:1000',
        'experiences.*.current' => 'boolean',
        'education.*.institution' => 'required|string|max:255',
        'education.*.degree' => 'required|string|max:255',
        'education.*.field' => 'nullable|string|max:255',
        'education.*.start_date' => 'required|date',
        'education.*.end_date' => 'nullable|date|after_or_equal:education.*.start_date',
        'education.*.gpa' => 'nullable|numeric|min:0|max:4',
        'skills.*' => 'required|string|max:100',
        'certifications.*.name' => 'required|string|max:255',
        'certifications.*.issuer' => 'required|string|max:255',
        'certifications.*.date' => 'required|date',
        'certifications.*.url' => 'nullable|url|max:255',
        'languages.*.name' => 'required|string|max:100',
        'languages.*.proficiency' => 'required|in:beginner,intermediate,advanced,native',
        'template' => 'required|in:modern,classic,creative,minimal'
    ];
    
    public function mount($resumeId = null)
    {
        if ($resumeId) {
            $this->resumeId = $resumeId;
            $this->resume = Resume::where('user_id', Auth::id())->findOrFail($resumeId);
            $this->loadResumeData();
        } else {
            $this->initializeFromProfile();
        }
        
        $this->initializeArrays();
    }
    
    public function initializeFromProfile()
    {
        $user = Auth::user();
        $profile = $user->jobSeekerProfile;
        
        if ($profile) {
            $this->full_name = $user->name;
            $this->email = $user->email;
            $this->phone = $profile->phone;
            $this->address = $profile->address;
            $this->linkedin = $profile->linkedin;
            $this->website = $profile->website;
            $this->summary = $profile->summary;
        }
    }
    
    public function loadResumeData()
    {
        $data = $this->resume->data;
        
        $this->full_name = $data['personal']['full_name'] ?? '';
        $this->email = $data['personal']['email'] ?? '';
        $this->phone = $data['personal']['phone'] ?? '';
        $this->address = $data['personal']['address'] ?? '';
        $this->linkedin = $data['personal']['linkedin'] ?? '';
        $this->website = $data['personal']['website'] ?? '';
        $this->summary = $data['personal']['summary'] ?? '';
        
        $this->experiences = $data['experience'] ?? [];
        $this->education = $data['education'] ?? [];
        $this->skills = $data['skills'] ?? [];
        $this->certifications = $data['certifications'] ?? [];
        $this->languages = $data['languages'] ?? [];
        $this->template = $data['template'] ?? 'modern';
    }
    
    public function initializeArrays()
    {
        if (empty($this->experiences)) {
            $this->experiences = [['company' => '', 'position' => '', 'start_date' => '', 'end_date' => '', 'description' => '', 'current' => false]];
        }
        
        if (empty($this->education)) {
            $this->education = [['institution' => '', 'degree' => '', 'field' => '', 'start_date' => '', 'end_date' => '', 'gpa' => '']];
        }
        
        if (empty($this->skills)) {
            $this->skills = [''];
        }
        
        if (empty($this->certifications)) {
            $this->certifications = [['name' => '', 'issuer' => '', 'date' => '', 'url' => '']];
        }
        
        if (empty($this->languages)) {
            $this->languages = [['name' => '', 'proficiency' => 'intermediate']];
        }
    }
    
    public function nextStep()
    {
        $this->validateCurrentStep();
        
        if ($this->currentStep < $this->totalSteps) {
            $this->currentStep++;
        }
    }
    
    public function previousStep()
    {
        if ($this->currentStep > 1) {
            $this->currentStep--;
        }
    }
    
    public function goToStep($step)
    {
        if ($step >= 1 && $step <= $this->totalSteps) {
            $this->currentStep = $step;
        }
    }
    
    public function validateCurrentStep()
    {
        switch ($this->currentStep) {
            case 1:
                $this->validate([
                    'full_name' => 'required|string|max:255',
                    'email' => 'required|email|max:255',
                ]);
                break;
            case 2:
                $this->validate([
                    'experiences.*.company' => 'required|string|max:255',
                    'experiences.*.position' => 'required|string|max:255',
                    'experiences.*.start_date' => 'required|date',
                ]);
                break;
            case 3:
                $this->validate([
                    'education.*.institution' => 'required|string|max:255',
                    'education.*.degree' => 'required|string|max:255',
                ]);
                break;
        }
    }
    
    public function addExperience()
    {
        $this->experiences[] = ['company' => '', 'position' => '', 'start_date' => '', 'end_date' => '', 'description' => '', 'current' => false];
    }
    
    public function removeExperience($index)
    {
        if (count($this->experiences) > 1) {
            unset($this->experiences[$index]);
            $this->experiences = array_values($this->experiences);
        }
    }
    
    public function addEducation()
    {
        $this->education[] = ['institution' => '', 'degree' => '', 'field' => '', 'start_date' => '', 'end_date' => '', 'gpa' => ''];
    }
    
    public function removeEducation($index)
    {
        if (count($this->education) > 1) {
            unset($this->education[$index]);
            $this->education = array_values($this->education);
        }
    }
    
    public function addSkill()
    {
        $this->skills[] = '';
    }
    
    public function removeSkill($index)
    {
        if (count($this->skills) > 1) {
            unset($this->skills[$index]);
            $this->skills = array_values($this->skills);
        }
    }
    
    public function addCertification()
    {
        $this->certifications[] = ['name' => '', 'issuer' => '', 'date' => '', 'url' => ''];
    }
    
    public function removeCertification($index)
    {
        if (count($this->certifications) > 1) {
            unset($this->certifications[$index]);
            $this->certifications = array_values($this->certifications);
        }
    }
    
    public function addLanguage()
    {
        $this->languages[] = ['name' => '', 'proficiency' => 'intermediate'];
    }
    
    public function removeLanguage($index)
    {
        if (count($this->languages) > 1) {
            unset($this->languages[$index]);
            $this->languages = array_values($this->languages);
        }
    }
    
    public function save()
    {
        $this->validate();
        
        $data = [
            'personal' => [
                'full_name' => $this->full_name,
                'email' => $this->email,
                'phone' => $this->phone,
                'address' => $this->address,
                'linkedin' => $this->linkedin,
                'website' => $this->website,
                'summary' => $this->summary,
            ],
            'experience' => array_filter($this->experiences, function($exp) {
                return !empty($exp['company']) && !empty($exp['position']);
            }),
            'education' => array_filter($this->education, function($edu) {
                return !empty($edu['institution']) && !empty($edu['degree']);
            }),
            'skills' => array_filter($this->skills, function($skill) {
                return !empty(trim($skill));
            }),
            'certifications' => array_filter($this->certifications, function($cert) {
                return !empty($cert['name']) && !empty($cert['issuer']);
            }),
            'languages' => array_filter($this->languages, function($lang) {
                return !empty($lang['name']);
            }),
            'template' => $this->template,
        ];
        
        if ($this->resume) {
            $this->resume->update([
                'title' => $this->full_name . ' - Resume',
                'data' => $data,
            ]);
            session()->flash('success', 'Resume updated successfully!');
        } else {
            $this->resume = Resume::create([
                'user_id' => Auth::id(),
                'title' => $this->full_name . ' - Resume',
                'data' => $data,
            ]);
            session()->flash('success', 'Resume created successfully!');
        }
        
        return redirect()->route('resumes.show', $this->resume);
    }
    
    public function render()
    {
        return view('livewire.resume-builder');
    }
}
