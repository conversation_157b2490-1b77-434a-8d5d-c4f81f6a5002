<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\JobSeekerProfile;
use App\Models\EmployerProfile;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create roles if they don't exist
        $adminRole = Role::firstOrCreate(['name' => 'admin']);
        $employerRole = Role::firstOrCreate(['name' => 'employer']);
        $jobSeekerRole = Role::firstOrCreate(['name' => 'job_seeker']);

        // Create admin user
        $admin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Admin User',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'admin',
                'email_verified_at' => now(),
            ]
        );
        if (!$admin->hasRole($adminRole)) {
            $admin->assignRole($adminRole);
        }

        // Create test job seeker users
        $jobSeekers = [
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'job_seeker',
                'email_verified_at' => now(),
                'job_alerts_enabled' => true,
            ],
            [
                'name' => 'Jane Smith',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'job_seeker',
                'email_verified_at' => now(),
                'job_alerts_enabled' => true,
            ],
            [
                'name' => 'Mike Johnson',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'job_seeker',
                'email_verified_at' => now(),
                'job_alerts_enabled' => false,
            ],
        ];

        foreach ($jobSeekers as $seekerData) {
            $user = User::firstOrCreate(
                ['email' => $seekerData['email']],
                $seekerData
            );
            if (!$user->hasRole($jobSeekerRole)) {
                $user->assignRole($jobSeekerRole);
            }

            // Create job seeker profile if it doesn't exist
            if (!$user->jobSeekerProfile) {
                JobSeekerProfile::create([
                'user_id' => $user->id,
                'summary' => 'Experienced professional looking for new opportunities in ' . fake()->randomElement(['technology', 'marketing', 'finance', 'healthcare']),
                'phone_number' => fake()->phoneNumber(),
                'location' => fake()->city() . ', ' . fake()->state(),
                'website' => fake()->optional()->url(),
                'linkedin_url' => 'https://linkedin.com/in/' . strtolower(str_replace(' ', '', $user->name)),
                'github_url' => fake()->optional()->url(),
                'skills' => fake()->randomElements([
                    'PHP', 'Laravel', 'JavaScript', 'React', 'Vue.js', 'Node.js', 'Python', 'Java',
                    'Project Management', 'Marketing', 'Sales', 'Customer Service', 'Data Analysis',
                    'Graphic Design', 'Content Writing', 'SEO', 'Social Media Marketing'
                ], fake()->numberBetween(3, 8)),
                'experience_level' => fake()->randomElement(['Entry-level', 'Mid-level', 'Senior', 'Manager']),
                'desired_salary_min' => fake()->numberBetween(30000, 80000),
                'desired_salary_max' => fake()->numberBetween(80000, 150000),
                'open_to_remote' => fake()->boolean(70),
                    'profile_completed' => fake()->boolean(80),
                ]);
            }
        }

        // Create test employer users
        $employers = [
            [
                'name' => 'Sarah Wilson',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'employer',
                'email_verified_at' => now(),
                'company' => 'TechCorp Solutions',
            ],
            [
                'name' => 'David Brown',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'employer',
                'email_verified_at' => now(),
                'company' => 'InnovateTech',
            ],
            [
                'name' => 'Lisa Garcia',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'employer',
                'email_verified_at' => now(),
                'company' => 'Digital Marketing Agency',
            ],
        ];

        foreach ($employers as $employerData) {
            $company = $employerData['company'];
            unset($employerData['company']);
            
            $user = User::firstOrCreate(
                ['email' => $employerData['email']],
                $employerData
            );
            if (!$user->hasRole($employerRole)) {
                $user->assignRole($employerRole);
            }

            // Create employer profile if it doesn't exist
            if (!$user->employerProfile) {
                EmployerProfile::create([
                'user_id' => $user->id,
                'company_name' => $company,
                'description' => 'We are a leading company in our industry, committed to innovation and excellence. Join our team and help us shape the future.',
                'website' => 'https://www.' . strtolower(str_replace(' ', '', $company)) . '.com',
                'industry' => fake()->randomElement([
                    'Technology', 'Healthcare', 'Finance', 'Education', 'Manufacturing',
                    'Retail', 'Marketing', 'Consulting', 'Real Estate', 'Transportation'
                ]),
                'company_size' => fake()->randomElement(['1-10', '11-50', '51-200', '201-500', '501-1000', '1000+']),
                'location' => fake()->city() . ', ' . fake()->state(),
                'phone_number' => fake()->phoneNumber(),
                'linkedin_url' => 'https://linkedin.com/company/' . strtolower(str_replace(' ', '-', $company)),
                    'is_verified' => fake()->boolean(60),
                ]);
            }
        }

        // Create additional random users
        User::factory(20)->create()->each(function ($user) use ($jobSeekerRole, $employerRole) {
            $role = fake()->randomElement(['job_seeker', 'employer']);
            $user->update(['role' => $role]);
            
            if ($role === 'job_seeker') {
                $user->assignRole($jobSeekerRole);
                JobSeekerProfile::factory()->create(['user_id' => $user->id]);
            } else {
                $user->assignRole($employerRole);
                EmployerProfile::factory()->create(['user_id' => $user->id]);
            }
        });
    }
}