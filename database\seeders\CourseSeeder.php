<?php

namespace Database\Seeders;

use App\Models\Course;
use App\Models\Lesson;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class CourseSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $courses = [
            [
                'title' => 'Complete Web Development Bootcamp',
                'slug' => 'complete-web-development-bootcamp',
                'description' => 'Learn full-stack web development from scratch. Master HTML, CSS, JavaScript, React, Node.js, and more in this comprehensive course.',
                'category' => 'Web Development',
                'is_premium' => true,
                'price' => 99.99,
                'difficulty_level' => 'beginner',
                'estimated_duration' => 40,
                'learning_outcomes' => [
                    'Build responsive websites with HTML and CSS',
                    'Create interactive web applications with JavaScript',
                    'Develop modern React applications',
                    'Build backend APIs with Node.js and Express',
                    'Work with databases and authentication',
                    'Deploy applications to production'
                ],
                'prerequisites' => [
                    'Basic computer skills',
                    'No programming experience required'
                ],
                'is_published' => true,
                'lessons' => [
                    [
                        'title' => 'Introduction to Web Development',
                        'slug' => 'introduction-to-web-development',
                        'description' => 'Overview of web development and what you\'ll learn in this course.',
                        'content_type' => 'video',
                        'content' => 'Welcome to the Complete Web Development Bootcamp! In this lesson, we\'ll cover what web development is and what you can expect to learn.',
                        'video_url' => 'https://example.com/videos/intro-web-dev.mp4',
                        'duration' => 15,
                        'order' => 1,
                        'is_preview' => true,
                        'resources' => [
                            'Course syllabus PDF',
                            'Development environment setup guide'
                        ]
                    ],
                    [
                        'title' => 'HTML Fundamentals',
                        'slug' => 'html-fundamentals',
                        'description' => 'Learn the building blocks of web pages with HTML.',
                        'content_type' => 'video',
                        'content' => 'HTML (HyperText Markup Language) is the foundation of all web pages. Learn about tags, elements, and structure.',
                        'video_url' => 'https://example.com/videos/html-fundamentals.mp4',
                        'duration' => 45,
                        'order' => 2,
                        'is_preview' => false,
                        'resources' => [
                            'HTML cheat sheet',
                            'Practice exercises'
                        ]
                    ],
                    [
                        'title' => 'CSS Styling and Layout',
                        'slug' => 'css-styling-layout',
                        'description' => 'Make your websites beautiful with CSS styling and modern layout techniques.',
                        'content_type' => 'video',
                        'content' => 'CSS (Cascading Style Sheets) controls the visual presentation of your web pages. Learn selectors, properties, and layout.',
                        'video_url' => 'https://example.com/videos/css-styling.mp4',
                        'duration' => 60,
                        'order' => 3,
                        'is_preview' => false,
                        'resources' => [
                            'CSS reference guide',
                            'Flexbox and Grid examples'
                        ]
                    ]
                ]
            ],
            [
                'title' => 'JavaScript for Beginners',
                'slug' => 'javascript-for-beginners',
                'description' => 'Master the fundamentals of JavaScript programming. Learn variables, functions, objects, and DOM manipulation.',
                'category' => 'Programming',
                'is_premium' => false,
                'price' => 0.00,
                'difficulty_level' => 'beginner',
                'estimated_duration' => 20,
                'learning_outcomes' => [
                    'Understand JavaScript syntax and concepts',
                    'Work with variables, functions, and objects',
                    'Manipulate the DOM',
                    'Handle events and user interactions',
                    'Debug JavaScript code effectively'
                ],
                'prerequisites' => [
                    'Basic HTML knowledge helpful but not required'
                ],
                'is_published' => true,
                'lessons' => [
                    [
                        'title' => 'JavaScript Introduction',
                        'slug' => 'javascript-introduction',
                        'description' => 'What is JavaScript and why is it important for web development?',
                        'content_type' => 'video',
                        'content' => 'JavaScript is the programming language of the web. Learn what makes it special and how it powers modern websites.',
                        'video_url' => 'https://example.com/videos/js-intro.mp4',
                        'duration' => 20,
                        'order' => 1,
                        'is_preview' => true,
                        'resources' => [
                            'JavaScript overview PDF'
                        ]
                    ],
                    [
                        'title' => 'Variables and Data Types',
                        'slug' => 'variables-data-types',
                        'description' => 'Learn how to store and work with different types of data in JavaScript.',
                        'content_type' => 'video',
                        'content' => 'Variables are containers for storing data. Learn about strings, numbers, booleans, and more.',
                        'video_url' => 'https://example.com/videos/js-variables.mp4',
                        'duration' => 30,
                        'order' => 2,
                        'is_preview' => true,
                        'resources' => [
                            'Variable exercises',
                            'Data types reference'
                        ]
                    ]
                ]
            ],
            [
                'title' => 'React Development Masterclass',
                'slug' => 'react-development-masterclass',
                'description' => 'Build modern, interactive user interfaces with React. Learn components, hooks, state management, and best practices.',
                'category' => 'Frontend Development',
                'is_premium' => true,
                'price' => 79.99,
                'difficulty_level' => 'intermediate',
                'estimated_duration' => 30,
                'learning_outcomes' => [
                    'Build React applications from scratch',
                    'Master React hooks and state management',
                    'Create reusable components',
                    'Handle forms and user input',
                    'Implement routing and navigation',
                    'Connect to APIs and manage data'
                ],
                'prerequisites' => [
                    'Solid JavaScript knowledge',
                    'Basic HTML and CSS skills',
                    'Understanding of ES6+ features'
                ],
                'is_published' => true,
                'lessons' => [
                    [
                        'title' => 'React Fundamentals',
                        'slug' => 'react-fundamentals',
                        'description' => 'Introduction to React and its core concepts.',
                        'content_type' => 'video',
                        'content' => 'React is a JavaScript library for building user interfaces. Learn about components, JSX, and the virtual DOM.',
                        'video_url' => 'https://example.com/videos/react-fundamentals.mp4',
                        'duration' => 40,
                        'order' => 1,
                        'is_preview' => true,
                        'resources' => [
                            'React documentation links',
                            'Setup instructions'
                        ]
                    ]
                ]
            ],
            [
                'title' => 'Data Science with Python',
                'slug' => 'data-science-python',
                'description' => 'Learn data analysis, visualization, and machine learning with Python. Perfect for beginners in data science.',
                'category' => 'Data Science',
                'is_premium' => true,
                'price' => 129.99,
                'difficulty_level' => 'intermediate',
                'estimated_duration' => 50,
                'learning_outcomes' => [
                    'Analyze data with pandas and NumPy',
                    'Create visualizations with matplotlib and seaborn',
                    'Build machine learning models',
                    'Work with real-world datasets',
                    'Present findings effectively'
                ],
                'prerequisites' => [
                    'Basic Python programming knowledge',
                    'High school level mathematics'
                ],
                'is_published' => true,
                'lessons' => [
                    [
                        'title' => 'Introduction to Data Science',
                        'slug' => 'intro-data-science',
                        'description' => 'What is data science and what tools do we use?',
                        'content_type' => 'video',
                        'content' => 'Data science combines statistics, programming, and domain expertise to extract insights from data.',
                        'video_url' => 'https://example.com/videos/data-science-intro.mp4',
                        'duration' => 25,
                        'order' => 1,
                        'is_preview' => true,
                        'resources' => [
                            'Python installation guide',
                            'Jupyter notebook setup'
                        ]
                    ]
                ]
            ],
            [
                'title' => 'Digital Marketing Fundamentals',
                'slug' => 'digital-marketing-fundamentals',
                'description' => 'Learn the essentials of digital marketing including SEO, social media, email marketing, and analytics.',
                'category' => 'Marketing',
                'is_premium' => false,
                'price' => 0.00,
                'difficulty_level' => 'beginner',
                'estimated_duration' => 15,
                'learning_outcomes' => [
                    'Understand digital marketing landscape',
                    'Create effective social media strategies',
                    'Optimize websites for search engines',
                    'Design email marketing campaigns',
                    'Measure and analyze marketing performance'
                ],
                'prerequisites' => [
                    'No prior experience required',
                    'Basic computer and internet skills'
                ],
                'is_published' => true,
                'lessons' => [
                    [
                        'title' => 'Digital Marketing Overview',
                        'slug' => 'digital-marketing-overview',
                        'description' => 'Introduction to the world of digital marketing.',
                        'content_type' => 'video',
                        'content' => 'Digital marketing encompasses all marketing efforts that use electronic devices or the internet.',
                        'video_url' => 'https://example.com/videos/digital-marketing-overview.mp4',
                        'duration' => 20,
                        'order' => 1,
                        'is_preview' => true,
                        'resources' => [
                            'Marketing strategy template',
                            'Industry statistics report'
                        ]
                    ]
                ]
            ]
        ];

        foreach ($courses as $courseData) {
            $lessons = $courseData['lessons'];
            unset($courseData['lessons']);
            
            $course = Course::firstOrCreate(
                ['slug' => $courseData['slug']],
                $courseData
            );
            
            foreach ($lessons as $lessonData) {
                $lessonData['course_id'] = $course->id;
                Lesson::firstOrCreate(
                    ['slug' => $lessonData['slug'], 'course_id' => $course->id],
                    $lessonData
                );
            }
        }
    }
}
