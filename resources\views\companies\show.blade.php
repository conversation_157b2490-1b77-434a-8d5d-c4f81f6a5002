@extends('layouts.app')

@section('title', $company->employerProfile->company_name)

@section('content')
<div class="bg-white">
    <!-- Company Header -->
    <div class="bg-gradient-to-r from-blue-600 to-blue-800 text-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="flex flex-col md:flex-row items-start md:items-center gap-6">
                <!-- Company Logo -->
                <div class="flex-shrink-0">
                    @if($company->employerProfile->getFirstMediaUrl('logo'))
                        <img src="{{ $company->employerProfile->getFirstMediaUrl('logo') }}" 
                             alt="{{ $company->employerProfile->company_name }} logo" 
                             class="w-24 h-24 object-contain bg-white rounded-lg p-2">
                    @else
                        <div class="w-24 h-24 bg-white rounded-lg flex items-center justify-center">
                            <span class="text-3xl font-bold text-gray-500">
                                {{ substr($company->employerProfile->company_name, 0, 1) }}
                            </span>
                        </div>
                    @endif
                </div>

                <!-- Company Info -->
                <div class="flex-1">
                    <h1 class="text-3xl md:text-4xl font-bold mb-2">{{ $company->employerProfile->company_name }}</h1>
                    
                    @if($company->employerProfile->industry)
                        <p class="text-xl text-blue-100 mb-4">{{ $company->employerProfile->industry }}</p>
                    @endif

                    <div class="flex flex-wrap gap-4 text-blue-100">
                        @if($company->employerProfile->location)
                            <div class="flex items-center">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                                {{ $company->employerProfile->location }}
                            </div>
                        @endif

                        @if($company->employerProfile->company_size)
                            <div class="flex items-center">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                </svg>
                                {{ $company->employerProfile->company_size }} employees
                            </div>
                        @endif

                        @if($company->employerProfile->website)
                            <div class="flex items-center">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9m0 9c-5 0-9-4-9-9s4-9 9-9"></path>
                                </svg>
                                <a href="{{ $company->employerProfile->website }}" target="_blank" class="hover:text-white transition-colors duration-200">
                                    Website
                                </a>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex flex-col gap-3">
                    <a href="{{ route('companies.jobs', $company) }}" 
                       class="px-6 py-3 bg-white text-blue-600 font-semibold rounded-lg hover:bg-gray-100 transition-colors duration-200 text-center">
                        View Jobs ({{ $stats['active_jobs'] }})
                    </a>
                    <a href="{{ route('companies.reviews', $company) }}" 
                       class="px-6 py-3 bg-blue-500 text-white font-semibold rounded-lg hover:bg-blue-400 transition-colors duration-200 text-center">
                        Read Reviews
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Company Stats -->
    <div class="bg-gray-50 border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="text-center">
                    <div class="text-3xl font-bold text-blue-600">{{ $stats['total_jobs'] }}</div>
                    <div class="text-gray-600">Total Jobs Posted</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl font-bold text-green-600">{{ $stats['active_jobs'] }}</div>
                    <div class="text-gray-600">Active Job Openings</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl font-bold text-purple-600">{{ $stats['total_applications'] }}</div>
                    <div class="text-gray-600">Total Applications</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Content -->
            <div class="lg:col-span-2">
                <!-- About Section -->
                @if($company->employerProfile->company_description)
                    <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                        <h2 class="text-2xl font-bold text-gray-900 mb-4">About {{ $company->employerProfile->company_name }}</h2>
                        <div class="prose max-w-none text-gray-600">
                            {!! nl2br(e($company->employerProfile->company_description)) !!}
                        </div>
                    </div>
                @endif

                <!-- Recent Jobs -->
                @if($company->employerProfile->jobs->count() > 0)
                    <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                        <div class="flex justify-between items-center mb-6">
                            <h2 class="text-2xl font-bold text-gray-900">Recent Job Openings</h2>
                            <a href="{{ route('companies.jobs', $company) }}" 
                               class="text-blue-600 hover:text-blue-800 font-medium transition-colors duration-200">
                                View All Jobs →
                            </a>
                        </div>

                        <div class="space-y-4">
                            @foreach($company->employerProfile->jobs->take(3) as $job)
                                <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-200">
                                    <div class="flex justify-between items-start mb-2">
                                        <h3 class="text-lg font-semibold text-gray-900">
                                            <a href="{{ route('jobs.show', $job) }}" class="hover:text-blue-600 transition-colors duration-200">
                                                {{ $job->title }}
                                            </a>
                                        </h3>
                                        <span class="px-3 py-1 bg-green-100 text-green-800 text-sm font-medium rounded-full">
                                            {{ ucfirst($job->type) }}
                                        </span>
                                    </div>
                                    
                                    <div class="flex flex-wrap gap-4 text-sm text-gray-600 mb-3">
                                        <div class="flex items-center">
                                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            </svg>
                                            {{ $job->location }}
                                        </div>
                                        
                                        @if($job->salary_min && $job->salary_max)
                                            <div class="flex items-center">
                                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                                </svg>
                                                ${{ number_format($job->salary_min) }} - ${{ number_format($job->salary_max) }}
                                            </div>
                                        @endif
                                        
                                        <div class="flex items-center">
                                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a1 1 0 011-1h6a1 1 0 011 1v4h3a1 1 0 011 1v9a1 1 0 01-1 1H5a1 1 0 01-1-1V8a1 1 0 011-1h3z"></path>
                                            </svg>
                                            {{ $job->created_at->diffForHumans() }}
                                        </div>
                                    </div>
                                    
                                    <p class="text-gray-600 text-sm line-clamp-2">
                                        {{ Str::limit(strip_tags($job->description), 150) }}
                                    </p>
                                </div>
                            @endforeach
                        </div>
                    </div>
                @endif
            </div>

            <!-- Sidebar -->
            <div class="lg:col-span-1">
                <!-- Company Details -->
                <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Company Details</h3>
                    
                    <div class="space-y-3">
                        @if($company->employerProfile->founded_year)
                            <div class="flex justify-between">
                                <span class="text-gray-600">Founded</span>
                                <span class="font-medium">{{ $company->employerProfile->founded_year }}</span>
                            </div>
                        @endif
                        
                        @if($company->employerProfile->industry)
                            <div class="flex justify-between">
                                <span class="text-gray-600">Industry</span>
                                <span class="font-medium">{{ $company->employerProfile->industry }}</span>
                            </div>
                        @endif
                        
                        @if($company->employerProfile->company_size)
                            <div class="flex justify-between">
                                <span class="text-gray-600">Company Size</span>
                                <span class="font-medium">{{ $company->employerProfile->company_size }}</span>
                            </div>
                        @endif
                        
                        @if($company->employerProfile->location)
                            <div class="flex justify-between">
                                <span class="text-gray-600">Location</span>
                                <span class="font-medium">{{ $company->employerProfile->location }}</span>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Contact Information -->
                @if($company->employerProfile->website || $company->employerProfile->phone || $company->email)
                    <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Contact Information</h3>
                        
                        <div class="space-y-3">
                            @if($company->employerProfile->website)
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 text-gray-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9m0 9c-5 0-9-4-9-9s4-9 9-9"></path>
                                    </svg>
                                    <a href="{{ $company->employerProfile->website }}" target="_blank" 
                                       class="text-blue-600 hover:text-blue-800 transition-colors duration-200">
                                        Website
                                    </a>
                                </div>
                            @endif
                            
                            @if($company->employerProfile->phone)
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 text-gray-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                    </svg>
                                    <span class="text-gray-600">{{ $company->employerProfile->phone }}</span>
                                </div>
                            @endif
                            
                            @if($company->email)
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 text-gray-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                    </svg>
                                    <span class="text-gray-600">{{ $company->email }}</span>
                                </div>
                            @endif
                        </div>
                    </div>
                @endif

                <!-- Quick Actions -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
                    
                    <div class="space-y-3">
                        <a href="{{ route('companies.jobs', $company) }}" 
                           class="block w-full px-4 py-2 bg-blue-600 text-white text-center rounded-md hover:bg-blue-700 transition-colors duration-200">
                            View All Jobs
                        </a>
                        
                        <a href="{{ route('companies.reviews', $company) }}" 
                           class="block w-full px-4 py-2 bg-gray-200 text-gray-800 text-center rounded-md hover:bg-gray-300 transition-colors duration-200">
                            Read Reviews
                        </a>
                        
                        @auth
                            @if(auth()->user()->isJobSeeker())
                                <button onclick="followCompany({{ $company->id }})" 
                                        class="block w-full px-4 py-2 bg-green-600 text-white text-center rounded-md hover:bg-green-700 transition-colors duration-200">
                                    Follow Company
                                </button>
                            @endif
                        @endauth
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style>
@endpush

@push('scripts')
<script>
function followCompany(companyId) {
    // Implementation for following a company
    alert('Follow company functionality would be implemented here');
}
</script>
@endpush