<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('job_applications', function (Blueprint $table) {
            $table->id();
            $table->foreignId('job_id')->constrained('job_listings')->onDelete('cascade');
            $table->foreignId('job_seeker_profile_id')->constrained()->onDelete('cascade');
            $table->foreignId('resume_id')->nullable()->constrained()->onDelete('set null');
            $table->text('cover_letter')->nullable();
            $table->enum('status', ['submitted', 'viewed', 'in_review', 'shortlisted', 'interviewed', 'rejected', 'hired'])->default('submitted');
            $table->text('employer_notes')->nullable();
            $table->timestamp('viewed_at')->nullable();
            $table->timestamp('status_updated_at')->nullable();
            $table->timestamps();
            
            $table->unique(['job_id', 'job_seeker_profile_id']); // Prevent duplicate applications
            $table->index(['status', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('job_applications');
    }
};