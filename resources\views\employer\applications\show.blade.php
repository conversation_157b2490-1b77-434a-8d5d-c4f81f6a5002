@extends('layouts.app')

@section('title', 'Application Details')

@section('content')
<div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">{{ $application->user->name }}</h1>
                <p class="mt-2 text-gray-600">Applied for {{ $application->job->title }} • {{ $application->created_at->diffForHumans() }}</p>
            </div>
            <div class="flex items-center space-x-3">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                    @if($application->status === 'submitted') bg-blue-100 text-blue-800
                    @elseif($application->status === 'viewed') bg-yellow-100 text-yellow-800
                    @elseif($application->status === 'in_review') bg-purple-100 text-purple-800
                    @elseif($application->status === 'shortlisted') bg-green-100 text-green-800
                    @elseif($application->status === 'interviewed') bg-indigo-100 text-indigo-800
                    @elseif($application->status === 'rejected') bg-red-100 text-red-800
                    @elseif($application->status === 'hired') bg-emerald-100 text-emerald-800
                    @else bg-gray-100 text-gray-800 @endif">
                    {{ ucfirst(str_replace('_', ' ', $application->status)) }}
                </span>
                <a href="{{ route('employer.applications.index') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Back to Applications
                </a>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Applicant Information -->
            <div class="bg-white shadow-sm rounded-lg p-6">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">Applicant Information</h2>
                
                <div class="flex items-start space-x-4 mb-6">
                    <img class="h-16 w-16 rounded-full object-cover" src="{{ $application->user->avatar ?? 'https://ui-avatars.com/api/?name=' . urlencode($application->user->name) }}" alt="{{ $application->user->name }}">
                    <div class="flex-1">
                        <h3 class="text-lg font-medium text-gray-900">{{ $application->user->name }}</h3>
                        <p class="text-gray-600">{{ $application->user->email }}</p>
                        @if($application->user->jobSeekerProfile)
                            @if($application->user->jobSeekerProfile->phone)
                                <p class="text-gray-600">{{ $application->user->jobSeekerProfile->phone }}</p>
                            @endif
                            @if($application->user->jobSeekerProfile->location)
                                <p class="text-gray-600">{{ $application->user->jobSeekerProfile->location }}</p>
                            @endif
                        @endif
                    </div>
                </div>

                @if($application->user->jobSeekerProfile)
                    @if($application->user->jobSeekerProfile->bio)
                    <div class="mb-6">
                        <h4 class="text-sm font-medium text-gray-700 mb-2">Bio</h4>
                        <p class="text-gray-900">{{ $application->user->jobSeekerProfile->bio }}</p>
                    </div>
                    @endif

                    @if($application->user->jobSeekerProfile->skills && count($application->user->jobSeekerProfile->skills) > 0)
                    <div class="mb-6">
                        <h4 class="text-sm font-medium text-gray-700 mb-2">Skills</h4>
                        <div class="flex flex-wrap gap-2">
                            @foreach($application->user->jobSeekerProfile->skills as $skill)
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                    {{ $skill }}
                                </span>
                            @endforeach
                        </div>
                    </div>
                    @endif

                    @if($application->user->jobSeekerProfile->experience_level)
                    <div class="mb-6">
                        <h4 class="text-sm font-medium text-gray-700 mb-2">Experience Level</h4>
                        <p class="text-gray-900">{{ ucfirst($application->user->jobSeekerProfile->experience_level) }}</p>
                    </div>
                    @endif
                @endif
            </div>

            <!-- Cover Letter -->
            @if($application->cover_letter)
            <div class="bg-white shadow-sm rounded-lg p-6">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">Cover Letter</h2>
                <div class="prose max-w-none text-gray-900">
                    {!! nl2br(e($application->cover_letter)) !!}
                </div>
            </div>
            @endif

            <!-- Resume -->
            @if($application->resume_path)
            <div class="bg-white shadow-sm rounded-lg p-6">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">Resume</h2>
                <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                    <div class="flex items-center space-x-3">
                        <svg class="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                        </svg>
                        <div>
                            <p class="text-sm font-medium text-gray-900">{{ basename($application->resume_path) }}</p>
                            <p class="text-sm text-gray-500">PDF Document</p>
                        </div>
                    </div>
                    <a href="{{ Storage::url($application->resume_path) }}" target="_blank" class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        Download
                    </a>
                </div>
            </div>
            @endif

            <!-- Employer Notes -->
            <div class="bg-white shadow-sm rounded-lg p-6">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-xl font-semibold text-gray-900">Internal Notes</h2>
                </div>

                @if($application->employer_notes)
                    <div class="mb-4 p-4 bg-gray-50 rounded-lg">
                        <p class="text-gray-900">{{ $application->employer_notes }}</p>
                        @if($application->notes_updated_at)
                            <p class="text-sm text-gray-500 mt-2">Last updated {{ $application->notes_updated_at->diffForHumans() }}</p>
                        @endif
                    </div>
                @endif

                <form action="{{ route('employer.applications.add-note', $application) }}" method="POST">
                    @csrf
                    <div class="mb-4">
                        <textarea name="notes" rows="3" 
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500" 
                                  placeholder="Add internal notes about this candidate...">{{ $application->employer_notes }}</textarea>
                    </div>
                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700">
                        {{ $application->employer_notes ? 'Update Notes' : 'Add Notes' }}
                    </button>
                </form>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Status Update -->
            <div class="bg-white shadow-sm rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Update Status</h3>
                <form action="{{ route('employer.applications.update-status', $application) }}" method="POST">
                    @csrf
                    @method('PUT')
                    <div class="mb-4">
                        <select name="status" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500">
                            <option value="submitted" {{ $application->status === 'submitted' ? 'selected' : '' }}>Submitted</option>
                            <option value="viewed" {{ $application->status === 'viewed' ? 'selected' : '' }}>Viewed</option>
                            <option value="in_review" {{ $application->status === 'in_review' ? 'selected' : '' }}>In Review</option>
                            <option value="shortlisted" {{ $application->status === 'shortlisted' ? 'selected' : '' }}>Shortlisted</option>
                            <option value="interviewed" {{ $application->status === 'interviewed' ? 'selected' : '' }}>Interviewed</option>
                            <option value="rejected" {{ $application->status === 'rejected' ? 'selected' : '' }}>Rejected</option>
                            <option value="hired" {{ $application->status === 'hired' ? 'selected' : '' }}>Hired</option>
                        </select>
                    </div>
                    <button type="submit" class="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700">
                        Update Status
                    </button>
                </form>
            </div>

            <!-- Application Details -->
            <div class="bg-white shadow-sm rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Application Details</h3>
                <div class="space-y-3">
                    <div>
                        <span class="text-sm text-gray-500">Applied for</span>
                        <p class="text-sm font-medium text-gray-900">{{ $application->job->title }}</p>
                    </div>
                    <div>
                        <span class="text-sm text-gray-500">Application Date</span>
                        <p class="text-sm font-medium text-gray-900">{{ $application->created_at->format('M d, Y \a\t g:i A') }}</p>
                    </div>
                    @if($application->viewed_at)
                    <div>
                        <span class="text-sm text-gray-500">First Viewed</span>
                        <p class="text-sm font-medium text-gray-900">{{ $application->viewed_at->format('M d, Y \a\t g:i A') }}</p>
                    </div>
                    @endif
                    @if($application->status_updated_at)
                    <div>
                        <span class="text-sm text-gray-500">Status Updated</span>
                        <p class="text-sm font-medium text-gray-900">{{ $application->status_updated_at->format('M d, Y \a\t g:i A') }}</p>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white shadow-sm rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
                <div class="space-y-3">
                    <a href="mailto:{{ $application->user->email }}" class="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                        </svg>
                        Send Email
                    </a>
                    
                    @if($application->user->jobSeekerProfile && $application->user->jobSeekerProfile->phone)
                    <a href="tel:{{ $application->user->jobSeekerProfile->phone }}" class="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                        </svg>
                        Call Candidate
                    </a>
                    @endif
                    
                    <a href="{{ route('employer.jobs.show', $application->job) }}" class="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6a2 2 0 01-2 2H8a2 2 0 01-2-2V8a2 2 0 012-2V6"></path>
                        </svg>
                        View Job Posting
                    </a>
                </div>
            </div>

            <!-- Candidate Profile -->
            @if($application->user->jobSeekerProfile)
            <div class="bg-white shadow-sm rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Candidate Profile</h3>
                <div class="space-y-3">
                    @if($application->user->jobSeekerProfile->linkedin_url)
                    <a href="{{ $application->user->jobSeekerProfile->linkedin_url }}" target="_blank" class="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                        </svg>
                        LinkedIn Profile
                    </a>
                    @endif
                    
                    @if($application->user->jobSeekerProfile->portfolio_url)
                    <a href="{{ $application->user->jobSeekerProfile->portfolio_url }}" target="_blank" class="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                        </svg>
                        Portfolio
                    </a>
                    @endif
                </div>
            </div>
            @endif
        </div>
    </div>
</div>
@endsection
