@extends('layouts.admin')

@section('title', 'Revenue Analytics')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Revenue Analytics</h1>
            <p class="text-gray-600">Comprehensive insights into subscription revenue and growth</p>
        </div>
        <div class="flex items-center space-x-4">
            <!-- Period Filter -->
            <form method="GET" class="flex items-center space-x-2">
                <select name="period" onchange="this.form.submit()" class="rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500">
                    <option value="7" {{ $period == '7' ? 'selected' : '' }}>Last 7 days</option>
                    <option value="30" {{ $period == '30' ? 'selected' : '' }}>Last 30 days</option>
                    <option value="90" {{ $period == '90' ? 'selected' : '' }}>Last 90 days</option>
                    <option value="365" {{ $period == '365' ? 'selected' : '' }}>Last year</option>
                </select>
            </form>
            
            <!-- Export Button -->
            <a href="{{ route('admin.analytics.export', 'revenue') }}?period={{ $period }}" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                Export CSV
            </a>
        </div>
    </div>

    <!-- Key Metrics -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Total Revenue</dt>
                        <dd class="text-lg font-medium text-gray-900">${{ number_format($revenueStats['total_revenue'], 2) }}</dd>
                    </dl>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Monthly Revenue</dt>
                        <dd class="text-lg font-medium text-gray-900">
                            ${{ number_format($revenueStats['monthly_revenue']->last() ?? 0, 2) }}
                        </dd>
                    </dl>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">New Subscriptions</dt>
                        <dd class="text-lg font-medium text-gray-900">{{ number_format($revenueStats['new_subscriptions']) }}</dd>
                    </dl>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Avg. Revenue/User</dt>
                        <dd class="text-lg font-medium text-gray-900">
                            @php
                                $totalSubscriptions = $revenueStats['subscription_status']->sum();
                                $avgRevenue = $totalSubscriptions > 0 ? $revenueStats['total_revenue'] / $totalSubscriptions : 0;
                            @endphp
                            ${{ number_format($avgRevenue, 2) }}
                        </dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Revenue Growth Chart -->
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Revenue Growth Trend</h3>
            <div class="h-64">
                <canvas id="revenueGrowthChart"></canvas>
            </div>
        </div>

        <!-- Revenue by Plan Chart -->
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Revenue by Plan</h3>
            <div class="h-64">
                <canvas id="revenuePlanChart"></canvas>
            </div>
        </div>
    </div>

    <!-- Additional Charts -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Monthly Revenue Chart -->
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Monthly Revenue</h3>
            <div class="h-64">
                <canvas id="monthlyRevenueChart"></canvas>
            </div>
        </div>

        <!-- Subscription Status Chart -->
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Subscription Status</h3>
            <div class="h-64">
                <canvas id="subscriptionStatusChart"></canvas>
            </div>
        </div>
    </div>

    <!-- Detailed Statistics -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Revenue by Plan Breakdown -->
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Revenue by Plan</h3>
            <div class="space-y-3">
                @foreach($revenueStats['revenue_by_plan'] as $plan => $revenue)
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-gray-900">{{ $plan }}</span>
                    <span class="text-sm text-gray-500">${{ number_format($revenue, 2) }}</span>
                </div>
                @endforeach
            </div>
        </div>

        <!-- Subscription Statistics -->
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Subscription Statistics</h3>
            <div class="space-y-4">
                @foreach($revenueStats['subscription_status'] as $status => $count)
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-3 h-3 rounded-full mr-3 
                            @if($status === 'active') bg-green-500
                            @elseif($status === 'cancelled') bg-red-500
                            @elseif($status === 'expired') bg-yellow-500
                            @else bg-gray-500 @endif">
                        </div>
                        <span class="text-sm font-medium text-gray-900">{{ ucfirst($status) }}</span>
                    </div>
                    <span class="text-sm text-gray-500">{{ number_format($count) }}</span>
                </div>
                @endforeach
            </div>
        </div>
    </div>

    <!-- Revenue Insights -->
    <div class="bg-white rounded-lg shadow p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Revenue Insights</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="text-center">
                <div class="text-2xl font-bold text-green-600">
                    @php
                        $growthRate = $revenueStats['revenue_growth']->count() > 1 
                            ? (($revenueStats['revenue_growth']->last()->revenue ?? 0) - ($revenueStats['revenue_growth']->first()->revenue ?? 0)) / max($revenueStats['revenue_growth']->first()->revenue ?? 1, 1) * 100
                            : 0;
                    @endphp
                    {{ number_format($growthRate, 1) }}%
                </div>
                <div class="text-sm text-gray-500">Revenue Growth</div>
            </div>
            <div class="text-center">
                <div class="text-2xl font-bold text-blue-600">
                    {{ $revenueStats['revenue_by_plan']->count() }}
                </div>
                <div class="text-sm text-gray-500">Active Plans</div>
            </div>
            <div class="text-center">
                <div class="text-2xl font-bold text-purple-600">
                    ${{ number_format($revenueStats['revenue_by_plan']->avg(), 2) }}
                </div>
                <div class="text-sm text-gray-500">Avg. Plan Revenue</div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Revenue Growth Chart
const revenueGrowthCtx = document.getElementById('revenueGrowthChart').getContext('2d');
const revenueGrowthChart = new Chart(revenueGrowthCtx, {
    type: 'line',
    data: {
        labels: {!! json_encode($revenueStats['revenue_growth']->pluck('date')) !!},
        datasets: [{
            label: 'Daily Revenue',
            data: {!! json_encode($revenueStats['revenue_growth']->pluck('revenue')) !!},
            borderColor: 'rgb(34, 197, 94)',
            backgroundColor: 'rgba(34, 197, 94, 0.1)',
            tension: 0.1
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return '$' + value.toLocaleString();
                    }
                }
            }
        }
    }
});

// Revenue by Plan Chart
const revenuePlanCtx = document.getElementById('revenuePlanChart').getContext('2d');
const revenuePlanChart = new Chart(revenuePlanCtx, {
    type: 'doughnut',
    data: {
        labels: {!! json_encode($revenueStats['revenue_by_plan']->keys()) !!},
        datasets: [{
            data: {!! json_encode($revenueStats['revenue_by_plan']->values()) !!},
            backgroundColor: [
                'rgb(59, 130, 246)',
                'rgb(16, 185, 129)',
                'rgb(139, 92, 246)',
                'rgb(245, 158, 11)',
                'rgb(239, 68, 68)'
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            tooltip: {
                callbacks: {
                    label: function(context) {
                        return context.label + ': $' + context.parsed.toLocaleString();
                    }
                }
            }
        }
    }
});

// Monthly Revenue Chart
const monthlyRevenueCtx = document.getElementById('monthlyRevenueChart').getContext('2d');
const monthlyRevenueChart = new Chart(monthlyRevenueCtx, {
    type: 'bar',
    data: {
        labels: {!! json_encode($revenueStats['monthly_revenue']->keys()) !!},
        datasets: [{
            label: 'Monthly Revenue',
            data: {!! json_encode($revenueStats['monthly_revenue']->values()) !!},
            backgroundColor: 'rgba(59, 130, 246, 0.8)'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return '$' + value.toLocaleString();
                    }
                }
            }
        }
    }
});

// Subscription Status Chart
const subscriptionStatusCtx = document.getElementById('subscriptionStatusChart').getContext('2d');
const subscriptionStatusChart = new Chart(subscriptionStatusCtx, {
    type: 'doughnut',
    data: {
        labels: {!! json_encode($revenueStats['subscription_status']->keys()) !!},
        datasets: [{
            data: {!! json_encode($revenueStats['subscription_status']->values()) !!},
            backgroundColor: [
                'rgb(34, 197, 94)',
                'rgb(239, 68, 68)',
                'rgb(245, 158, 11)',
                'rgb(107, 114, 128)'
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false
    }
});
</script>
@endpush
@endsection
