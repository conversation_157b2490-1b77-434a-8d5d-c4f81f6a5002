@extends('layouts.admin')

@section('title', 'Job Management')

@section('content')
<div class="container-fluid px-6 py-8">
    <!-- Modern Header -->
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8">
        <div class="flex-1 min-w-0">
            <h1 class="text-3xl font-bold text-gray-900 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Job Management
            </h1>
            <p class="mt-2 text-lg text-gray-600">
                Manage and moderate all job postings with advanced filtering and real-time updates
            </p>
        </div>

        <div class="mt-4 flex flex-col sm:flex-row sm:items-center space-y-3 sm:space-y-0 sm:space-x-3 lg:mt-0">
            <!-- Export Jobs -->
            <a href="{{ route('admin.analytics.export', 'jobs') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200" title="Export all jobs to CSV">
                <svg class="-ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5M16.5 12L12 16.5m0 0L7.5 12m4.5 4.5V3" />
                </svg>
                Export All
            </a>

            <!-- Refresh -->
            <button type="button" onclick="refreshData()" class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200" title="Refresh Data">
                <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99" />
                </svg>
            </button>
        </div>
    </div>

    <!-- Livewire Jobs Table Component -->
    @livewire('admin.jobs-table')
</div>

@push('scripts')
<script>
// Prevent multiple initializations
if (typeof window.adminJobsInitialized === 'undefined') {
    window.adminJobsInitialized = true;

    // Wait for DOM to be ready
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Initializing admin jobs page...');
        initializeDropdowns();
        initializeModals();
    });

    // Dropdown functionality - single event listener
    function initializeDropdowns() {
        console.log('Setting up job dropdown functionality...');

        // Single click handler for the entire document
        document.addEventListener('click', function(event) {
            // Handle dropdown button clicks
            const dropdownButton = event.target.closest('button[onclick*="toggleJobActions"]');
            if (dropdownButton) {
                event.preventDefault();
                event.stopPropagation();

                // Extract job ID from onclick attribute
                const onclickAttr = dropdownButton.getAttribute('onclick');
                const jobIdMatch = onclickAttr.match(/toggleJobActions\((\d+)\)/);
                if (jobIdMatch) {
                    const jobId = parseInt(jobIdMatch[1]);
                    console.log('Job dropdown button clicked for job:', jobId);
                    toggleJobActions(jobId);
                }
                return;
            }

            // Handle clicks inside dropdown menus (don't close)
            const dropdownMenu = event.target.closest('[id^="jobActions"]');
            if (dropdownMenu) {
                return; // Don't close dropdown when clicking inside it
            }

            // Close all dropdowns when clicking outside
            const allDropdowns = document.querySelectorAll('[id^="jobActions"]');
            allDropdowns.forEach(dd => dd.classList.add('hidden'));
        });
    }

    // Global dropdown toggle function
    window.toggleJobActions = function(jobId) {
        console.log('Toggling dropdown for job:', jobId);

        const dropdown = document.getElementById('jobActions' + jobId);
        if (!dropdown) {
            console.error('Dropdown not found for job:', jobId);
            return;
        }

        // Close all other dropdowns first
        const allDropdowns = document.querySelectorAll('[id^="jobActions"]');
        allDropdowns.forEach(dd => {
            if (dd.id !== 'jobActions' + jobId) {
                dd.classList.add('hidden');
            }
        });

        // Toggle current dropdown
        dropdown.classList.toggle('hidden');
        console.log('Job dropdown toggled. Visible:', !dropdown.classList.contains('hidden'));
    };

    // Modal functions
    function initializeModals() {
        console.log('Setting up job modal functions...');

        window.approveJob = function(jobId) {
            if (confirm('Are you sure you want to approve this job?')) {
                console.log('Approving job:', jobId);
                fetch(`/admin/jobs/${jobId}/approve`, {
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                        'Accept': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        window.showNotification('Job approved successfully!', 'success');
                        if (typeof Livewire !== 'undefined') {
                            Livewire.dispatch('refreshComponent');
                        } else {
                            window.location.reload();
                        }
                    } else {
                        window.showNotification(data.message || 'Error approving job', 'error');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    window.showNotification('Error approving job', 'error');
                });
            }
        };

        window.rejectJob = function(jobId) {
            if (confirm('Are you sure you want to reject this job?')) {
                console.log('Rejecting job:', jobId);
                fetch(`/admin/jobs/${jobId}/reject`, {
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                        'Accept': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        window.showNotification('Job rejected successfully!', 'success');
                        if (typeof Livewire !== 'undefined') {
                            Livewire.dispatch('refreshComponent');
                        } else {
                            window.location.reload();
                        }
                    } else {
                        window.showNotification(data.message || 'Error rejecting job', 'error');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    window.showNotification('Error rejecting job', 'error');
                });
            }
        };

        window.featureJob = function(jobId) {
            if (confirm('Are you sure you want to feature this job?')) {
                console.log('Featuring job:', jobId);
                fetch(`/admin/jobs/${jobId}/feature`, {
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                        'Accept': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        window.showNotification('Job featured successfully!', 'success');
                        if (typeof Livewire !== 'undefined') {
                            Livewire.dispatch('refreshComponent');
                        } else {
                            window.location.reload();
                        }
                    } else {
                        window.showNotification(data.message || 'Error featuring job', 'error');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    window.showNotification('Error featuring job', 'error');
                });
            }
        };
    }

    // Utility functions
    window.showNotification = function(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full ${
            type === 'success' ? 'bg-green-500 text-white' :
            type === 'error' ? 'bg-red-500 text-white' :
            'bg-blue-500 text-white'
        }`;
        notification.textContent = message;

        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.classList.remove('translate-x-full');
        }, 100);

        // Remove after 3 seconds
        setTimeout(() => {
            notification.classList.add('translate-x-full');
            setTimeout(() => {
                if (document.body.contains(notification)) {
                    document.body.removeChild(notification);
                }
            }, 300);
        }, 3000);
    };

    window.refreshData = function() {
        if (typeof Livewire !== 'undefined') {
            Livewire.dispatch('refreshComponent');
            window.showNotification('Data refreshed', 'success');
        } else {
            window.location.reload();
        }
    };

} // End of initialization check
</script>
@endpush
@endsection
