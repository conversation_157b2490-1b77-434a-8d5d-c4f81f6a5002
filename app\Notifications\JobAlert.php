<?php

namespace App\Notifications;

use App\Models\SavedSearch;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Collection;

class JobAlert extends Notification implements ShouldQueue
{
    use Queueable;

    public $jobs;
    public $savedSearch;

    /**
     * Create a new notification instance.
     */
    public function __construct(Collection $jobs, SavedSearch $savedSearch)
    {
        $this->jobs = $jobs;
        $this->savedSearch = $savedSearch;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $jobCount = $this->jobs->count();
        $searchName = $this->savedSearch->name;
        
        $message = (new MailMessage)
            ->subject("New Job Alert: {$jobCount} new " . str_plural('job', $jobCount) . " found")
            ->greeting("Hello {$notifiable->name}!")
            ->line("We found {$jobCount} new " . str_plural('job', $jobCount) . " matching your saved search: '{$searchName}'")
            ->line('');
        
        // Add up to 5 job listings in the email
        foreach ($this->jobs->take(5) as $job) {
            $message->line("**{$job->title}** at {$job->employer->company_name ?? $job->employer->user->name}")
                    ->line("Location: {$job->location}")
                    ->line("Type: " . ucfirst(str_replace('-', ' ', $job->type)));
            
            if ($job->salary_min) {
                $salary = '$' . number_format($job->salary_min);
                if ($job->salary_max && $job->salary_max != $job->salary_min) {
                    $salary .= ' - $' . number_format($job->salary_max);
                }
                $message->line("Salary: {$salary}");
            }
            
            $message->action('View Job', route('jobs.show', $job))
                    ->line('---');
        }
        
        if ($jobCount > 5) {
            $remaining = $jobCount - 5;
            $message->line("And {$remaining} more " . str_plural('job', $remaining) . "...");
        }
        
        $message->action('View All Jobs', route('jobs.index', $this->savedSearch->filters))
                ->line('You can manage your job alerts in your profile settings.')
                ->line('Thank you for using Rectra!');
        
        return $message;
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'type' => 'job_alert',
            'message' => "Found {$this->jobs->count()} new " . str_plural('job', $this->jobs->count()) . " matching '{$this->savedSearch->name}'",
            'job_count' => $this->jobs->count(),
            'saved_search_id' => $this->savedSearch->id,
            'saved_search_name' => $this->savedSearch->name,
            'jobs' => $this->jobs->take(3)->map(function ($job) {
                return [
                    'id' => $job->id,
                    'title' => $job->title,
                    'company' => $job->employer->company_name ?? $job->employer->user->name,
                    'location' => $job->location,
                    'type' => $job->type,
                ];
            })->toArray(),
        ];
    }
}
