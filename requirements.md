# **Project Specification: "Rectra" Job Board & Training Platform**

**Version:** 1.0
**Date:** 2025-06-12
**Author:** <PERSON>
**Target Stack:** Laravel, MySQL, Livewire, Tailwind CSS

---

## **1.0 Introduction**

This document outlines the detailed requirements for the development of "Rectra" a modern, scalable job board and professional development platform. This specification will serve as the primary guide for the AI agent tasked with its development. The core mission is to create a seamless, intuitive, and valuable ecosystem for job seekers, employers, and administrators, rivaling the user experience of leading platforms like Indeed and CV-Library.

### **1.1 Project Purpose**

The primary purpose of Rectra is to create a dual-function platform that:
1.  **Streamlines Recruitment:** Facilitates an efficient and effective job search and application process through advanced filtering, instant job alerts, comprehensive user profiles, and robust application tracking.
2.  **Fosters Professional Growth:** Provides an integrated training module offering educational courses, certifications, and skill development resources to empower users and enhance their career prospects.
3.  **Generates Revenue:** Establishes a sustainable business model through tiered subscription plans for employers and premium content access for job seekers.

### **1.2 Scope**

#### **In-Scope (MVP & V1)**
* **Core Job Board:** Job posting, advanced search, application management.
* **User Roles & Profiles:** Detailed profiles for Job Seekers and Employers, plus an Admin role.
* **Subscription Engine:** Integration of a payment gateway (Stripe) to manage recurring subscriptions for employers (e.g., `Basic`, `Premium`) and one-time purchases for job seekers (e.g., premium courses).
* **Training Module:** Course catalog, video/text content delivery, progress tracking, and certification upon completion.
* **Key Features:** Resume builder/uploader, job alerts, company reviews, and AI-powered job recommendations.
* **Technical Foundation:** Responsive design (mobile-first), cloud-native infrastructure, adherence to security best practices, and WCAG 2.1 accessibility standards.

#### **Out-of-Scope (Post-V1)**
* Native mobile applications (iOS/Android).
* Direct hardware procurement.
* Complex third-party vendor contracts beyond specified API integrations (e.g., job scraping/aggregation from other boards).
* In-platform video conferencing for interviews.
* Advanced HRIS (Human Resource Information System) integrations.

---

## **2.0 Personas & Target Audience**

1.  **Job Seeker (Sarah, 28):** A marketing professional with 5 years of experience looking to switch industries. *Needs:* Advanced search to find remote roles, skill-based job recommendations, and a resume builder to tailor her CV. *Goal:* Find a relevant job quickly and upskill with a digital marketing certification.
2.  **Employer/Recruiter (David, 45):** A hiring manager at a mid-sized tech company in Abuja. *Needs:* A dashboard to manage multiple job postings, tools to efficiently screen and filter candidates by skills, and access to a diverse talent pool. *Goal:* Reduce time-to-hire by 25% and find qualified candidates who are a good cultural fit.
3.  **Training Participant (Leo, 22):** A recent computer science graduate from the University of Lagos. *Needs:* Foundational courses on "Coding Interview Prep" and "Effective Resume Writing" to land his first job. *Goal:* Gain practical skills and earn shareable certificates to enhance his LinkedIn profile.
4.  **Administrator (Admin):** The platform manager. *Needs:* A comprehensive backend panel to manage users, moderate content (jobs, reviews), oversee subscription plans, and manage the course catalog. *Goal:* Ensure platform integrity, user safety, and smooth operation.

---

## **3.0 Functional Requirements**

### **3.1 Global & Platform-Wide**

* **Authentication:**
    * Secure user registration and login with email/password.
    * Password hashing using `Bcrypt`.
    * Socialite integration for "Login with Google" and "Login with LinkedIn".
    * Password reset functionality ("Forgot Password").
    * Role-based access control (RBAC) using `Spatie/laravel-permission` to manage `Job Seeker`, `Employer`, and `Admin` roles.

### **3.2 Job Seeker Features**

* **Profile Management:**
    * Dashboard to manage profile, applications, and saved jobs.
    * **Resume Builder:** A step-by-step Livewire component to build a resume from scratch. Sections: Personal Info, Summary, Work Experience, Education, Skills, Certifications, Portfolio Links.
    * **Resume Uploader:** Upload multiple resume versions (PDF, DOCX) using `Spatie/laravel-medialibrary`. Auto-parse key details from the primary PDF to pre-fill the profile (consider a queued job for this).
    * Resume privacy settings: `Public`, `Private (only when applying)`, `Anonymous`.
* **Job Search & Application:**
    * **Advanced Search Page:** A full-page Livewire component for real-time filtering without page reloads.
    * **Filters:**
        * Keywords (Job Title, Company, Skills). Use `Laravel Scout` with a MySQL full-text index or Algolia/Meilisearch driver for performance.
        * Location (City, State, Country, with "Remote" as an option).
        * Salary Range (slider or predefined ranges).
        * Job Type (`Full-time`, `Part-time`, `Contract`, `Internship`).
        * Experience Level (`Entry-level`, `Mid-level`, `Senior`, `Manager`).
        * Industry.
    * **Saved Searches & Job Alerts:** Save search filter combinations. A scheduled command (`php artisan schedule:run`) will run daily/weekly to find matching new jobs and email notifications to users via a queued job.
    * **Application Tracking:** Dashboard view of all applications with statuses: `Submitted`, `Viewed by Employer`, `In Review`, `Shortlisted`, `Rejected`.
    * **"Easy Apply":** One-click application using the primary resume on file.

### **3.3 Employer Features**

* **Company Profile:**
    * Create and manage a public-facing company page with logo, banner image, company description, industry, size, location, and website link.
* **Employer Dashboard:**
    * Central hub to post new jobs, manage existing listings, and view candidate applications.
    * Analytics on job postings: `Views`, `Number of Applicants`.
* **Job Management:**
    * A CRUD interface for creating, editing, archiving, and deleting job postings.
    * Job description field should support Markdown for rich text formatting.
* **Candidate Management:**
    * View a list of applicants for each job.
    * Filter applicants by keywords in their resume or profile skills.
    * Update application statuses, which will notify the job seeker.
    * Internal commenting/notes system per applicant (visible only to the employer's team).

### **3.4 Training Module**

* **Course Catalog:** Publicly browsable grid/list of all available courses, showing title, category, and whether it's `Free` or `Premium`.
* **Course Structure:**
    * Courses are composed of `Modules`, and Modules are composed of `Lessons`.
    * Lessons can be `video` (embedded Vimeo/Wistia) or `text/article`.
* **Progress Tracking:**
    * The system automatically tracks completion of each lesson.
    * User dashboard shows a progress bar for each enrolled course.
* **Certification:**
    * Upon 100% course completion, a unique digital certificate (PDF) is generated using a package like `barryvdh/laravel-dompdf`.
    * Certificates are stored in the user's profile and can be shared via a unique public URL or directly to LinkedIn.

### **3.5 Subscription & Monetization**

* **Stripe Integration:** Use `Laravel Cashier` for seamless integration.
* **Employer Subscription Tiers:**
    * **Free Plan:** 1 active job post, limited candidate views, standard listing.
    * **Basic Plan ($X/month):** 5 active job posts, basic analytics, company branding.
    * **Premium Plan ($Y/month):** Unlimited job posts, "Featured Job" highlighting, access to search the job seeker/resume database, advanced candidate filtering.
* **Job Seeker Monetization (One-time Purchase):**
    * Most courses are free.
    * Premium/Certified courses require a one-time payment, processed via Stripe.

### **3.6 Admin Panel Features**

* **Dashboard:** Overview of site statistics (new users, job posts, revenue).
* **User Management:** View, edit, suspend, and delete users. Manually assign roles.
* **Job Post Management:** Moderate (approve/reject) new job posts. Edit or remove inappropriate listings.
* **Subscription Management:** View all active subscriptions, revenue, and manage plans.
* **Course Management:** Full CRUD for Courses, Modules, and Lessons.
* **Content Moderation:** Manage and moderate company reviews submitted by users.

---

## **4.0 Non-Functional Requirements**

* **Performance:**
    * Page Load Time: < 3 seconds.
    * Leverage Laravel Octane for enhanced performance.
    * Utilize caching for routes, configs, and views (`php artisan optimize`).
    * Use Redis for application caching (e.g., complex queries, user profiles).
    * Offload slow tasks (email sending, resume parsing) to Queue Workers.
    * Optimize database queries (use eager loading to prevent N+1 problems).
* **Scalability:**
    * The application must be stateless to allow for horizontal scaling.
    * Utilize AWS S3 for all user-uploaded files (resumes, company logos).
    * Database: MySQL 8.0+ on a scalable instance (e.g., AWS RDS).
* **Security:**
    * Enable Laravel's built-in CSRF protection on all forms.
    * Sanitize all user input to prevent XSS attacks.
    * Use parameter binding in all database queries (handled by Eloquent) to prevent SQL injection.
    * Implement HTTPS across the entire application.
    * Adhere to GDPR/CCPA principles for data handling and user consent.
* **Reliability:**
    * Target 99.9% uptime.
    * Implement comprehensive logging (e.g., to Sentry or Flare) for error tracking.
    * Regular, automated database backups.
* **Usability & Accessibility:**
    * Mobile-first responsive design using Tailwind CSS.
    * Intuitive navigation.
    * Compliance with WCAG 2.1 Level AA:
        * Semantic HTML (`<nav>`, `<main>`, `<header>`).
        * Sufficient color contrast.
        * Keyboard navigability.
        * `aria-` attributes where necessary.
        * `alt` text for all informative images.

---

## **5.0 System Architecture & Database Design**

### **5.1 Technology Stack**
* **Backend Framework:** Laravel 11+
* **Frontend:** Livewire 3+ & Alpine.js 3+
* **CSS Framework:** Tailwind CSS 3+
* **Database:** MySQL 8.0+
* **Server:** Nginx
* **Caching:** Redis
* **Queue Driver:** Redis or AWS SQS

### **5.2 Key Laravel Packages**
* `livewire/livewire`: Core reactive component framework.
* `laravel/cashier-stripe`: Subscription management.
* `laravel/scout`: Full-text search abstraction.
* `laravel/socialite`: OAuth authentication.
* `spatie/laravel-permission`: Role-based access control.
* `spatie/laravel-medialibrary`: File and media management.
* `barryvdh/laravel-dompdf`: PDF generation for certificates.

### **5.3 High-Level Database Schema**

* `users`: id, name, email, password, role_id, email_verified_at.
* `job_seeker_profiles`: user_id (FK), summary, phone_number.
* `employer_profiles`: user_id (FK), company_name, description, logo_path, website.
* `jobs`: id, employer_profile_id (FK), title, description, location, job_type, salary_min, salary_max, is_featured, status.
* `job_applications`: id, job_id (FK), job_seeker_profile_id (FK), resume_path, status.
* `resumes`: id, job_seeker_profile_id (FK), file_path, file_name, is_primary.
* `subscriptions` (from Cashier): user_id, name, stripe_id, stripe_status, stripe_plan, quantity, ends_at.
* `courses`: id, title, slug, description, thumbnail_path, is_premium (boolean).
* `lessons`: id, course_id (FK), title, content_type (`video`, `text`), content_path, order.
* `course_enrollments`: user_id (FK), course_id (FK), progress (integer), completed_at.
* `company_reviews`: id, job_seeker_profile_id (FK), employer_profile_id (FK), rating, title, body, is_approved.

*Note: This is a simplified model. Relationships (one-to-many, many-to-many) will be defined using Eloquent ORM.*

---

## **6.0 Testing & Deployment**

### **6.1 Testing Strategy**
* **Unit Tests (Pest/PHPUnit):** Test individual methods and classes (e.g., helper functions, specific service class logic).
* **Feature Tests (Pest/PHPUnit):** Test API endpoints and controller logic to ensure correct responses and behavior.
* **Livewire Component Tests:** Test component actions, data binding, and rendering logic.
* **Browser/E2E Tests (Laravel Dusk):** Simulate user journeys in a real browser (e.g., user registers -> builds resume -> applies for a job).
* **Acceptance Criteria:** All user stories must have passing tests; 95% test coverage target.

### **6.2 Deployment**
* **CI/CD Pipeline (GitHub Actions):**
    * On `push` to `main` branch:
        1.  Run tests.
        2.  Build frontend assets (`npm run build`).
        3.  Deploy to a production environment (e.g., using Laravel Vapor, Forge, or a custom script).
* **Environments:**
    * `local`: Developer machines.
    * `staging`: A mirror of production for final testing.
    * `production`: Live user-facing application.

---

## **7.0 User Stories**

### **7.1 Job Seeker Stories**
* **As a** job seeker, **I want to** create a profile by uploading my existing PDF resume **so that** the system can auto-fill my details and I can get started quickly.
* **As a** job seeker, **I want to** filter job listings by "Remote" and salary range **so that** I only see opportunities that match my financial needs and lifestyle.
* **As a** job seeker, **I want to** save a search for "Laravel Developer in Nigeria" and receive daily email alerts **so that** I don't miss any new, relevant openings.
* **As a** job seeker, **I want to** see the status of my applications (e.g., "Viewed by Employer") on my dashboard **so that** I can track my progress and know where I stand.
* **As a** job seeker, **I want to** enroll in a free "Interview Skills" course **so that** I can feel more confident and prepared for my interviews.
* **As a** job seeker, **I want to** read anonymous reviews of a company from other employees **so that** I can get an insight into the company culture before I apply.

### **7.2 Employer Stories**
* **As an** employer, **I want to** purchase a "Premium" monthly subscription using a credit card **so that** I can post multiple jobs and access the resume database.
* **As an** employer, **I want to** create a branded company page with our logo and mission **so that** we can attract candidates who align with our values.
* **As an** employer, **I want to** search the resume database for candidates with "Livewire" and "Tailwind CSS" skills **so that** I can proactively find qualified talent.
* **As an** employer, **I want to** manage all applicants for a specific job in a single dashboard view **so that** I can efficiently screen, shortlist, and reject candidates.
* **As an** employer, **I want to** assign a "Diversity Hiring" course to my recruitment team **so that** we can improve our hiring practices.

### **7.3 Administrator Stories**
* **As an** administrator, **I want to** review and approve new job postings **so that** I can ensure they are legitimate and meet our quality standards.
* **As an** administrator, **I want to** view a dashboard of monthly recurring revenue (MRR) from subscriptions **so that** I can track the platform's financial performance.
* **As an** administrator, **I want to** add a new course with video lessons and text modules to the training catalog **so that** we can keep our content offerings fresh and relevant.
* **As an** administrator, **I want to** suspend a user account that has violated our terms of service **so that** I can maintain a safe and professional community.

---

## **8.0 Use Cases**

### **Use Case 1: Job Seeker Applies for a Job**
* **Actor:** Job Seeker (logged in)
* **Preconditions:** The Job Seeker has a completed profile and a primary resume uploaded.
* **Main Success Scenario:**
    1.  User navigates to the "Job Search" page.
    2.  User enters "Project Manager" into the search bar and filters by location "Abuja".
    3.  System displays a list of matching jobs.
    4.  User clicks on a job title to view the detailed description.
    5.  User clicks the "Easy Apply" button.
    6.  System submits the application using the user's primary profile and resume.
    7.  A confirmation message "Application Sent!" is displayed. The application is added to the user's dashboard with the status "Submitted".
* **Postconditions:** Employer receives the application. Job Seeker can track the application.
* **Exceptions:**
    * If the user's profile is incomplete, the system will prompt them to complete the required sections before applying.

### **Use Case 2: Employer Posts a Featured Job**
* **Actor:** Employer (logged in)
* **Preconditions:** The Employer has an active "Premium" subscription.
* **Main Success Scenario:**
    1.  User navigates to their Employer Dashboard.
    2.  User clicks "Post a New Job".
    3.  User fills out the job creation form (Title, Description, Salary, etc.).
    4.  User selects the "Mark as Featured" checkbox (available due to their subscription).
    5.  User clicks "Publish Job".
    6.  System validates the form and publishes the job listing. It appears on the main job board with a "Featured" badge.
* **Postconditions:** The job is live and visible to all job seekers. The cost is covered by the subscription.
* **Exceptions:**
    * If the employer does not have an active premium subscription, the "Mark as Featured" option is disabled or prompts an upgrade.
    * If required fields are missing, the form displays validation errors.

---

## **9.0 User Journeys**

### **9.1 Job Seeker: Leo's First Job Hunt**
1.  **Discovery:** Leo, a recent graduate, finds Rectra through a Google search for "tech jobs in Nigeria".
2.  **Registration:** He registers for a free account using his Google login.
3.  **Onboarding:** The platform prompts him to build his profile. He uses the resume builder to create a professional-looking CV from scratch.
4.  **Upskilling:** He browses the Training Module and enrolls in the free "Resume Writing 101" course. After completing it, he updates his new resume with the tips he learned.
5.  **Search & Alert:** He searches for "Junior Software Engineer" jobs, saves the search, and enables daily alerts.
6.  **Application:** The next day, he receives an email alert for a new opening. He likes the company review and applies using the "Easy Apply" feature.
7.  **Tracking:** He checks his dashboard over the next week, seeing his application status change from "Submitted" to "Viewed by Employer".
8.  **Success:** He gets an interview request and uses the skills learned from the "Interview Skills" course to prepare. He lands the job.

### **9.2 Employer: David's Hiring Campaign**
1.  **Discovery:** David, a hiring manager, needs to hire a marketing manager. A colleague recommends Rectra.
2.  **Registration & Subscription:** He registers his company and immediately subscribes to the "Premium Plan" to get maximum visibility and access to the resume database.
3.  **Job Posting:** He posts the "Marketing Manager" position, marking it as "Featured". The job instantly gets high visibility on the homepage.
4.  **Proactive Sourcing:** While applications come in, he uses the resume search feature to find candidates with "SEO" and "PPC" skills, inviting three promising individuals to apply.
5.  **Candidate Management:** He uses his dashboard to review all 50+ applicants, shortlisting the top 10 and sending bulk rejection notices to the rest with a single click.
6.  **Hiring:** After interviews, he updates the status of his chosen candidate to "Hired" and archives the job posting. The entire process took two weeks, down from an average of four.

---

## **10.0 API Strategy**

While the primary interface will be built with Livewire, a RESTful API will be developed to ensure future scalability and extensibility (e.g., for a future mobile app or third-party integrations).

* **Standard:** RESTful principles.
* **Format:** JSON for all requests and responses.
* **Authentication:** `Laravel Sanctum` will be used for token-based authentication for external clients.
* **Versioning:** The API will be versioned via the URL (e.g., `/api/v1/...`).
* **Key Endpoints (v1):**
    * `GET /api/v1/jobs`: Search and filter jobs.
    * `GET /api/v1/jobs/{id}`: Retrieve a single job.
    * `GET /api/v1/courses`: List available training courses.
    * `GET /api/v1/user/applications`: (Authenticated) View user's job applications.
    * `POST /api/v1/jobs/{id}/apply`: (Authenticated) Apply for a job.
* **Documentation:** API documentation will be generated automatically using a package like `Scribe`.

---

## **11.0 Risks and Mitigation**

| Risk ID | Risk Description | Impact | Mitigation Strategy |
| :--- | :--- | :--- | :--- |
| **T-01** | **Performance Bottlenecks:** Complex Livewire components with large datasets may lead to slow response times. | Medium | Proactively identify complex components. Utilize Livewire's deferred loading (`wire:init`), caching, and offload heavy computations to the backend. Rigorous performance testing during development. |
| **T-02** | **Payment Gateway Failure:** Stripe API downtime or integration errors could prevent subscriptions and purchases. | High | Implement robust error handling and logging for all Stripe interactions. Set up monitoring to alert administrators of API failures. Have a clear user-facing message for when the payment system is unavailable. |
| **P-01** | **Scope Creep:** Stakeholders request additional features not in the MVP, delaying the launch. | High | Adhere strictly to the defined scope in this document. All new feature requests must go through a formal change request process and be evaluated for a post-MVP release. |
| **B-01** | **Low User Adoption:** Both job seekers and employers fail to register on the new platform. | High | Implement a pre-launch marketing strategy. Offer early-bird discounts on employer subscriptions. Ensure the free tier for both users is genuinely useful to drive initial traffic and word-of-mouth. |
| **B-02** | **Poor Quality of Job Postings:** The platform is flooded with low-quality, scam, or duplicate jobs. | High | Implement a mandatory admin approval process for all new employers and their first few job posts. Develop an easy-to-use "Report Job" feature for users. |
| **S-01** | **Data Breach:** Sensitive user data (resumes, personal info) is compromised. | High | Adhere strictly to security best practices outlined in section 4.0. Conduct regular security audits and dependency scans. Encrypt sensitive data at rest. |

---

## **12.0 Training & Support**

* **User Knowledge Base:**
    * A public, searchable Help Center with articles and FAQs covering all major features (e.g., "How to build your resume", "How to post a job", "How to purchase a course").
* **Video Tutorials:**
    * Short, 1-2 minute video tutorials embedded within the knowledge base and linked from relevant dashboard pages, demonstrating key tasks.
* **Support System:**
    * An email-based support system (`<EMAIL>`) for handling user queries.
    * For logged-in users, a simple support ticket form within their dashboard.
* **Administrator Manual:**
    * A separate, detailed technical manual for platform administrators covering user management, content moderation, and financial oversight.

---

## **13.0 Glossary**

| Term | Definition |
| :--- | :--- |
| **TALL Stack** | An acronym for the core technologies used: **T**ailwind CSS, **A**lpine.js, **L**ivewire, and **L**aravel. |
| **Livewire** | A full-stack framework for Laravel that allows building dynamic interfaces with PHP, minimizing the need for custom JavaScript. |
| **Laravel Scout** | A Laravel package that provides a simple, driver-based solution for adding full-text search to Eloquent models. |
| **Laravel Cashier** | A Laravel package that provides an expressive, fluent interface to Stripe's subscription billing services. |
| **MVP** | **M**inimum **V**iable **P**roduct. The initial version of the application containing just enough features to be usable by early customers. |
| **RBAC** | **R**ole-**B**ased **A**ccess **C**ontrol. A security paradigm that restricts system access to authorized users based on their role. |
| **Queued Job** | A task (e.g., sending an email) that is dispatched to a background process to be executed asynchronously, preventing the user from having to wait. |
| **WCAG** | **W**eb **C**ontent **A**ccessibility **G**uidelines. A set of standards for making web content more accessible to people with disabilities. |

---

## **14.0 Document Approval**

This document serves as the agreed-upon blueprint for the Rectra platform. Sign-off from the stakeholders below indicates their acceptance of these requirements and authorizes the development team to proceed.

| Role | Name | Signature | Date |
| :--- | :--- | :--- | :--- |
| **Project Manager** | | | |
| **Lead Developer** | | | |
| **Client Representative** | | | |
