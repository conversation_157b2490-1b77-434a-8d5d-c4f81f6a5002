<?php $__env->startSection('title', 'User Management'); ?>

<?php $__env->startSection('content'); ?>
<div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-8 fade-in-modern">
    <!-- Modern Header -->
    <div class="flex items-center justify-between mb-8 slide-up-modern">
        <div>
            <h1 class="text-4xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 bg-clip-text text-transparent">User Management</h1>
            <p class="mt-3 text-lg text-gray-600">Manage all users, roles, and permissions with advanced controls</p>

            <!-- Quick Stats Indicators -->
            <div class="flex items-center space-x-6 mt-4">
                <div class="flex items-center space-x-2">
                    <div class="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                    <span class="text-sm font-semibold text-gray-700"><?php echo e(number_format($statusCounts['active'])); ?> Active</span>
                </div>
                <div class="flex items-center space-x-2">
                    <div class="w-3 h-3 bg-yellow-500 rounded-full animate-pulse"></div>
                    <span class="text-sm font-semibold text-gray-700"><?php echo e(number_format($statusCounts['unverified'])); ?> Pending</span>
                </div>
                <div class="flex items-center space-x-2">
                    <div class="w-3 h-3 bg-blue-500 rounded-full animate-pulse"></div>
                    <span class="text-sm font-semibold text-gray-700"><?php echo e(number_format($statusCounts['all'])); ?> Total</span>
                </div>
            </div>
        </div>
        <div class="flex items-center space-x-3 mt-6 sm:mt-0 scale-in-modern">
            <!-- Bulk Actions -->
            <div class="relative">
                <button type="button" id="bulkActionsBtn" class="btn-modern btn-secondary-modern interactive-element">
                    <svg class="-ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M9 12h3.75M9 15h3.75M9 18h3.75m3-6h3.75m-3.75 3h3.75m-3.75 3h3.75M5.25 6.75h.007v.008H5.25V6.75zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zM5.25 12h.007v.008H5.25V12zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zM5.25 17.25h.007v.008H5.25v-.008zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z" />
                    </svg>
                    Bulk Actions
                    <svg class="ml-2 -mr-1 h-4 w-4 transition-transform duration-200" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
                    </svg>
                </button>
                <div id="bulkActionsMenu" class="hidden absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-xl bg-white py-2 shadow-xl ring-1 ring-black ring-opacity-5 focus:outline-none border border-gray-100">
                    <button type="button" onclick="bulkExport()" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-700 rounded-lg mx-2 transition-colors duration-150">
                        <svg class="inline w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        Export Selected
                    </button>
                    <button type="button" onclick="bulkActivate()" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-green-50 hover:text-green-700 rounded-lg mx-2 transition-colors duration-150">
                        <svg class="inline w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        Activate Selected
                    </button>
                    <button type="button" onclick="bulkSuspend()" class="block w-full text-left px-4 py-2 text-sm text-red-700 hover:bg-red-50 hover:text-red-800 rounded-lg mx-2 transition-colors duration-150">
                        <svg class="inline w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728"></path>
                        </svg>
                        Suspend Selected
                    </button>
                </div>
            </div>

            <!-- Export Users -->
            <a href="<?php echo e(route('admin.analytics.export', 'users')); ?>" class="btn-modern btn-secondary-modern interactive-element" title="Export all users to CSV">
                <svg class="-ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5M16.5 12L12 16.5m0 0L7.5 12m4.5 4.5V3" />
                </svg>
                Export All
            </a>

            <!-- Create User -->
            <button type="button" onclick="openCreateUserModal()" class="btn-modern btn-primary-modern interactive-element" title="Create a new user">
                <svg class="-ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
                </svg>
                Add User
            </button>

            <!-- Refresh -->
            <button type="button" onclick="refreshData()" class="btn-modern btn-secondary-modern interactive-element p-2.5" title="Refresh Data">
                <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99" />
                </svg>
            </button>
        </div>
    </div>

    <!-- Modern Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="card-modern interactive-element">
            <div class="card-body-modern">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg">
                            <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M15 19.128a9.38 9.38 0 002.625.372 9.337 9.337 0 004.121-.952 4.125 4.125 0 00-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 018.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0111.964-3.07M12 6.375a3.375 3.375 0 11-6.75 0 3.375 3.375 0 016.75 0zm8.25 2.25a2.625 2.625 0 11-5.25 0 2.625 2.625 0 015.25 0z" />
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4 flex-1">
                        <dl>
                            <dt class="text-sm font-semibold text-gray-600 uppercase tracking-wider">Total Users</dt>
                            <dd class="text-2xl font-bold text-gray-900"><?php echo e(number_format($statusCounts['all'])); ?></dd>
                            <dd class="text-xs text-blue-600 font-medium">All registered users</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="card-modern interactive-element">
            <div class="card-body-modern">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center shadow-lg">
                            <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4 flex-1">
                        <dl>
                            <dt class="text-sm font-semibold text-gray-600 uppercase tracking-wider">Active Users</dt>
                            <dd class="text-2xl font-bold text-gray-900"><?php echo e(number_format($statusCounts['active'])); ?></dd>
                            <dd class="text-xs text-green-600 font-medium">Verified & active</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="card-modern interactive-element">
            <div class="card-body-modern">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-xl flex items-center justify-center shadow-lg">
                            <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z" />
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4 flex-1">
                        <dl>
                            <dt class="text-sm font-semibold text-gray-600 uppercase tracking-wider">Pending</dt>
                            <dd class="text-2xl font-bold text-gray-900"><?php echo e(number_format($statusCounts['unverified'])); ?></dd>
                            <dd class="text-xs text-yellow-600 font-medium">Awaiting verification</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="card-modern interactive-element">
            <div class="card-body-modern">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                            <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 3v11.25A2.25 2.25 0 006 16.5h2.25M3.75 3h-1.5m1.5 0h16.5m0 0h1.5m-1.5 0v11.25A2.25 2.25 0 0118 16.5h-2.25m-7.5 0h7.5m-7.5 0l-1 3m8.5-3l1 3m0 0l-1-3m1 3l-1-3m-16.5 0h16.5" />
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4 flex-1">
                        <dl>
                            <dt class="text-sm font-semibold text-gray-600 uppercase tracking-wider">Growth Rate</dt>
                            <dd class="text-2xl font-bold text-gray-900">+12.5%</dd>
                            <dd class="text-xs text-purple-600 font-medium">This month</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modern Filters -->
    <div class="card-modern mb-8">
        <div class="card-header-modern">
            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.414A1 1 0 013 6.707V4z"></path>
                </svg>
                Advanced Filters
            </h3>
        </div>
        <div class="card-body-modern">
            <form method="GET" action="<?php echo e(route('admin.users.index')); ?>" class="grid grid-cols-1 md:grid-cols-4 gap-6">
                <!-- Search -->
                <div>
                    <label for="search" class="block text-sm font-semibold text-gray-700 mb-2">Search Users</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                        <input type="text" name="search" id="search" value="<?php echo e(request('search')); ?>" placeholder="Name, email, or ID..." class="form-input-modern pl-10">
                    </div>
                </div>

                <!-- Role Filter -->
                <div>
                    <label for="role" class="block text-sm font-semibold text-gray-700 mb-2">Filter by Role</label>
                    <select name="role" id="role" class="form-select-modern">
                        <option value="">All Roles</option>
                        <?php $__currentLoopData = $roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($role); ?>" <?php echo e(request('role') === $role ? 'selected' : ''); ?>>
                                <?php echo e(ucfirst(str_replace('_', ' ', $role))); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>

                <!-- Status Filter -->
                <div>
                    <label for="status" class="block text-sm font-semibold text-gray-700 mb-2">Filter by Status</label>
                    <select name="status" id="status" class="form-select-modern">
                        <option value="">All Statuses</option>
                        <?php $__currentLoopData = $statuses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $status): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($status); ?>" <?php echo e(request('status') === $status ? 'selected' : ''); ?>>
                                <?php echo e(ucfirst(str_replace('_', ' ', $status))); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>

                <!-- Submit -->
                <div class="flex items-end space-x-2">
                    <button type="submit" class="btn-modern btn-primary-modern flex-1 interactive-element">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.414A1 1 0 013 6.707V4z"></path>
                        </svg>
                        Apply Filters
                    </button>
                    <?php if(request()->hasAny(['search', 'role', 'status'])): ?>
                        <a href="<?php echo e(route('admin.users.index')); ?>" class="btn-modern btn-secondary-modern interactive-element">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </a>
                    <?php endif; ?>
                </div>
            </form>
        </div>
    </div>

    <!-- Modern Users Table -->
    <div class="card-modern">
        <div class="card-header-modern">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                    <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                    Users Directory
                    <span class="ml-2 badge-modern badge-info-modern"><?php echo e($users->total()); ?> users</span>
                </h3>
                <div class="flex items-center space-x-2">
                    <span class="text-sm text-gray-500">Showing <?php echo e($users->firstItem()); ?>-<?php echo e($users->lastItem()); ?> of <?php echo e($users->total()); ?></span>
                </div>
            </div>
        </div>
        <div class="overflow-x-auto">
            <table class="table-modern">
                <thead>
                    <tr>
                        <th scope="col" class="relative px-6 py-4">
                            <input type="checkbox" id="selectAll" class="absolute left-4 top-1/2 -mt-2 h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500 transition-colors duration-200">
                        </th>
                        <th scope="col" class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                            User Information
                        </th>
                        <th scope="col" class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                            Role & Status
                        </th>
                        <th scope="col" class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                            Activity
                        </th>
                        <th scope="col" class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                            Last Active
                        </th>
                        <th scope="col" class="relative px-6 py-4">
                            <span class="sr-only">Actions</span>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <?php $__empty_1 = true; $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr class="interactive-element" data-user-id="<?php echo e($user->id); ?>">
                            <td class="relative px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" class="user-checkbox absolute left-4 top-1/2 -mt-2 h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500 transition-colors duration-200" value="<?php echo e($user->id); ?>">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-12 w-12">
                                        <?php if($user->avatar): ?>
                                            <img class="h-12 w-12 rounded-xl object-cover shadow-md" src="<?php echo e($user->avatar); ?>" alt="<?php echo e($user->name); ?>">
                                        <?php else: ?>
                                            <div class="h-12 w-12 rounded-xl bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center shadow-md">
                                                <span class="text-lg font-bold text-white"><?php echo e(substr($user->name, 0, 1)); ?></span>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-semibold text-gray-900"><?php echo e($user->name); ?></div>
                                        <div class="text-sm text-gray-500"><?php echo e($user->email); ?></div>
                                        <div class="text-xs text-gray-400 mt-1">ID: <?php echo e($user->id); ?></div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="space-y-2">
                                    <span class="badge-modern
                                        <?php if($user->role === 'admin'): ?> badge-danger-modern
                                        <?php elseif($user->role === 'employer'): ?> badge-info-modern
                                        <?php else: ?> badge-success-modern <?php endif; ?>">
                                        <?php echo e(ucfirst(str_replace('_', ' ', $user->role))); ?>

                                    </span>
                                    <div>
                                        <?php if(!$user->email_verified_at): ?>
                                            <span class="badge-modern badge-warning-modern">
                                                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                                </svg>
                                                Unverified
                                            </span>
                                        <?php else: ?>
                                            <span class="badge-modern badge-success-modern">
                                                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                </svg>
                                                Active
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900 font-medium"><?php echo e($user->created_at->format('M d, Y')); ?></div>
                                <div class="text-xs text-gray-500"><?php echo e($user->created_at->diffForHumans()); ?></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <?php if($user->last_login_at): ?>
                                    <div class="text-sm text-gray-900 font-medium"><?php echo e($user->last_login_at->diffForHumans()); ?></div>
                                    <div class="text-xs text-gray-500"><?php echo e($user->last_login_at->format('M d, Y H:i')); ?></div>
                                <?php else: ?>
                                    <div class="text-sm text-gray-500">Never logged in</div>
                                <?php endif; ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <div class="flex items-center justify-end space-x-2">
                                    <!-- Quick Actions Dropdown -->
                                    <div class="relative">
                                        <button type="button" onclick="toggleUserActions(<?php echo e($user->id); ?>)" class="btn-modern btn-sm-modern btn-secondary-modern interactive-element">
                                            Actions
                                            <svg class="ml-1 -mr-1 h-3 w-3 transition-transform duration-200" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
                                            </svg>
                                        </button>
                                        <div id="userActions<?php echo e($user->id); ?>" class="hidden absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-xl bg-white py-2 shadow-xl ring-1 ring-black ring-opacity-5 focus:outline-none border border-gray-100">
                                            <a href="<?php echo e(route('admin.users.show', $user)); ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-700 rounded-lg mx-2 transition-colors duration-150">
                                                <svg class="inline w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                                </svg>
                                                View Details
                                            </a>
                                            <a href="<?php echo e(route('admin.users.edit', $user)); ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-purple-50 hover:text-purple-700 rounded-lg mx-2 transition-colors duration-150">
                                                <svg class="inline w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                                </svg>
                                                Edit User
                                            </a>
                                            <?php if($user->email_verified_at): ?>
                                                <button type="button" onclick="suspendUser(<?php echo e($user->id); ?>)" class="block w-full text-left px-4 py-2 text-sm text-red-700 hover:bg-red-50 hover:text-red-800 rounded-lg mx-2 transition-colors duration-150">
                                                    <svg class="inline w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728"></path>
                                                    </svg>
                                                    Suspend User
                                                </button>
                                            <?php else: ?>
                                                <form method="POST" action="<?php echo e(route('admin.users.activate', $user)); ?>" class="block">
                                                    <?php echo csrf_field(); ?>
                                                    <button type="submit" class="block w-full text-left px-4 py-2 text-sm text-green-700 hover:bg-green-50 hover:text-green-800 rounded-lg mx-2 transition-colors duration-150">
                                                        <svg class="inline w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                        </svg>
                                                        Activate User
                                                    </button>
                                                </form>
                                            <?php endif; ?>
                                            <button type="button" onclick="sendMessage(<?php echo e($user->id); ?>)" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-gray-900 rounded-lg mx-2 transition-colors duration-150">
                                                <svg class="inline w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                                </svg>
                                                Send Message
                                            </button>
                                        </div>
                                    </div>

                                    <!-- Quick Status Toggle -->
                                    <?php if($user->email_verified_at): ?>
                                        <button type="button" onclick="suspendUser(<?php echo e($user->id); ?>)" class="btn-modern btn-sm-modern btn-danger-modern interactive-element" title="Suspend User">
                                            <svg class="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728"></path>
                                            </svg>
                                        </button>
                                    <?php else: ?>
                                        <form method="POST" action="<?php echo e(route('admin.users.activate', $user)); ?>" class="inline">
                                            <?php echo csrf_field(); ?>
                                            <button type="submit" class="btn-modern btn-sm-modern btn-success-modern interactive-element" title="Activate User">
                                                <svg class="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                </svg>
                                            </button>
                                        </form>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="7" class="px-6 py-12 text-center">
                                <div class="text-gray-500">
                                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M15 19.128a9.38 9.38 0 002.625.372 9.337 9.337 0 004.121-.952 4.125 4.125 0 00-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 018.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0111.964-3.07M12 6.375a3.375 3.375 0 11-6.75 0 3.375 3.375 0 016.75 0zm8.25 2.25a2.625 2.625 0 11-5.25 0 2.625 2.625 0 015.25 0z" />
                                    </svg>
                                    <h3 class="mt-2 text-sm font-medium text-gray-900">No users found</h3>
                                    <p class="mt-1 text-sm text-gray-500">Try adjusting your search or filter criteria.</p>
                                </div>
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <?php if($users->hasPages()): ?>
            <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
                <?php echo e($users->links()); ?>

            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Modern Suspend User Modal -->
<div id="suspendModal" class="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-6 w-96 scale-in-modern">
        <div class="card-modern">
            <div class="card-header-modern">
                <h3 class="text-xl font-bold text-gray-900 flex items-center">
                    <svg class="w-6 h-6 mr-2 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                    Suspend User
                </h3>
            </div>
            <div class="card-body-modern">
                <form id="suspendForm" method="POST">
                    <?php echo csrf_field(); ?>
                    <div class="mb-6">
                        <label for="reason" class="block text-sm font-semibold text-gray-700 mb-2">Suspension Reason</label>
                        <textarea name="reason" id="reason" rows="3" required class="form-textarea-modern" placeholder="Enter detailed reason for suspension..."></textarea>
                    </div>
                    <div class="mb-6">
                        <label for="duration" class="block text-sm font-semibold text-gray-700 mb-2">Duration (days)</label>
                        <input type="number" name="duration" id="duration" min="1" max="365" class="form-input-modern" placeholder="Leave empty for permanent suspension">
                        <p class="text-xs text-gray-500 mt-1">Maximum 365 days. Leave empty for permanent suspension.</p>
                    </div>
                    <div class="flex justify-end space-x-3">
                        <button type="button" onclick="closeSuspendModal()" class="btn-modern btn-secondary-modern interactive-element">
                            Cancel
                        </button>
                        <button type="submit" class="btn-modern btn-danger-modern interactive-element">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728"></path>
                            </svg>
                            Suspend User
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modern Create User Modal -->
    <div id="createUserModal" class="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-10 mx-auto p-6 w-96 scale-in-modern">
            <div class="card-modern">
                <div class="card-header-modern">
                    <h3 class="text-xl font-bold text-gray-900 flex items-center">
                        <svg class="w-6 h-6 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"></path>
                        </svg>
                        Create New User
                    </h3>
                </div>
                <div class="card-body-modern">
                    <form id="createUserForm" method="POST" action="<?php echo e(route('admin.users.store')); ?>">
                        <?php echo csrf_field(); ?>
                        <div class="mb-4">
                            <label for="name" class="block text-sm font-semibold text-gray-700 mb-2">Full Name</label>
                            <input type="text" name="name" id="name" required class="form-input-modern" placeholder="Enter user's full name">
                        </div>
                        <div class="mb-4">
                            <label for="email" class="block text-sm font-semibold text-gray-700 mb-2">Email Address</label>
                            <input type="email" name="email" id="email" required class="form-input-modern" placeholder="<EMAIL>">
                        </div>
                        <div class="mb-4">
                            <label for="role" class="block text-sm font-semibold text-gray-700 mb-2">User Role</label>
                            <select name="role" id="role" required class="form-select-modern">
                                <option value="">Select Role</option>
                                <option value="job_seeker">Job Seeker</option>
                                <option value="employer">Employer</option>
                                <option value="admin">Administrator</option>
                            </select>
                        </div>
                        <div class="mb-6">
                            <label for="password" class="block text-sm font-semibold text-gray-700 mb-2">Password</label>
                            <input type="password" name="password" id="password" required class="form-input-modern" placeholder="Minimum 8 characters">
                            <p class="text-xs text-gray-500 mt-1">Password must be at least 8 characters long.</p>
                        </div>
                        <div class="flex justify-end space-x-3">
                            <button type="button" onclick="closeCreateUserModal()" class="btn-modern btn-secondary-modern interactive-element">
                                Cancel
                            </button>
                            <button type="submit" class="btn-modern btn-primary-modern interactive-element">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                                Create User
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Modern Send Message Modal -->
    <div id="messageModal" class="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-10 mx-auto p-6 w-96 scale-in-modern">
            <div class="card-modern">
                <div class="card-header-modern">
                    <h3 class="text-xl font-bold text-gray-900 flex items-center">
                        <svg class="w-6 h-6 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                        </svg>
                        Send Message
                    </h3>
                </div>
                <div class="card-body-modern">
                    <form id="messageForm" method="POST">
                        <?php echo csrf_field(); ?>
                        <div class="mb-4">
                            <label for="subject" class="block text-sm font-semibold text-gray-700 mb-2">Subject</label>
                            <input type="text" name="subject" id="subject" required class="form-input-modern" placeholder="Enter message subject">
                        </div>
                        <div class="mb-6">
                            <label for="message" class="block text-sm font-semibold text-gray-700 mb-2">Message</label>
                            <textarea name="message" id="message" rows="4" required class="form-textarea-modern" placeholder="Type your message here..."></textarea>
                        </div>
                        <div class="flex justify-end space-x-3">
                            <button type="button" onclick="closeMessageModal()" class="btn-modern btn-secondary-modern interactive-element">
                                Cancel
                            </button>
                            <button type="submit" class="btn-modern btn-success-modern interactive-element">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                                </svg>
                                Send Message
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
// Global variables
let selectedUsers = [];

// Initialize page functionality
document.addEventListener('DOMContentLoaded', function() {
    initializeCheckboxes();
    initializeBulkActions();
    initializeFilters();
    initializeModernFeatures();
});

// Modern features initialization
function initializeModernFeatures() {
    // Add loading states to action buttons
    document.querySelectorAll('.btn-modern').forEach(button => {
        button.addEventListener('click', function() {
            if (this.type === 'submit' || this.tagName === 'A') {
                this.classList.add('loading-modern');

                // Add spinner for submit buttons
                if (this.type === 'submit') {
                    const spinner = document.createElement('div');
                    spinner.className = 'inline-block w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2';
                    this.insertBefore(spinner, this.firstChild);
                    this.disabled = true;
                }
            }
        });
    });

    // Enhanced dropdown animations
    document.querySelectorAll('[id$="Menu"], [id$="Dropdown"]').forEach(dropdown => {
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                    if (!dropdown.classList.contains('hidden')) {
                        dropdown.style.animation = 'scaleInModern 0.2s ease-out';
                    }
                }
            });
        });
        observer.observe(dropdown, { attributes: true });
    });

    // Auto-save search filters
    const searchInput = document.getElementById('search');
    if (searchInput) {
        let searchTimeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                // Auto-submit after 1 second of no typing
                this.form.submit();
            }, 1000);
        });
    }

    // Enhanced table row interactions
    document.querySelectorAll('tbody tr').forEach(row => {
        row.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.001)';
        });

        row.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1)';
        });
    });

    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + N for new user
        if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
            e.preventDefault();
            openCreateUserModal();
        }

        // Ctrl/Cmd + R for refresh
        if ((e.ctrlKey || e.metaKey) && e.key === 'r') {
            e.preventDefault();
            refreshData();
        }
    });
}

// Checkbox functionality
function initializeCheckboxes() {
    const selectAllCheckbox = document.getElementById('selectAll');
    const userCheckboxes = document.querySelectorAll('.user-checkbox');

    selectAllCheckbox.addEventListener('change', function() {
        userCheckboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateSelectedUsers();
    });

    userCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateSelectedUsers();
            updateSelectAllState();
        });
    });
}

function updateSelectedUsers() {
    selectedUsers = Array.from(document.querySelectorAll('.user-checkbox:checked')).map(cb => cb.value);
    updateBulkActionsVisibility();
}

function updateSelectAllState() {
    const selectAllCheckbox = document.getElementById('selectAll');
    const userCheckboxes = document.querySelectorAll('.user-checkbox');
    const checkedCheckboxes = document.querySelectorAll('.user-checkbox:checked');

    if (checkedCheckboxes.length === 0) {
        selectAllCheckbox.indeterminate = false;
        selectAllCheckbox.checked = false;
    } else if (checkedCheckboxes.length === userCheckboxes.length) {
        selectAllCheckbox.indeterminate = false;
        selectAllCheckbox.checked = true;
    } else {
        selectAllCheckbox.indeterminate = true;
    }
}

// Bulk actions functionality
function initializeBulkActions() {
    const bulkActionsBtn = document.getElementById('bulkActionsBtn');
    const bulkActionsMenu = document.getElementById('bulkActionsMenu');

    bulkActionsBtn.addEventListener('click', function() {
        bulkActionsMenu.classList.toggle('hidden');
    });

    // Close menu when clicking outside
    document.addEventListener('click', function(e) {
        if (!bulkActionsBtn.contains(e.target) && !bulkActionsMenu.contains(e.target)) {
            bulkActionsMenu.classList.add('hidden');
        }
    });
}

function updateBulkActionsVisibility() {
    const bulkActionsBtn = document.getElementById('bulkActionsBtn');
    if (selectedUsers.length > 0) {
        bulkActionsBtn.classList.remove('opacity-50', 'cursor-not-allowed');
        bulkActionsBtn.disabled = false;
    } else {
        bulkActionsBtn.classList.add('opacity-50', 'cursor-not-allowed');
        bulkActionsBtn.disabled = true;
    }
}

// Bulk action functions
function bulkExport() {
    if (selectedUsers.length === 0) {
        alert('Please select users to export.');
        return;
    }

    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '<?php echo e(route("admin.users.bulk-export")); ?>';

    const csrfToken = document.createElement('input');
    csrfToken.type = 'hidden';
    csrfToken.name = '_token';
    csrfToken.value = '<?php echo e(csrf_token()); ?>';
    form.appendChild(csrfToken);

    selectedUsers.forEach(userId => {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'user_ids[]';
        input.value = userId;
        form.appendChild(input);
    });

    document.body.appendChild(form);
    form.submit();
    document.body.removeChild(form);
}

function bulkActivate() {
    if (selectedUsers.length === 0) {
        alert('Please select users to activate.');
        return;
    }

    if (confirm(`Are you sure you want to activate ${selectedUsers.length} users?`)) {
        performBulkAction('activate');
    }
}

function bulkSuspend() {
    if (selectedUsers.length === 0) {
        alert('Please select users to suspend.');
        return;
    }

    if (confirm(`Are you sure you want to suspend ${selectedUsers.length} users?`)) {
        performBulkAction('suspend');
    }
}

function performBulkAction(action) {
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = `<?php echo e(route("admin.users.bulk-action")); ?>`;

    const csrfToken = document.createElement('input');
    csrfToken.type = 'hidden';
    csrfToken.name = '_token';
    csrfToken.value = '<?php echo e(csrf_token()); ?>';
    form.appendChild(csrfToken);

    const actionInput = document.createElement('input');
    actionInput.type = 'hidden';
    actionInput.name = 'action';
    actionInput.value = action;
    form.appendChild(actionInput);

    selectedUsers.forEach(userId => {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'user_ids[]';
        input.value = userId;
        form.appendChild(input);
    });

    document.body.appendChild(form);
    form.submit();
    document.body.removeChild(form);
}

// User action dropdowns
function toggleUserActions(userId) {
    const menu = document.getElementById(`userActions${userId}`);

    // Close all other menus
    document.querySelectorAll('[id^="userActions"]').forEach(otherMenu => {
        if (otherMenu !== menu) {
            otherMenu.classList.add('hidden');
        }
    });

    menu.classList.toggle('hidden');
}

// Close dropdowns when clicking outside
document.addEventListener('click', function(e) {
    if (!e.target.closest('[onclick^="toggleUserActions"]')) {
        document.querySelectorAll('[id^="userActions"]').forEach(menu => {
            menu.classList.add('hidden');
        });
    }
});

// Modal functions
function openCreateUserModal() {
    document.getElementById('createUserModal').classList.remove('hidden');
}

function closeCreateUserModal() {
    document.getElementById('createUserModal').classList.add('hidden');
    document.getElementById('createUserForm').reset();
}

function sendMessage(userId) {
    document.getElementById('messageForm').action = `/admin/users/${userId}/message`;
    document.getElementById('messageModal').classList.remove('hidden');
}

function closeMessageModal() {
    document.getElementById('messageModal').classList.add('hidden');
    document.getElementById('messageForm').reset();
}

function suspendUser(userId) {
    document.getElementById('suspendForm').action = `/admin/users/${userId}/suspend`;
    document.getElementById('suspendModal').classList.remove('hidden');
}

function closeSuspendModal() {
    document.getElementById('suspendModal').classList.add('hidden');
    document.getElementById('suspendForm').reset();
}

// Utility functions
function refreshData() {
    window.location.reload();
}

// Filter functionality
function initializeFilters() {
    const searchInput = document.getElementById('search');
    const roleSelect = document.getElementById('role');
    const statusSelect = document.getElementById('status');

    // Auto-submit form on filter changes
    [roleSelect, statusSelect].forEach(element => {
        element.addEventListener('change', function() {
            this.form.submit();
        });
    });

    // Search with debounce
    let searchTimeout;
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            this.form.submit();
        }, 500);
    });
}

// Real-time updates (optional)
function startRealTimeUpdates() {
    setInterval(() => {
        // Check for updates without full page reload
        fetch(window.location.href, {
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        }).then(response => {
            if (response.ok) {
                // Update status indicators if needed
                console.log('Status updated');
            }
        });
    }, 30000); // Check every 30 seconds
}

// Initialize real-time updates
// startRealTimeUpdates();
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\laravel\rectra\resources\views/admin/users/index.blade.php ENDPATH**/ ?>