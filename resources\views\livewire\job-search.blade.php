<div class="bg-white">
    <!-- Search Filters -->
    <div class="bg-gray-50 py-8">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="bg-white rounded-lg shadow-sm p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                    <!-- Search Input -->
                    <div>
                        <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Job Title or Keywords</label>
                        <input type="text" id="search" wire:model.live.debounce.300ms="search" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                               placeholder="e.g. Software Engineer">
                    </div>

                    <!-- Location Input -->
                    <div>
                        <label for="location" class="block text-sm font-medium text-gray-700 mb-1">Location</label>
                        <input type="text" id="location" wire:model.live.debounce.300ms="location" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                               placeholder="e.g. New York, NY">
                    </div>

                    <!-- Job Type Select -->
                    <div>
                        <label for="jobType" class="block text-sm font-medium text-gray-700 mb-1">Job Type</label>
                        <select id="jobType" wire:model.live="jobType" 
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="">All Types</option>
                            @foreach($jobTypes as $type)
                                <option value="{{ $type }}">{{ ucfirst(str_replace('-', ' ', $type)) }}</option>
                            @endforeach
                        </select>
                    </div>

                    <!-- Minimum Salary -->
                    <div>
                        <label for="minSalary" class="block text-sm font-medium text-gray-700 mb-1">Minimum Salary</label>
                        <input type="number" id="minSalary" wire:model.live.debounce.300ms="minSalary" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                               placeholder="e.g. 50000">
                    </div>
                </div>

                <!-- Checkboxes and Actions -->
                <div class="flex flex-wrap items-center justify-between gap-4">
                    <div class="flex flex-wrap gap-4">
                        <label class="flex items-center">
                            <input type="checkbox" wire:model.live="remote" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            <span class="ml-2 text-sm text-gray-700">Remote Only</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" wire:model.live="featured" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            <span class="ml-2 text-sm text-gray-700">Featured Jobs</span>
                        </label>
                    </div>

                    <div class="flex gap-2">
                        <button wire:click="clearFilters" 
                                class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            Clear Filters
                        </button>
                        @auth
                            <button wire:click="saveSearch" 
                                    class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                Save Search
                            </button>
                        @endauth
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Results Section -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Results Header -->
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
            <div>
                <h2 class="text-2xl font-bold text-gray-900">Job Results</h2>
                <p class="text-sm text-gray-600 mt-1">{{ $jobs->total() }} jobs found</p>
            </div>
            
            <!-- Sort Options -->
            <div class="mt-4 sm:mt-0">
                <div class="flex items-center space-x-2">
                    <span class="text-sm text-gray-700">Sort by:</span>
                    <button wire:click="sortBy('created_at')" 
                            class="text-sm {{ $sortBy === 'created_at' ? 'text-blue-600 font-medium' : 'text-gray-500' }} hover:text-blue-600">
                        Date Posted
                        @if($sortBy === 'created_at')
                            <span class="ml-1">{{ $sortDirection === 'asc' ? '↑' : '↓' }}</span>
                        @endif
                    </button>
                    <span class="text-gray-300">|</span>
                    <button wire:click="sortBy('title')" 
                            class="text-sm {{ $sortBy === 'title' ? 'text-blue-600 font-medium' : 'text-gray-500' }} hover:text-blue-600">
                        Title
                        @if($sortBy === 'title')
                            <span class="ml-1">{{ $sortDirection === 'asc' ? '↑' : '↓' }}</span>
                        @endif
                    </button>
                    <span class="text-gray-300">|</span>
                    <button wire:click="sortBy('salary_min')" 
                            class="text-sm {{ $sortBy === 'salary_min' ? 'text-blue-600 font-medium' : 'text-gray-500' }} hover:text-blue-600">
                        Salary
                        @if($sortBy === 'salary_min')
                            <span class="ml-1">{{ $sortDirection === 'asc' ? '↑' : '↓' }}</span>
                        @endif
                    </button>
                </div>
            </div>
        </div>

        <!-- Flash Messages -->
        @if (session()->has('success'))
            <div class="mb-4 p-4 bg-green-100 border border-green-400 text-green-700 rounded-md">
                {{ session('success') }}
            </div>
        @endif

        @if (session()->has('error'))
            <div class="mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded-md">
                {{ session('error') }}
            </div>
        @endif

        <!-- Job Listings -->
        <div class="space-y-4">
            @forelse($jobs as $job)
                <div class="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow duration-200">
                    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                        <div class="flex-1">
                            <div class="flex items-start justify-between">
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-900">
                                        <a href="{{ route('jobs.show', $job) }}" class="hover:text-blue-600">
                                            {{ $job->title }}
                                            @if($job->featured)
                                                <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                                    Featured
                                                </span>
                                            @endif
                                        </a>
                                    </h3>
                                    <p class="text-sm text-gray-600 mt-1">
                                        {{ $job->employer->company_name ?? $job->employer->user->name }}
                                    </p>
                                    <div class="flex flex-wrap items-center gap-4 mt-2 text-sm text-gray-500">
                                        <span class="flex items-center">
                                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            </svg>
                                            {{ $job->location }}
                                        </span>
                                        <span class="flex items-center">
                                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m8 0V6a2 2 0 00-2-2H10a2 2 0 00-2 2v0m8 0h2a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V8a2 2 0 012-2h2"></path>
                                            </svg>
                                            {{ ucfirst(str_replace('-', ' ', $job->type)) }}
                                        </span>
                                        @if($job->remote)
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                Remote
                                            </span>
                                        @endif
                                        <span class="flex items-center">
                                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                            {{ $job->created_at->diffForHumans() }}
                                        </span>
                                    </div>
                                </div>
                                @if($job->salary_min || $job->salary_max)
                                    <div class="text-right">
                                        <p class="text-lg font-semibold text-gray-900">
                                            ${{ number_format($job->salary_min) }}
                                            @if($job->salary_max && $job->salary_max != $job->salary_min)
                                                - ${{ number_format($job->salary_max) }}
                                            @endif
                                        </p>
                                        <p class="text-sm text-gray-500">per year</p>
                                    </div>
                                @endif
                            </div>
                            
                            @if($job->description)
                                <p class="text-gray-700 mt-3 line-clamp-2">
                                    {{ Str::limit(strip_tags($job->description), 150) }}
                                </p>
                            @endif
                        </div>
                        
                        <div class="mt-4 lg:mt-0 lg:ml-6 flex-shrink-0">
                            <a href="{{ route('jobs.show', $job) }}" 
                               class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                View Details
                            </a>
                        </div>
                    </div>
                </div>
            @empty
                <div class="text-center py-12">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">No jobs found</h3>
                    <p class="mt-1 text-sm text-gray-500">Try adjusting your search criteria or clearing filters.</p>
                </div>
            @endforelse
        </div>

        <!-- Pagination -->
        @if($jobs->hasPages())
            <div class="mt-8">
                {{ $jobs->links() }}
            </div>
        @endif
    </div>
</div>
