<?php

namespace App\Http\Controllers;

use App\Models\Course;
use App\Models\CourseEnrollment;
use App\Models\Lesson;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class CourseEnrollmentController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('role:job_seeker');
    }

    /**
     * Display a listing of the user's course enrollments.
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        
        $query = $user->courseEnrollments()
            ->with(['course.instructor', 'course.media'])
            ->latest();

        // Filter by enrollment status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by progress
        if ($request->filled('progress')) {
            switch ($request->progress) {
                case 'not_started':
                    $query->where('progress_percentage', 0);
                    break;
                case 'in_progress':
                    $query->where('progress_percentage', '>', 0)
                          ->where('progress_percentage', '<', 100);
                    break;
                case 'completed':
                    $query->where('progress_percentage', 100);
                    break;
            }
        }

        // Filter by course category
        if ($request->filled('category')) {
            $query->whereHas('course', function ($q) use ($request) {
                $q->where('category', $request->category);
            });
        }

        // Search by course title or instructor
        if ($request->filled('search')) {
            $searchTerm = $request->search;
            $query->whereHas('course', function ($q) use ($searchTerm) {
                $q->where('title', 'like', "%{$searchTerm}%")
                  ->orWhere('description', 'like', "%{$searchTerm}%")
                  ->orWhereHas('instructor', function ($iq) use ($searchTerm) {
                      $iq->where('name', 'like', "%{$searchTerm}%");
                  });
            });
        }

        // Sort options
        $sortBy = $request->get('sort', 'enrolled_at');
        $sortOrder = $request->get('order', 'desc');

        switch ($sortBy) {
            case 'title':
                $query->join('courses', 'course_enrollments.course_id', '=', 'courses.id')
                      ->orderBy('courses.title', $sortOrder)
                      ->select('course_enrollments.*');
                break;
            case 'progress':
                $query->orderBy('progress_percentage', $sortOrder);
                break;
            case 'last_accessed':
                $query->orderBy('last_accessed_at', $sortOrder);
                break;
            default: // enrolled_at
                $query->orderBy('enrolled_at', $sortOrder);
        }

        $enrollments = $query->paginate(12)->withQueryString();

        // Get filter options
        $categories = $user->courseEnrollments()
            ->join('courses', 'course_enrollments.course_id', '=', 'courses.id')
            ->select('courses.category')
            ->distinct()
            ->whereNotNull('courses.category')
            ->pluck('courses.category')
            ->sort();

        $statuses = ['active', 'completed', 'paused', 'cancelled'];

        // Get statistics
        $stats = [
            'total' => $user->courseEnrollments()->count(),
            'active' => $user->courseEnrollments()->where('status', 'active')->count(),
            'completed' => $user->courseEnrollments()->where('status', 'completed')->count(),
            'in_progress' => $user->courseEnrollments()
                ->where('progress_percentage', '>', 0)
                ->where('progress_percentage', '<', 100)
                ->count(),
            'certificates' => $user->courseEnrollments()
                ->where('certificate_issued', true)
                ->count(),
        ];

        return view('course-enrollments.index', compact(
            'enrollments',
            'categories',
            'statuses',
            'stats'
        ));
    }

    /**
     * Enroll the user in a course.
     */
    public function store(Request $request, Course $course)
    {
        $user = Auth::user();

        // Check if course is published
        if (!$course->is_published) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'This course is not available for enrollment.'
                ], 400);
            }
            
            return back()->with('error', 'This course is not available for enrollment.');
        }

        // Check if user is already enrolled
        $existingEnrollment = $user->courseEnrollments()
            ->where('course_id', $course->id)
            ->first();

        if ($existingEnrollment) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'You are already enrolled in this course.',
                    'enrollment_id' => $existingEnrollment->id,
                    'redirect_url' => route('courses.learn', $course)
                ]);
            }
            
            return redirect()->route('courses.learn', $course)
                ->with('info', 'You are already enrolled in this course.');
        }

        // Create enrollment
        $enrollment = CourseEnrollment::create([
            'user_id' => $user->id,
            'course_id' => $course->id,
            'enrolled_at' => now(),
            'status' => 'active',
            'progress_percentage' => 0,
        ]);

        // Increment course enrollment count
        $course->increment('students_count');

        if ($request->ajax()) {
            return response()->json([
                'success' => true,
                'message' => 'Successfully enrolled in the course!',
                'enrollment_id' => $enrollment->id,
                'redirect_url' => route('courses.learn', $course)
            ]);
        }

        return redirect()->route('courses.learn', $course)
            ->with('success', 'Successfully enrolled in the course!');
    }

    /**
     * Display a specific enrollment.
     */
    public function show(CourseEnrollment $enrollment)
    {
        $this->authorize('view', $enrollment);
        
        $enrollment->load([
            'course.lessons' => function ($query) {
                $query->orderBy('order');
            },
            'course.instructor',
            'course.media',
            'completedLessons'
        ]);

        // Calculate detailed progress
        $totalLessons = $enrollment->course->lessons->count();
        $completedLessons = $enrollment->completedLessons->count();
        $progressPercentage = $totalLessons > 0 ? round(($completedLessons / $totalLessons) * 100, 2) : 0;

        // Get next lesson to study
        $nextLesson = $enrollment->course->lessons
            ->whereNotIn('id', $enrollment->completedLessons->pluck('id'))
            ->first();

        // Get recent activity
        $recentActivity = $enrollment->completedLessons()
            ->with('lesson')
            ->latest('pivot.completed_at')
            ->limit(5)
            ->get();

        return view('course-enrollments.show', compact(
            'enrollment',
            'totalLessons',
            'completedLessons',
            'progressPercentage',
            'nextLesson',
            'recentActivity'
        ));
    }

    /**
     * Update enrollment progress.
     */
    public function updateProgress(Request $request, CourseEnrollment $enrollment)
    {
        $this->authorize('update', $enrollment);
        
        $request->validate([
            'lesson_id' => 'required|exists:lessons,id',
            'completed' => 'boolean',
        ]);

        $lesson = Lesson::findOrFail($request->lesson_id);
        
        // Verify lesson belongs to the course
        if ($lesson->course_id !== $enrollment->course_id) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid lesson for this course.'
            ], 400);
        }

        if ($request->boolean('completed')) {
            // Mark lesson as completed
            $enrollment->completedLessons()->syncWithoutDetaching([
                $lesson->id => ['completed_at' => now()]
            ]);
        } else {
            // Mark lesson as not completed
            $enrollment->completedLessons()->detach($lesson->id);
        }

        // Update overall progress
        $totalLessons = $enrollment->course->lessons()->count();
        $completedLessons = $enrollment->completedLessons()->count();
        $progressPercentage = $totalLessons > 0 ? round(($completedLessons / $totalLessons) * 100, 2) : 0;

        $enrollment->update([
            'progress_percentage' => $progressPercentage,
            'last_accessed_at' => now(),
        ]);

        // Check if course is completed
        if ($progressPercentage >= 100) {
            $enrollment->update([
                'status' => 'completed',
                'completed_at' => now(),
            ]);

            // Issue certificate if course offers one
            if ($enrollment->course->certificate_available && !$enrollment->certificate_issued) {
                $enrollment->update([
                    'certificate_issued' => true,
                    'certificate_issued_at' => now(),
                ]);
            }
        }

        return response()->json([
            'success' => true,
            'progress_percentage' => $progressPercentage,
            'completed_lessons' => $completedLessons,
            'total_lessons' => $totalLessons,
            'course_completed' => $progressPercentage >= 100,
            'certificate_issued' => $enrollment->certificate_issued,
        ]);
    }

    /**
     * Pause an enrollment.
     */
    public function pause(CourseEnrollment $enrollment)
    {
        $this->authorize('update', $enrollment);
        
        if ($enrollment->status === 'completed') {
            return back()->with('error', 'Cannot pause a completed course.');
        }

        $enrollment->update(['status' => 'paused']);

        return back()->with('success', 'Course enrollment paused.');
    }

    /**
     * Resume a paused enrollment.
     */
    public function resume(CourseEnrollment $enrollment)
    {
        $this->authorize('update', $enrollment);
        
        if ($enrollment->status !== 'paused') {
            return back()->with('error', 'This enrollment is not paused.');
        }

        $enrollment->update(['status' => 'active']);

        return back()->with('success', 'Course enrollment resumed.');
    }

    /**
     * Cancel an enrollment.
     */
    public function cancel(CourseEnrollment $enrollment)
    {
        $this->authorize('delete', $enrollment);
        
        if ($enrollment->status === 'completed') {
            return back()->with('error', 'Cannot cancel a completed course.');
        }

        $enrollment->update(['status' => 'cancelled']);

        // Decrement course enrollment count
        $enrollment->course->decrement('students_count');

        return back()->with('success', 'Course enrollment cancelled.');
    }

    /**
     * Download course certificate.
     */
    public function downloadCertificate(CourseEnrollment $enrollment)
    {
        $this->authorize('view', $enrollment);
        
        if (!$enrollment->certificate_issued) {
            return back()->with('error', 'Certificate not available for this course.');
        }

        // Generate and download certificate
        // This would typically use a PDF generation library
        return $this->generateCertificatePdf($enrollment);
    }

    /**
     * Generate certificate PDF.
     */
    private function generateCertificatePdf(CourseEnrollment $enrollment)
    {
        // Placeholder for certificate generation
        // You would implement this using a library like DomPDF or TCPDF
        
        $certificateData = [
            'student_name' => $enrollment->user->name,
            'course_title' => $enrollment->course->title,
            'instructor_name' => $enrollment->course->instructor->name,
            'completion_date' => $enrollment->completed_at->format('F j, Y'),
            'certificate_id' => 'CERT-' . $enrollment->id . '-' . $enrollment->completed_at->format('Ymd'),
        ];

        // For now, return a simple response
        return response()->json([
            'message' => 'Certificate generation feature coming soon.',
            'data' => $certificateData
        ]);
    }

    /**
     * Get enrollment statistics for dashboard.
     */
    public function getStats()
    {
        $user = Auth::user();
        
        $stats = [
            'total_enrollments' => $user->courseEnrollments()->count(),
            'active_enrollments' => $user->courseEnrollments()->where('status', 'active')->count(),
            'completed_courses' => $user->courseEnrollments()->where('status', 'completed')->count(),
            'certificates_earned' => $user->courseEnrollments()->where('certificate_issued', true)->count(),
            'total_study_time' => $user->courseEnrollments()
                ->join('courses', 'course_enrollments.course_id', '=', 'courses.id')
                ->sum('courses.duration_hours'),
            'average_progress' => $user->courseEnrollments()->avg('progress_percentage'),
        ];

        return response()->json($stats);
    }

    /**
     * Get recent learning activity.
     */
    public function getRecentActivity()
    {
        $user = Auth::user();
        
        $recentEnrollments = $user->courseEnrollments()
            ->with(['course', 'completedLessons.lesson'])
            ->where('last_accessed_at', '>=', now()->subDays(7))
            ->orderBy('last_accessed_at', 'desc')
            ->limit(5)
            ->get();

        return response()->json($recentEnrollments);
    }

    /**
     * Export enrollment data.
     */
    public function export(Request $request)
    {
        $user = Auth::user();
        $format = $request->get('format', 'csv');

        $enrollments = $user->courseEnrollments()
            ->with(['course.instructor'])
            ->get();

        switch ($format) {
            case 'csv':
                return $this->exportToCsv($enrollments);
            case 'pdf':
                return $this->exportToPdf($enrollments);
            default:
                return back()->with('error', 'Invalid export format.');
        }
    }

    /**
     * Export enrollments to CSV.
     */
    private function exportToCsv($enrollments)
    {
        $filename = 'course-enrollments-' . now()->format('Y-m-d') . '.csv';
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename={$filename}",
        ];

        $callback = function () use ($enrollments) {
            $file = fopen('php://output', 'w');
            fputcsv($file, [
                'Course Title', 'Instructor', 'Enrolled Date', 'Status', 
                'Progress %', 'Completed Date', 'Certificate Issued'
            ]);

            foreach ($enrollments as $enrollment) {
                fputcsv($file, [
                    $enrollment->course->title,
                    $enrollment->course->instructor->name,
                    $enrollment->enrolled_at->format('Y-m-d'),
                    $enrollment->status,
                    $enrollment->progress_percentage . '%',
                    $enrollment->completed_at ? $enrollment->completed_at->format('Y-m-d') : 'N/A',
                    $enrollment->certificate_issued ? 'Yes' : 'No'
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Export enrollments to PDF.
     */
    private function exportToPdf($enrollments)
    {
        // Placeholder for PDF export
        return back()->with('info', 'PDF export feature coming soon.');
    }
}