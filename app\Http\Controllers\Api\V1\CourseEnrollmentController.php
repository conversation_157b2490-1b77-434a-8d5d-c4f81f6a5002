<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Api\Controller;
use App\Models\Course;
use App\Models\CourseEnrollment;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class CourseEnrollmentController extends Controller
{
    /**
     * Display a listing of the user's course enrollments.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $user = $request->user();
        
        // Ensure user is a job seeker
        if (!$user->isJobSeeker()) {
            return $this->error('Access denied. Only job seekers can view course enrollments.', 403);
        }

        $jobSeekerProfile = $user->jobSeekerProfile;
        
        if (!$jobSeekerProfile) {
            return $this->error('Job seeker profile not found', 404);
        }

        $query = CourseEnrollment::with(['course.instructor'])
            ->where('job_seeker_profile_id', $jobSeekerProfile->id);

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by completion status
        if ($request->filled('completed')) {
            $completed = filter_var($request->completed, FILTER_VALIDATE_BOOLEAN);
            if ($completed) {
                $query->where('completion_percentage', 100);
            } else {
                $query->where('completion_percentage', '<', 100);
            }
        }

        // Search by course title
        if ($request->filled('search')) {
            $search = $request->search;
            $query->whereHas('course', function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Sort
        $sortBy = $request->input('sort_by', 'enrolled_at');
        $sortOrder = $request->input('sort_order', 'desc');
        
        if (in_array($sortBy, ['enrolled_at', 'last_accessed_at', 'completion_percentage'])) {
            $query->orderBy($sortBy, $sortOrder);
        } else {
            $query->orderBy('enrolled_at', 'desc');
        }

        $enrollments = $query->paginate($request->input('per_page', 15));

        return $this->success([
            'enrollments' => $enrollments->items(),
            'pagination' => [
                'current_page' => $enrollments->currentPage(),
                'last_page' => $enrollments->lastPage(),
                'per_page' => $enrollments->perPage(),
                'total' => $enrollments->total(),
                'has_more_pages' => $enrollments->hasMorePages()
            ]
        ]);
    }

    /**
     * Enroll in a course.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Course  $course
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request, Course $course): JsonResponse
    {
        $user = $request->user();
        
        // Ensure user is a job seeker
        if (!$user->isJobSeeker()) {
            return $this->error('Access denied. Only job seekers can enroll in courses.', 403);
        }

        $jobSeekerProfile = $user->jobSeekerProfile;
        
        if (!$jobSeekerProfile) {
            return $this->error('Job seeker profile not found', 404);
        }

        // Check if course is active
        if ($course->status !== 'active') {
            return $this->error('Course is not available for enrollment', 422);
        }

        // Check if user is already enrolled
        $existingEnrollment = CourseEnrollment::where('job_seeker_profile_id', $jobSeekerProfile->id)
            ->where('course_id', $course->id)
            ->first();

        if ($existingEnrollment) {
            return $this->error('You are already enrolled in this course', 422);
        }

        // For premium courses, check if user has active subscription
        if ($course->is_premium) {
            $hasActiveSubscription = $user->subscriptions()
                ->where('status', 'active')
                ->where('ends_at', '>', now())
                ->exists();

            if (!$hasActiveSubscription) {
                return $this->error('Premium subscription required to enroll in this course', 422);
            }
        }

        // Create enrollment
        $enrollment = CourseEnrollment::create([
            'job_seeker_profile_id' => $jobSeekerProfile->id,
            'course_id' => $course->id,
            'status' => 'active',
            'enrolled_at' => now(),
            'completion_percentage' => 0,
        ]);

        // Increment course enrollment count
        $course->increment('enrollment_count');

        // Load relationships for response
        $enrollment->load(['course.instructor']);

        return $this->success([
            'enrollment' => $enrollment
        ], 'Successfully enrolled in course', 201);
    }

    /**
     * Update course progress.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\CourseEnrollment  $enrollment
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, CourseEnrollment $enrollment): JsonResponse
    {
        $user = $request->user();
        
        // Ensure user owns this enrollment
        if ($enrollment->jobSeekerProfile->user_id !== $user->id) {
            return $this->error('Access denied.', 403);
        }

        $validator = Validator::make($request->all(), [
            'completion_percentage' => ['sometimes', 'integer', 'min:0', 'max:100'],
            'status' => ['sometimes', 'in:active,paused,completed,dropped'],
        ]);

        if ($validator->fails()) {
            return $this->error('Validation error', 422, $validator->errors());
        }

        $updateData = [];

        if ($request->filled('completion_percentage')) {
            $updateData['completion_percentage'] = $request->completion_percentage;
            
            // If completion is 100%, mark as completed
            if ($request->completion_percentage == 100) {
                $updateData['status'] = 'completed';
                $updateData['completed_at'] = now();
            }
        }

        if ($request->filled('status')) {
            $updateData['status'] = $request->status;
            
            if ($request->status === 'completed') {
                $updateData['completed_at'] = now();
                $updateData['completion_percentage'] = 100;
            }
        }

        // Update last accessed time
        $updateData['last_accessed_at'] = now();

        $enrollment->update($updateData);

        // Load relationships for response
        $enrollment->load(['course.instructor']);

        return $this->success([
            'enrollment' => $enrollment
        ], 'Enrollment updated successfully');
    }

    /**
     * Display the specified enrollment.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\CourseEnrollment  $enrollment
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(Request $request, CourseEnrollment $enrollment): JsonResponse
    {
        $user = $request->user();
        
        // Ensure user owns this enrollment
        if ($enrollment->jobSeekerProfile->user_id !== $user->id) {
            return $this->error('Access denied.', 403);
        }

        // Update last accessed time
        $enrollment->update(['last_accessed_at' => now()]);

        // Load relationships
        $enrollment->load([
            'course.instructor',
            'course.lessons' => function ($query) {
                $query->orderBy('order');
            },
            'lessonProgress'
        ]);

        return $this->success([
            'enrollment' => $enrollment
        ]);
    }

    /**
     * Remove the specified enrollment (drop course).
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\CourseEnrollment  $enrollment
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(Request $request, CourseEnrollment $enrollment): JsonResponse
    {
        $user = $request->user();
        
        // Ensure user owns this enrollment
        if ($enrollment->jobSeekerProfile->user_id !== $user->id) {
            return $this->error('Access denied.', 403);
        }

        // Check if enrollment can be dropped
        if ($enrollment->status === 'completed') {
            return $this->error('Cannot drop a completed course', 422);
        }

        // Update status to dropped instead of deleting
        $enrollment->update([
            'status' => 'dropped',
            'dropped_at' => now()
        ]);

        // Decrement course enrollment count
        $enrollment->course->decrement('enrollment_count');

        return $this->success([
            'message' => 'Course dropped successfully'
        ]);
    }

    /**
     * Get enrollment statistics for the authenticated user.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function stats(Request $request): JsonResponse
    {
        $user = $request->user();
        
        // Ensure user is a job seeker
        if (!$user->isJobSeeker()) {
            return $this->error('Access denied. Only job seekers can view enrollment statistics.', 403);
        }

        $jobSeekerProfile = $user->jobSeekerProfile;
        
        if (!$jobSeekerProfile) {
            return $this->error('Job seeker profile not found', 404);
        }
        
        $stats = [
            'total_enrollments' => CourseEnrollment::where('job_seeker_profile_id', $jobSeekerProfile->id)->count(),
            'active_enrollments' => CourseEnrollment::where('job_seeker_profile_id', $jobSeekerProfile->id)
                ->where('status', 'active')->count(),
            'completed_enrollments' => CourseEnrollment::where('job_seeker_profile_id', $jobSeekerProfile->id)
                ->where('status', 'completed')->count(),
            'paused_enrollments' => CourseEnrollment::where('job_seeker_profile_id', $jobSeekerProfile->id)
                ->where('status', 'paused')->count(),
            'dropped_enrollments' => CourseEnrollment::where('job_seeker_profile_id', $jobSeekerProfile->id)
                ->where('status', 'dropped')->count(),
            'average_completion' => CourseEnrollment::where('job_seeker_profile_id', $jobSeekerProfile->id)
                ->avg('completion_percentage') ?? 0,
            'total_learning_hours' => CourseEnrollment::where('job_seeker_profile_id', $jobSeekerProfile->id)
                ->join('courses', 'course_enrollments.course_id', '=', 'courses.id')
                ->where('course_enrollments.status', 'completed')
                ->sum('courses.duration_hours') ?? 0,
        ];

        return $this->success($stats);
    }

    /**
     * Get recent enrollment activity.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function recentActivity(Request $request): JsonResponse
    {
        $user = $request->user();
        $limit = $request->input('limit', 10);
        
        // Ensure user is a job seeker
        if (!$user->isJobSeeker()) {
            return $this->error('Access denied. Only job seekers can view enrollment activity.', 403);
        }

        $jobSeekerProfile = $user->jobSeekerProfile;
        
        if (!$jobSeekerProfile) {
            return $this->error('Job seeker profile not found', 404);
        }
        
        $recentEnrollments = CourseEnrollment::with(['course.instructor'])
            ->where('job_seeker_profile_id', $jobSeekerProfile->id)
            ->orderBy('last_accessed_at', 'desc')
            ->limit($limit)
            ->get()
            ->map(function ($enrollment) {
                return [
                    'id' => $enrollment->id,
                    'course_title' => $enrollment->course->title,
                    'instructor_name' => $enrollment->course->instructor->name ?? 'Unknown',
                    'status' => $enrollment->status,
                    'completion_percentage' => $enrollment->completion_percentage,
                    'enrolled_at' => $enrollment->enrolled_at,
                    'last_accessed_at' => $enrollment->last_accessed_at,
                ];
            });

        return $this->success([
            'recent_enrollments' => $recentEnrollments
        ]);
    }

    /**
     * Get certificates for completed courses.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function certificates(Request $request): JsonResponse
    {
        $user = $request->user();
        
        // Ensure user is a job seeker
        if (!$user->isJobSeeker()) {
            return $this->error('Access denied. Only job seekers can view certificates.', 403);
        }

        $jobSeekerProfile = $user->jobSeekerProfile;
        
        if (!$jobSeekerProfile) {
            return $this->error('Job seeker profile not found', 404);
        }
        
        $certificates = CourseEnrollment::with(['course.instructor'])
            ->where('job_seeker_profile_id', $jobSeekerProfile->id)
            ->where('status', 'completed')
            ->where('completion_percentage', 100)
            ->orderBy('completed_at', 'desc')
            ->get()
            ->map(function ($enrollment) {
                return [
                    'id' => $enrollment->id,
                    'course_title' => $enrollment->course->title,
                    'instructor_name' => $enrollment->course->instructor->name ?? 'Unknown',
                    'completed_at' => $enrollment->completed_at,
                    'certificate_url' => route('api.v1.enrollments.certificate', $enrollment->id),
                ];
            });

        return $this->success([
            'certificates' => $certificates
        ]);
    }
}