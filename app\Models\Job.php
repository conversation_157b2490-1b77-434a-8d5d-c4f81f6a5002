<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use <PERSON>vel\Scout\Searchable;
use Spatie\MediaLibrary\HasMedia;
use Spa<PERSON>\MediaLibrary\InteractsWithMedia;

class Job extends Model implements HasMedia
{
    use HasFactory, InteractsWithMedia;

    protected $table = 'job_listings';

    protected $fillable = [
        'employer_profile_id',
        'title',
        'description',
        'location',
        'job_type',
        'experience_level',
        'industry',
        'salary_min',
        'salary_max',
        'salary_negotiable',
        'required_skills',
        'preferred_skills',
        'requirements',
        'benefits',
        'is_remote',
        'is_featured',
        'status',
        'expires_at',
        'views_count',
        'applications_count',
    ];

    protected $casts = [
        'salary_min' => 'decimal:2',
        'salary_max' => 'decimal:2',
        'salary_negotiable' => 'boolean',
        'required_skills' => 'array',
        'preferred_skills' => 'array',
        'is_remote' => 'boolean',
        'is_featured' => 'boolean',
        'expires_at' => 'datetime',
    ];

    /**
     * Get the employer profile that owns the job.
     */
    public function employerProfile()
    {
        return $this->belongsTo(EmployerProfile::class);
    }

    /**
     * Get the job applications for the job.
     */
    public function jobApplications()
    {
        return $this->hasMany(JobApplication::class);
    }

    /**
     * Alias for jobApplications() for consistency.
     */
    public function applications()
    {
        return $this->jobApplications();
    }

    /**
     * Get the users who saved this job.
     */
    public function savedByUsers()
    {
        return $this->belongsToMany(User::class, 'saved_jobs');
    }

    /**
     * Scope a query to only include active jobs.
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope a query to only include featured jobs.
     */
    public function scopeFeatured(Builder $query): Builder
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope a query to only include remote jobs.
     */
    public function scopeRemote(Builder $query): Builder
    {
        return $query->where('is_remote', true);
    }

    /**
     * Scope a query to filter by job type.
     */
    public function scopeJobType(Builder $query, string $jobType): Builder
    {
        return $query->where('job_type', $jobType);
    }

    /**
     * Scope a query to filter by experience level.
     */
    public function scopeExperienceLevel(Builder $query, string $level): Builder
    {
        return $query->where('experience_level', $level);
    }

    /**
     * Scope a query to filter by salary range.
     */
    public function scopeSalaryRange(Builder $query, ?float $min = null, ?float $max = null): Builder
    {
        if ($min !== null) {
            $query->where('salary_min', '>=', $min);
        }
        
        if ($max !== null) {
            $query->where('salary_max', '<=', $max);
        }
        
        return $query;
    }

    /**
     * Increment the views count.
     */
    public function incrementViews(): void
    {
        $this->increment('views_count');
    }

    /**
     * Increment the applications count.
     */
    public function incrementApplications(): void
    {
        $this->increment('applications_count');
    }

    /**
     * Check if the job is expired.
     */
    public function isExpired(): bool
    {
        return $this->expires_at && $this->expires_at->isPast();
    }

    /**
     * Get the searchable data for the job.
     */
    public function toSearchableArray(): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'description' => $this->description,
            'location' => $this->location,
            'job_type' => $this->job_type,
            'experience_level' => $this->experience_level,
            'industry' => $this->industry,
            'required_skills' => $this->required_skills,
            'company_name' => $this->employerProfile->company_name ?? '',
        ];
    }
}