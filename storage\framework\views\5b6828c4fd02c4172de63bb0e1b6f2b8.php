<?php $__env->startSection('title', 'Employer Resources'); ?>

<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-gray-50 py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="text-center mb-12">
            <h1 class="text-4xl font-bold text-gray-900 mb-4">Hiring Resources</h1>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Everything you need to attract, hire, and retain top talent for your organization.
            </p>
        </div>

        <!-- Resource Categories -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
            <!-- Hiring Guides -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">Hiring Guides</h3>
                <p class="text-gray-600 mb-4">Comprehensive guides to help you navigate the hiring process from start to finish.</p>
                <a href="#" class="text-blue-600 hover:text-blue-800 font-medium">Explore Guides →</a>
            </div>

            <!-- Interview Templates -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">Interview Templates</h3>
                <p class="text-gray-600 mb-4">Ready-to-use interview questions and evaluation templates for different roles.</p>
                <a href="#" class="text-blue-600 hover:text-blue-800 font-medium">Download Templates →</a>
            </div>

            <!-- Salary Benchmarks -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">Salary Benchmarks</h3>
                <p class="text-gray-600 mb-4">Industry salary data and compensation insights to help you make competitive offers.</p>
                <a href="#" class="text-blue-600 hover:text-blue-800 font-medium">View Data →</a>
            </div>
        </div>

        <!-- Featured Resources -->
        <div class="mb-16">
            <h2 class="text-3xl font-bold text-gray-900 mb-8 text-center">Featured Resources</h2>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Resource 1 -->
                <div class="bg-white rounded-lg shadow-md overflow-hidden">
                    <div class="h-48 bg-gradient-to-r from-blue-500 to-purple-600"></div>
                    <div class="p-6">
                        <h3 class="text-xl font-semibold text-gray-900 mb-2">The Complete Guide to Remote Hiring</h3>
                        <p class="text-gray-600 mb-4">
                            Learn best practices for hiring remote employees, from sourcing to onboarding.
                        </p>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-500">15 min read</span>
                            <a href="#" class="text-blue-600 hover:text-blue-800 font-medium">Read More →</a>
                        </div>
                    </div>
                </div>

                <!-- Resource 2 -->
                <div class="bg-white rounded-lg shadow-md overflow-hidden">
                    <div class="h-48 bg-gradient-to-r from-green-500 to-teal-600"></div>
                    <div class="p-6">
                        <h3 class="text-xl font-semibold text-gray-900 mb-2">Building an Inclusive Hiring Process</h3>
                        <p class="text-gray-600 mb-4">
                            Strategies to eliminate bias and create a more diverse and inclusive workplace.
                        </p>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-500">12 min read</span>
                            <a href="#" class="text-blue-600 hover:text-blue-800 font-medium">Read More →</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tools and Templates -->
        <div class="mb-16">
            <h2 class="text-3xl font-bold text-gray-900 mb-8 text-center">Tools & Templates</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <!-- Tool 1 -->
                <div class="bg-white rounded-lg shadow-md p-6 text-center">
                    <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Job Description Template</h3>
                    <p class="text-gray-600 text-sm mb-4">Professional job posting templates for various roles</p>
                    <button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors text-sm">
                        Download
                    </button>
                </div>

                <!-- Tool 2 -->
                <div class="bg-white rounded-lg shadow-md p-6 text-center">
                    <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Interview Scorecard</h3>
                    <p class="text-gray-600 text-sm mb-4">Standardized evaluation forms for consistent hiring</p>
                    <button class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors text-sm">
                        Download
                    </button>
                </div>

                <!-- Tool 3 -->
                <div class="bg-white rounded-lg shadow-md p-6 text-center">
                    <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Salary Calculator</h3>
                    <p class="text-gray-600 text-sm mb-4">Calculate competitive salaries based on market data</p>
                    <button class="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 transition-colors text-sm">
                        Use Tool
                    </button>
                </div>

                <!-- Tool 4 -->
                <div class="bg-white rounded-lg shadow-md p-6 text-center">
                    <div class="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Onboarding Checklist</h3>
                    <p class="text-gray-600 text-sm mb-4">Comprehensive new hire onboarding process</p>
                    <button class="bg-orange-600 text-white px-4 py-2 rounded-md hover:bg-orange-700 transition-colors text-sm">
                        Download
                    </button>
                </div>
            </div>
        </div>

        <!-- Webinars and Events -->
        <div class="mb-16">
            <h2 class="text-3xl font-bold text-gray-900 mb-8 text-center">Upcoming Webinars</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <!-- Webinar 1 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mr-4">
                            <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900">Mastering Technical Interviews</h3>
                            <p class="text-sm text-gray-600">January 25, 2024 • 2:00 PM EST</p>
                        </div>
                    </div>
                    <p class="text-gray-600 mb-4">Learn how to effectively evaluate technical candidates and ask the right questions.</p>
                    <button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors">
                        Register Now
                    </button>
                </div>

                <!-- Webinar 2 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900">Building Diverse Teams</h3>
                            <p class="text-sm text-gray-600">February 8, 2024 • 1:00 PM EST</p>
                        </div>
                    </div>
                    <p class="text-gray-600 mb-4">Strategies for creating inclusive hiring practices and building diverse teams.</p>
                    <button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors">
                        Register Now
                    </button>
                </div>
            </div>
        </div>

        <!-- Contact Support -->
        <div class="bg-blue-600 rounded-lg p-8 text-center">
            <h2 class="text-3xl font-bold text-white mb-4">Need Personalized Help?</h2>
            <p class="text-blue-100 mb-6 max-w-2xl mx-auto">
                Our hiring experts are here to help you optimize your recruitment process and find the best talent.
            </p>
            <button class="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                Contact Our Experts
            </button>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\laravel\rectra\resources\views/employer/resources.blade.php ENDPATH**/ ?>