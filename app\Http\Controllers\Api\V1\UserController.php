<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Api\Controller;
use App\Models\User;
use App\Models\JobSeekerProfile;
use App\Models\EmployerProfile;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Password;

class UserController extends Controller
{
    /**
     * Get the authenticated user's profile.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function profile(Request $request): JsonResponse
    {
        $user = $request->user();
        
        // Load appropriate profile based on user role
        if ($user->isJobSeeker()) {
            $user->load(['jobSeekerProfile.skills', 'jobSeekerProfile.experiences', 'jobSeekerProfile.educations']);
        } elseif ($user->isEmployer()) {
            $user->load(['employerProfile']);
        }
        
        return $this->success([
            'user' => $user
        ]);
    }

    /**
     * Update the authenticated user's profile.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request): JsonResponse
    {
        $user = $request->user();
        
        $validator = Validator::make($request->all(), [
            'name' => ['sometimes', 'string', 'max:255'],
            'email' => ['sometimes', 'string', 'email', 'max:255', 'unique:users,email,' . $user->id],
            'password' => ['sometimes', 'confirmed', Password::defaults()],
            'avatar' => ['sometimes', 'string', 'max:255'],
        ]);

        if ($validator->fails()) {
            return $this->error('Validation error', 422, $validator->errors());
        }

        // Update user basic info
        $userData = $request->only(['name', 'email', 'avatar']);
        
        if ($request->filled('password')) {
            $userData['password'] = Hash::make($request->password);
        }
        
        $user->update($userData);

        // Update profile based on role
        if ($user->isJobSeeker() && $request->has('job_seeker_profile')) {
            $profileValidator = Validator::make($request->job_seeker_profile, [
                'summary' => ['sometimes', 'string'],
                'phone_number' => ['sometimes', 'string', 'max:20'],
                'location' => ['sometimes', 'string', 'max:255'],
                'skills' => ['sometimes', 'array'],
                'experience_level' => ['sometimes', 'string', 'in:entry,mid,senior,manager'],
                'preferred_job_type' => ['sometimes', 'string', 'in:full_time,part_time,contract,internship'],
                'preferred_salary_min' => ['sometimes', 'numeric', 'min:0'],
                'preferred_salary_max' => ['sometimes', 'numeric', 'min:0'],
                'is_available' => ['sometimes', 'boolean'],
            ]);

            if ($profileValidator->fails()) {
                return $this->error('Profile validation error', 422, $profileValidator->errors());
            }

            $user->jobSeekerProfile()->updateOrCreate(
                ['user_id' => $user->id],
                $request->job_seeker_profile
            );
        }

        if ($user->isEmployer() && $request->has('employer_profile')) {
            $profileValidator = Validator::make($request->employer_profile, [
                'company_name' => ['sometimes', 'string', 'max:255'],
                'company_description' => ['sometimes', 'string'],
                'company_size' => ['sometimes', 'string', 'in:1-10,11-50,51-200,201-500,501-1000,1000+'],
                'industry' => ['sometimes', 'string', 'max:255'],
                'website' => ['sometimes', 'url', 'max:255'],
                'location' => ['sometimes', 'string', 'max:255'],
                'logo_path' => ['sometimes', 'string', 'max:255'],
            ]);

            if ($profileValidator->fails()) {
                return $this->error('Profile validation error', 422, $profileValidator->errors());
            }

            $user->employerProfile()->updateOrCreate(
                ['user_id' => $user->id],
                $request->employer_profile
            );
        }

        // Reload user with updated profile
        if ($user->isJobSeeker()) {
            $user->load('jobSeekerProfile');
        } elseif ($user->isEmployer()) {
            $user->load('employerProfile');
        }

        return $this->success([
            'user' => $user
        ], 'Profile updated successfully');
    }

    /**
     * Update the authenticated user's job seeker profile.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateJobSeekerProfile(Request $request): JsonResponse
    {
        $user = $request->user();
        
        if (!$user->isJobSeeker()) {
            return $this->error('Access denied. Only job seekers can update job seeker profile.', 403);
        }

        $jobSeekerProfile = $user->jobSeekerProfile;
        
        if (!$jobSeekerProfile) {
            return $this->error('Job seeker profile not found', 404);
        }

        $validator = Validator::make($request->all(), [
            'title' => ['nullable', 'string', 'max:255'],
            'bio' => ['nullable', 'string', 'max:1000'],
            'location' => ['nullable', 'string', 'max:255'],
            'website' => ['nullable', 'url', 'max:255'],
            'linkedin_url' => ['nullable', 'url', 'max:255'],
            'github_url' => ['nullable', 'url', 'max:255'],
            'portfolio_url' => ['nullable', 'url', 'max:255'],
            'expected_salary_min' => ['nullable', 'integer', 'min:0'],
            'expected_salary_max' => ['nullable', 'integer', 'min:0', 'gte:expected_salary_min'],
            'salary_currency' => ['nullable', 'string', 'max:3'],
            'availability' => ['nullable', 'in:immediately,within_2_weeks,within_1_month,within_3_months,not_available'],
            'work_type_preference' => ['nullable', 'in:remote,onsite,hybrid,any'],
            'job_type_preference' => ['nullable', 'in:full_time,part_time,contract,freelance,internship,any'],
            'is_open_to_work' => ['nullable', 'boolean'],
            'skills' => ['nullable', 'array'],
            'skills.*' => ['string', 'max:100'],
        ]);

        if ($validator->fails()) {
            return $this->error('Validation error', 422, $validator->errors());
        }

        $updateData = $request->only([
            'title', 'bio', 'location', 'website', 'linkedin_url', 'github_url', 'portfolio_url',
            'expected_salary_min', 'expected_salary_max', 'salary_currency', 'availability',
            'work_type_preference', 'job_type_preference', 'is_open_to_work'
        ]);

        // Remove null values
        $updateData = array_filter($updateData, function ($value) {
            return $value !== null;
        });

        $jobSeekerProfile->update($updateData);

        // Update skills if provided
        if ($request->has('skills')) {
            $jobSeekerProfile->skills()->delete();
            
            if (!empty($request->skills)) {
                $skillsData = collect($request->skills)->map(function ($skill) {
                    return ['name' => $skill];
                })->toArray();
                
                $jobSeekerProfile->skills()->createMany($skillsData);
            }
        }

        // Load relationships for response
        $jobSeekerProfile->load(['skills', 'experiences', 'educations']);

        return $this->success([
            'profile' => $jobSeekerProfile
        ], 'Job seeker profile updated successfully');
    }

    /**
     * Update the authenticated user's employer profile.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateEmployerProfile(Request $request): JsonResponse
    {
        $user = $request->user();
        
        if (!$user->isEmployer()) {
            return $this->error('Access denied. Only employers can update employer profile.', 403);
        }

        $employerProfile = $user->employerProfile;
        
        if (!$employerProfile) {
            return $this->error('Employer profile not found', 404);
        }

        $validator = Validator::make($request->all(), [
            'company_name' => ['sometimes', 'required', 'string', 'max:255'],
            'company_description' => ['nullable', 'string', 'max:2000'],
            'company_size' => ['nullable', 'in:1-10,11-50,51-200,201-500,501-1000,1000+'],
            'industry' => ['nullable', 'string', 'max:255'],
            'founded_year' => ['nullable', 'integer', 'min:1800', 'max:' . date('Y')],
            'website' => ['nullable', 'url', 'max:255'],
            'linkedin_url' => ['nullable', 'url', 'max:255'],
            'twitter_url' => ['nullable', 'url', 'max:255'],
            'facebook_url' => ['nullable', 'url', 'max:255'],
            'location' => ['nullable', 'string', 'max:255'],
            'address' => ['nullable', 'string', 'max:500'],
            'phone' => ['nullable', 'string', 'max:20'],
            'logo' => ['nullable', 'image', 'mimes:jpeg,png,jpg,gif', 'max:2048'],
            'cover_image' => ['nullable', 'image', 'mimes:jpeg,png,jpg,gif', 'max:5120'],
        ]);

        if ($validator->fails()) {
            return $this->error('Validation error', 422, $validator->errors());
        }

        $updateData = $request->only([
            'company_name', 'company_description', 'company_size', 'industry', 'founded_year',
            'website', 'linkedin_url', 'twitter_url', 'facebook_url', 'location', 'address', 'phone'
        ]);

        // Remove null values
        $updateData = array_filter($updateData, function ($value) {
            return $value !== null;
        });

        // Handle logo upload
        if ($request->hasFile('logo')) {
            // Delete old logo if exists
            if ($employerProfile->logo) {
                Storage::disk('public')->delete($employerProfile->logo);
            }
            
            $logoPath = $request->file('logo')->store('company-logos', 'public');
            $updateData['logo'] = $logoPath;
        }

        // Handle cover image upload
        if ($request->hasFile('cover_image')) {
            // Delete old cover image if exists
            if ($employerProfile->cover_image) {
                Storage::disk('public')->delete($employerProfile->cover_image);
            }
            
            $coverImagePath = $request->file('cover_image')->store('company-covers', 'public');
            $updateData['cover_image'] = $coverImagePath;
        }

        $employerProfile->update($updateData);

        return $this->success([
            'profile' => $employerProfile
        ], 'Employer profile updated successfully');
    }

    /**
     * Change the authenticated user's password.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function changePassword(Request $request): JsonResponse
    {
        $user = $request->user();
        
        $validator = Validator::make($request->all(), [
            'current_password' => ['required', 'string'],
            'new_password' => ['required', 'string', 'min:8', 'confirmed'],
        ]);

        if ($validator->fails()) {
            return $this->error('Validation error', 422, $validator->errors());
        }

        // Check if current password is correct
        if (!Hash::check($request->current_password, $user->password)) {
            return $this->error('Current password is incorrect', 422);
        }

        // Update password
        $user->update([
            'password' => Hash::make($request->new_password)
        ]);

        return $this->success([
            'message' => 'Password changed successfully'
        ]);
    }

    /**
     * Get user's account settings.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function settings(Request $request): JsonResponse
    {
        $user = $request->user();
        
        $settings = [
            'email_notifications' => $user->email_notifications ?? true,
            'push_notifications' => $user->push_notifications ?? true,
            'marketing_emails' => $user->marketing_emails ?? false,
            'profile_visibility' => $user->profile_visibility ?? 'public',
            'two_factor_enabled' => $user->two_factor_secret ? true : false,
        ];

        return $this->success([
            'settings' => $settings
        ]);
    }

    /**
     * Update user's account settings.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateSettings(Request $request): JsonResponse
    {
        $user = $request->user();
        
        $validator = Validator::make($request->all(), [
            'email_notifications' => ['nullable', 'boolean'],
            'push_notifications' => ['nullable', 'boolean'],
            'marketing_emails' => ['nullable', 'boolean'],
            'profile_visibility' => ['nullable', 'in:public,private,connections_only'],
        ]);

        if ($validator->fails()) {
            return $this->error('Validation error', 422, $validator->errors());
        }

        $updateData = $request->only([
            'email_notifications', 'push_notifications', 'marketing_emails', 'profile_visibility'
        ]);

        // Remove null values
        $updateData = array_filter($updateData, function ($value) {
            return $value !== null;
        });

        $user->update($updateData);

        $settings = [
            'email_notifications' => $user->email_notifications ?? true,
            'push_notifications' => $user->push_notifications ?? true,
            'marketing_emails' => $user->marketing_emails ?? false,
            'profile_visibility' => $user->profile_visibility ?? 'public',
            'two_factor_enabled' => $user->two_factor_secret ? true : false,
        ];

        return $this->success([
            'settings' => $settings
        ], 'Settings updated successfully');
    }

    /**
     * Get user's activity statistics.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function stats(Request $request): JsonResponse
    {
        $user = $request->user();
        
        $stats = [];
        
        if ($user->isJobSeeker()) {
            $jobSeekerProfile = $user->jobSeekerProfile;
            
            if ($jobSeekerProfile) {
                $stats = [
                    'profile_views' => $jobSeekerProfile->profile_views ?? 0,
                    'applications_sent' => $jobSeekerProfile->jobApplications()->count(),
                    'courses_enrolled' => $jobSeekerProfile->courseEnrollments()->count(),
                    'courses_completed' => $jobSeekerProfile->courseEnrollments()->where('status', 'completed')->count(),
                    'saved_jobs' => $jobSeekerProfile->savedJobs()->count(),
                    'resumes_count' => $jobSeekerProfile->resumes()->count(),
                ];
            }
        } elseif ($user->isEmployer()) {
            $employerProfile = $user->employerProfile;
            
            if ($employerProfile) {
                $stats = [
                    'profile_views' => $employerProfile->profile_views ?? 0,
                    'jobs_posted' => $employerProfile->jobs()->count(),
                    'active_jobs' => $employerProfile->jobs()->where('status', 'active')->count(),
                    'applications_received' => $employerProfile->jobs()->withCount('applications')->get()->sum('applications_count'),
                    'hired_candidates' => $employerProfile->jobs()->join('job_applications', 'jobs.id', '=', 'job_applications.job_id')
                        ->where('job_applications.status', 'hired')->count(),
                ];
            }
        }

        return $this->success([
            'stats' => $stats
        ]);
    }

    /**
     * Delete the authenticated user's account.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteAccount(Request $request): JsonResponse
    {
        $user = $request->user();
        
        $validator = Validator::make($request->all(), [
            'password' => ['required', 'string'],
            'confirmation' => ['required', 'string', 'in:DELETE'],
        ]);

        if ($validator->fails()) {
            return $this->error('Validation error', 422, $validator->errors());
        }

        // Check if password is correct
        if (!Hash::check($request->password, $user->password)) {
            return $this->error('Password is incorrect', 422);
        }

        // Delete user's files
        if ($user->avatar) {
            Storage::disk('public')->delete($user->avatar);
        }

        if ($user->isEmployer() && $user->employerProfile) {
            if ($user->employerProfile->logo) {
                Storage::disk('public')->delete($user->employerProfile->logo);
            }
            if ($user->employerProfile->cover_image) {
                Storage::disk('public')->delete($user->employerProfile->cover_image);
            }
        }

        // Revoke all tokens
        $user->tokens()->delete();

        // Soft delete the user (this will cascade to related models if configured)
        $user->delete();

        return $this->success([
            'message' => 'Account deleted successfully'
        ]);
    }

    /**
     * Get user's notifications.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function notifications(Request $request): JsonResponse
    {
        $user = $request->user();
        
        $query = $user->notifications();
        
        // Filter by read status
        if ($request->filled('unread_only')) {
            $unreadOnly = filter_var($request->unread_only, FILTER_VALIDATE_BOOLEAN);
            if ($unreadOnly) {
                $query->whereNull('read_at');
            }
        }
        
        // Filter by type
        if ($request->filled('type')) {
            $query->where('job_type', $request->type);
        }
        
        $notifications = $query->orderBy('created_at', 'desc')
            ->paginate($request->input('per_page', 20));

        return $this->success([
            'notifications' => $notifications->items(),
            'pagination' => [
                'current_page' => $notifications->currentPage(),
                'last_page' => $notifications->lastPage(),
                'per_page' => $notifications->perPage(),
                'total' => $notifications->total(),
                'has_more_pages' => $notifications->hasMorePages()
            ],
            'unread_count' => $user->unreadNotifications()->count()
        ]);
    }

    /**
     * Mark notifications as read.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function markNotificationsRead(Request $request): JsonResponse
    {
        $user = $request->user();
        
        $validator = Validator::make($request->all(), [
            'notification_ids' => ['nullable', 'array'],
            'notification_ids.*' => ['string'],
            'mark_all' => ['nullable', 'boolean'],
        ]);

        if ($validator->fails()) {
            return $this->error('Validation error', 422, $validator->errors());
        }

        if ($request->mark_all) {
            // Mark all notifications as read
            $user->unreadNotifications()->update(['read_at' => now()]);
            $message = 'All notifications marked as read';
        } elseif ($request->filled('notification_ids')) {
            // Mark specific notifications as read
            $user->notifications()
                ->whereIn('id', $request->notification_ids)
                ->whereNull('read_at')
                ->update(['read_at' => now()]);
            $message = 'Selected notifications marked as read';
        } else {
            return $this->error('Either notification_ids or mark_all must be provided', 422);
        }

        return $this->success([
            'message' => $message,
            'unread_count' => $user->unreadNotifications()->count()
        ]);
    }
}