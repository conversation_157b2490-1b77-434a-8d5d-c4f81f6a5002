@extends('layouts.admin')

@section('title', 'Subscription Plans')

@section('content')
<div class="min-h-screen bg-gray-50">
    <div class="py-6">
        <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <!-- Header -->
            <div class="mb-8">
                <div class="sm:flex sm:items-center sm:justify-between">
                    <div>
                        <h1 class="text-3xl font-bold leading-tight tracking-tight text-gray-900">Subscription Plans</h1>
                        <p class="mt-2 text-sm text-gray-700">Manage subscription plans and pricing</p>
                    </div>
                    <div class="mt-4 sm:mt-0 flex items-center space-x-3">
                        <!-- View Toggle -->
                        <div class="flex items-center bg-gray-100 rounded-lg p-1">
                            <button type="button" id="gridViewBtn" class="px-3 py-1 text-sm font-medium rounded-md bg-white text-gray-900 shadow-sm">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"></path>
                                </svg>
                            </button>
                            <button type="button" id="tableViewBtn" class="px-3 py-1 text-sm font-medium rounded-md text-gray-500 hover:text-gray-900">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path>
                                </svg>
                            </button>
                        </div>

                        <!-- Export Plans -->
                        <button type="button" onclick="exportPlans()" class="inline-flex items-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50">
                            <svg class="-ml-0.5 mr-1.5 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            Export
                        </button>

                        <!-- Add Plan -->
                        <button type="button" onclick="openCreatePlanModal()" class="inline-flex items-center rounded-md bg-blue-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600">
                            <svg class="-ml-0.5 mr-1.5 h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                <path d="M10.75 4.75a.75.75 0 00-1.5 0v4.5h-4.5a.75.75 0 000 1.5h4.5v4.5a.75.75 0 001.5 0v-4.5h4.5a.75.75 0 000-1.5h-4.5v-4.5z" />
                            </svg>
                            Add Plan
                        </button>

                        <!-- Refresh -->
                        <button type="button" onclick="refreshPlans()" class="inline-flex items-center rounded-md bg-white px-2 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50" title="Refresh">
                            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">Total Plans</dt>
                                    <dd class="text-lg font-medium text-gray-900">{{ $plans->count() }}</dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">Active Plans</dt>
                                    <dd class="text-lg font-medium text-gray-900">{{ $plans->where('status', 'active')->count() }}</dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">Total Subscribers</dt>
                                    <dd class="text-lg font-medium text-gray-900">{{ number_format($plans->sum('subscribers_count') ?? 0) }}</dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">Total Revenue</dt>
                                    <dd class="text-lg font-medium text-gray-900">${{ number_format($plans->sum('total_revenue') ?? 0, 2) }}</dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Plans Grid -->
            <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
                @foreach($plans as $plan)
                    <div class="relative bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
                        <!-- Plan Header -->
                        <div class="p-6">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-900">{{ $plan->name }}</h3>
                                    <p class="text-sm text-gray-500">{{ $plan->description }}</p>
                                </div>
                                <div class="flex items-center space-x-2">
                                    @if($plan->is_popular)
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                            Popular
                                        </span>
                                    @endif
                                    @if($plan->status === 'active')
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            <svg class="w-1.5 h-1.5 mr-1.5" fill="currentColor" viewBox="0 0 8 8">
                                                <circle cx="4" cy="4" r="3" />
                                            </svg>
                                            Active
                                        </span>
                                    @else
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                            <svg class="w-1.5 h-1.5 mr-1.5" fill="currentColor" viewBox="0 0 8 8">
                                                <circle cx="4" cy="4" r="3" />
                                            </svg>
                                            Inactive
                                        </span>
                                    @endif
                                </div>
                            </div>
                            
                            <!-- Pricing -->
                            <div class="mt-4">
                                <div class="flex items-baseline">
                                    @if($plan->price > 0)
                                        <span class="text-3xl font-bold text-gray-900">${{ number_format($plan->price, 2) }}</span>
                                        <span class="ml-1 text-sm text-gray-500">/{{ $plan->billing_cycle }}</span>
                                    @else
                                        <span class="text-3xl font-bold text-gray-900">Free</span>
                                    @endif
                                </div>
                                @if($plan->trial_days > 0)
                                    <p class="text-sm text-gray-500 mt-1">{{ $plan->trial_days }}-day free trial</p>
                                @endif
                            </div>
                        </div>

                        <!-- Features -->
                        <div class="px-6 pb-6">
                            <h4 class="text-sm font-medium text-gray-900 mb-3">Features</h4>
                            @if($plan->features)
                                <ul class="space-y-2">
                                    @foreach($plan->features as $feature)
                                        <li class="flex items-start">
                                            <svg class="w-4 h-4 text-green-500 mt-0.5 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                            <span class="text-sm text-gray-600">{{ $feature }}</span>
                                        </li>
                                    @endforeach
                                </ul>
                            @else
                                <p class="text-sm text-gray-500">No features listed</p>
                            @endif
                        </div>

                        <!-- Limits -->
                        @if($plan->job_posts_limit || $plan->featured_jobs_limit || $plan->applications_limit)
                            <div class="px-6 pb-6">
                                <h4 class="text-sm font-medium text-gray-900 mb-3">Limits</h4>
                                <div class="grid grid-cols-2 gap-4 text-sm">
                                    @if($plan->job_posts_limit)
                                        <div>
                                            <span class="text-gray-500">Job Posts:</span>
                                            <span class="font-medium text-gray-900">{{ $plan->job_posts_limit === -1 ? 'Unlimited' : $plan->job_posts_limit }}</span>
                                        </div>
                                    @endif
                                    @if($plan->featured_jobs_limit)
                                        <div>
                                            <span class="text-gray-500">Featured:</span>
                                            <span class="font-medium text-gray-900">{{ $plan->featured_jobs_limit === -1 ? 'Unlimited' : $plan->featured_jobs_limit }}</span>
                                        </div>
                                    @endif
                                    @if($plan->applications_limit)
                                        <div>
                                            <span class="text-gray-500">Applications:</span>
                                            <span class="font-medium text-gray-900">{{ $plan->applications_limit === -1 ? 'Unlimited' : $plan->applications_limit }}</span>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        @endif

                        <!-- Stats -->
                        <div class="px-6 pb-6">
                            <div class="grid grid-cols-2 gap-4 text-sm">
                                <div>
                                    <span class="text-gray-500">Subscribers:</span>
                                    <span class="font-medium text-gray-900">{{ $plan->subscribers_count ?? 0 }}</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">Revenue:</span>
                                    <span class="font-medium text-gray-900">${{ number_format($plan->total_revenue ?? 0, 2) }}</span>
                                </div>
                            </div>
                        </div>

                        <!-- Actions -->
                        <div class="px-6 py-4 bg-gray-50 rounded-b-lg">
                            <div class="flex items-center justify-between">
                                <div class="text-xs text-gray-500">
                                    Created {{ $plan->created_at->format('M j, Y') }}
                                </div>
                                <div class="flex items-center space-x-2">
                                    <!-- Actions Dropdown -->
                                    <div class="relative">
                                        <button type="button" onclick="togglePlanActions({{ $plan->id }})" class="inline-flex items-center px-2 py-1 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                            Actions
                                            <svg class="ml-1 -mr-1 h-3 w-3" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
                                            </svg>
                                        </button>
                                        <div id="planActions{{ $plan->id }}" class="hidden absolute right-0 z-10 mt-1 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                                            <button type="button" onclick="editPlan({{ $plan->id }})" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                                <svg class="inline w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                                </svg>
                                                Edit Plan
                                            </button>
                                            <button type="button" onclick="duplicatePlan({{ $plan->id }})" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                                <svg class="inline w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                                </svg>
                                                Duplicate Plan
                                            </button>
                                            <button type="button" onclick="viewSubscribers({{ $plan->id }})" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                                <svg class="inline w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                                </svg>
                                                View Subscribers ({{ $plan->subscribers_count ?? 0 }})
                                            </button>
                                            @if($plan->status === 'active')
                                                <form method="POST" action="{{ route('admin.subscription-plans.deactivate', $plan) }}" class="block">
                                                    @csrf
                                                    <button type="submit" class="block w-full text-left px-4 py-2 text-sm text-yellow-700 hover:bg-gray-100">
                                                        <svg class="inline w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                        </svg>
                                                        Deactivate Plan
                                                    </button>
                                                </form>
                                            @else
                                                <form method="POST" action="{{ route('admin.subscription-plans.activate', $plan) }}" class="block">
                                                    @csrf
                                                    <button type="submit" class="block w-full text-left px-4 py-2 text-sm text-green-700 hover:bg-gray-100">
                                                        <svg class="inline w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                        </svg>
                                                        Activate Plan
                                                    </button>
                                                </form>
                                            @endif
                                            <button type="button" onclick="deletePlan({{ $plan->id }})" class="block w-full text-left px-4 py-2 text-sm text-red-700 hover:bg-gray-100">
                                                <svg class="inline w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1-1H8a1 1 0 00-1 1v3M4 7h16"></path>
                                                </svg>
                                                Delete Plan
                                            </button>
                                        </div>
                                    </div>

                                    <!-- Quick Status Toggle -->
                                    @if($plan->status === 'active')
                                        <form method="POST" action="{{ route('admin.subscription-plans.deactivate', $plan) }}" class="inline">
                                            @csrf
                                            <button type="submit" class="inline-flex items-center px-2 py-1 border border-yellow-300 shadow-sm text-xs font-medium rounded text-yellow-700 bg-yellow-50 hover:bg-yellow-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500" title="Deactivate Plan">
                                                <svg class="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                </svg>
                                            </button>
                                        </form>
                                    @else
                                        <form method="POST" action="{{ route('admin.subscription-plans.activate', $plan) }}" class="inline">
                                            @csrf
                                            <button type="submit" class="inline-flex items-center px-2 py-1 border border-green-300 shadow-sm text-xs font-medium rounded text-green-700 bg-green-50 hover:bg-green-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500" title="Activate Plan">
                                                <svg class="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                </svg>
                                            </button>
                                        </form>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>

            @if($plans->isEmpty())
                <div class="text-center py-12">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 8.25h19.5M2.25 9h19.5m-16.5 5.25h6m-6 2.25h3m-3.75 3h15a2.25 2.25 0 002.25-2.25V6.75A2.25 2.25 0 0019.5 4.5h-15a2.25 2.25 0 00-2.25 2.25v10.5A2.25 2.25 0 004.5 19.5z" />
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">No subscription plans</h3>
                    <p class="mt-1 text-sm text-gray-500">Get started by creating a new subscription plan.</p>
                    <div class="mt-6">
                        <button type="button" class="inline-flex items-center rounded-md bg-blue-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600">
                            <svg class="-ml-0.5 mr-1.5 h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                <path d="M10.75 4.75a.75.75 0 00-1.5 0v4.5h-4.5a.75.75 0 000 1.5h4.5v4.5a.75.75 0 001.5 0v-4.5h4.5a.75.75 0 000-1.5h-4.5v-4.5z" />
                            </svg>
                            Add Plan
                        </button>
                    </div>
                </div>
            @endif

            <!-- Plan Comparison Table -->
            @if($plans->isNotEmpty())
                <div class="mt-12">
                    <div class="bg-white shadow rounded-lg overflow-hidden">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h2 class="text-lg font-medium text-gray-900">Plan Comparison</h2>
                        </div>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Plan</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Job Posts</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Featured Jobs</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Applications</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subscribers</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    @foreach($plans as $plan)
                                        <tr class="hover:bg-gray-50">
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="flex items-center">
                                                    <div>
                                                        <div class="text-sm font-medium text-gray-900">{{ $plan->name }}</div>
                                                        <div class="text-sm text-gray-500">{{ Str::limit($plan->description, 50) }}</div>
                                                    </div>
                                                    @if($plan->is_popular)
                                                        <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                            Popular
                                                        </span>
                                                    @endif
                                                </div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                @if($plan->price > 0)
                                                    <div class="font-medium">${{ number_format($plan->price, 2) }}</div>
                                                    <div class="text-gray-500">per {{ $plan->billing_cycle }}</div>
                                                @else
                                                    <div class="font-medium text-green-600">Free</div>
                                                @endif
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                {{ $plan->job_posts_limit === -1 ? 'Unlimited' : ($plan->job_posts_limit ?? 'N/A') }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                {{ $plan->featured_jobs_limit === -1 ? 'Unlimited' : ($plan->featured_jobs_limit ?? 'N/A') }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                {{ $plan->applications_limit === -1 ? 'Unlimited' : ($plan->applications_limit ?? 'N/A') }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                <div class="font-medium">{{ $plan->subscribers_count ?? 0 }}</div>
                                                <div class="text-gray-500">${{ number_format($plan->total_revenue ?? 0, 2) }} revenue</div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                @if($plan->status === 'active')
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                        <svg class="w-1.5 h-1.5 mr-1.5" fill="currentColor" viewBox="0 0 8 8">
                                                            <circle cx="4" cy="4" r="3" />
                                                        </svg>
                                                        Active
                                                    </span>
                                                @else
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                                        <svg class="w-1.5 h-1.5 mr-1.5" fill="currentColor" viewBox="0 0 8 8">
                                                            <circle cx="4" cy="4" r="3" />
                                                        </svg>
                                                        Inactive
                                                    </span>
                                                @endif
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>

    <!-- Create Plan Modal -->
    <div id="createPlanModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-10 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Create New Subscription Plan</h3>
                <form id="createPlanForm" method="POST" action="{{ route('admin.subscription-plans.store') }}">
                    @csrf
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Plan Name</label>
                            <input type="text" name="name" id="name" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div>
                            <label for="price" class="block text-sm font-medium text-gray-700 mb-2">Price</label>
                            <input type="number" name="price" id="price" step="0.01" min="0" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                    </div>
                    <div class="mb-4">
                        <label for="description" class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                        <textarea name="description" id="description" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                        <div>
                            <label for="billing_cycle" class="block text-sm font-medium text-gray-700 mb-2">Billing Cycle</label>
                            <select name="billing_cycle" id="billing_cycle" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="monthly">Monthly</option>
                                <option value="yearly">Yearly</option>
                            </select>
                        </div>
                        <div>
                            <label for="trial_days" class="block text-sm font-medium text-gray-700 mb-2">Trial Days</label>
                            <input type="number" name="trial_days" id="trial_days" min="0" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div>
                            <label for="job_posts_limit" class="block text-sm font-medium text-gray-700 mb-2">Job Posts Limit</label>
                            <input type="number" name="job_posts_limit" id="job_posts_limit" min="-1" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="-1 for unlimited">
                        </div>
                    </div>
                    <div class="flex items-center space-x-4 mb-4">
                        <label class="flex items-center">
                            <input type="checkbox" name="is_popular" value="1" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <span class="ml-2 text-sm text-gray-700">Mark as Popular</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" name="status" value="active" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <span class="ml-2 text-sm text-gray-700">Active</span>
                        </label>
                    </div>
                    <div class="flex justify-end space-x-3">
                        <button type="button" onclick="closeCreatePlanModal()" class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200">
                            Cancel
                        </button>
                        <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700">
                            Create Plan
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div id="deletePlanModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3 text-center">
                <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                    <svg class="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mt-2">Delete Subscription Plan</h3>
                <div class="mt-2 px-7 py-3">
                    <p class="text-sm text-gray-500">
                        Are you sure you want to delete this subscription plan? This action cannot be undone and will affect all current subscribers.
                    </p>
                </div>
                <div class="items-center px-4 py-3">
                    <button id="cancelDelete" class="px-4 py-2 bg-gray-500 text-white text-base font-medium rounded-md w-24 mr-2 hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-300">
                        Cancel
                    </button>
                    <form id="deletePlanForm" method="POST" class="inline">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="px-4 py-2 bg-red-600 text-white text-base font-medium rounded-md w-24 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-300">
                            Delete
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Subscribers Modal -->
    <div id="subscribersModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-10 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">Plan Subscribers</h3>
                    <button onclick="closeSubscribersModal()" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div id="subscribersContent">
                    <!-- Content will be loaded dynamically -->
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
// Global variables
let currentView = 'grid';

// Initialize page functionality
document.addEventListener('DOMContentLoaded', function() {
    initializeViewToggle();
    initializePlanActions();
});

// View toggle functionality
function initializeViewToggle() {
    const gridViewBtn = document.getElementById('gridViewBtn');
    const tableViewBtn = document.getElementById('tableViewBtn');
    const plansGrid = document.querySelector('.grid.grid-cols-1.gap-6.sm\\:grid-cols-2.lg\\:grid-cols-3');
    const comparisonTable = document.querySelector('.mt-12');

    gridViewBtn.addEventListener('click', function() {
        currentView = 'grid';
        updateViewButtons();
        plansGrid.style.display = 'grid';
        if (comparisonTable) comparisonTable.style.display = 'none';
    });

    tableViewBtn.addEventListener('click', function() {
        currentView = 'table';
        updateViewButtons();
        plansGrid.style.display = 'none';
        if (comparisonTable) comparisonTable.style.display = 'block';
    });
}

function updateViewButtons() {
    const gridViewBtn = document.getElementById('gridViewBtn');
    const tableViewBtn = document.getElementById('tableViewBtn');

    if (currentView === 'grid') {
        gridViewBtn.className = 'px-3 py-1 text-sm font-medium rounded-md bg-white text-gray-900 shadow-sm';
        tableViewBtn.className = 'px-3 py-1 text-sm font-medium rounded-md text-gray-500 hover:text-gray-900';
    } else {
        gridViewBtn.className = 'px-3 py-1 text-sm font-medium rounded-md text-gray-500 hover:text-gray-900';
        tableViewBtn.className = 'px-3 py-1 text-sm font-medium rounded-md bg-white text-gray-900 shadow-sm';
    }
}

// Plan actions functionality
function initializePlanActions() {
    // Close dropdowns when clicking outside
    document.addEventListener('click', function(e) {
        if (!e.target.closest('[onclick^="togglePlanActions"]')) {
            document.querySelectorAll('[id^="planActions"]').forEach(menu => {
                menu.classList.add('hidden');
            });
        }
    });
}

function togglePlanActions(planId) {
    const menu = document.getElementById(`planActions${planId}`);

    // Close all other menus
    document.querySelectorAll('[id^="planActions"]').forEach(otherMenu => {
        if (otherMenu !== menu) {
            otherMenu.classList.add('hidden');
        }
    });

    menu.classList.toggle('hidden');
}

// Modal functions
function openCreatePlanModal() {
    document.getElementById('createPlanModal').classList.remove('hidden');
}

function closeCreatePlanModal() {
    document.getElementById('createPlanModal').classList.add('hidden');
    document.getElementById('createPlanForm').reset();
}

function deletePlan(planId) {
    document.getElementById('deletePlanForm').action = `/admin/subscription-plans/${planId}`;
    document.getElementById('deletePlanModal').classList.remove('hidden');
}

document.getElementById('cancelDelete').addEventListener('click', function() {
    document.getElementById('deletePlanModal').classList.add('hidden');
});

function editPlan(planId) {
    // Redirect to edit page or open edit modal
    window.location.href = `/admin/subscription-plans/${planId}/edit`;
}

function duplicatePlan(planId) {
    if (confirm('Are you sure you want to duplicate this plan?')) {
        fetch(`/admin/subscription-plans/${planId}/duplicate`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            }
        }).then(response => {
            if (response.ok) {
                window.location.reload();
            } else {
                alert('Failed to duplicate plan');
            }
        });
    }
}

function viewSubscribers(planId) {
    document.getElementById('subscribersModal').classList.remove('hidden');

    // Load subscribers data
    fetch(`/admin/subscription-plans/${planId}/subscribers`)
        .then(response => response.text())
        .then(html => {
            document.getElementById('subscribersContent').innerHTML = html;
        })
        .catch(error => {
            document.getElementById('subscribersContent').innerHTML = '<p class="text-red-500">Failed to load subscribers</p>';
        });
}

function closeSubscribersModal() {
    document.getElementById('subscribersModal').classList.add('hidden');
}

// Utility functions
function exportPlans() {
    window.location.href = '/admin/subscription-plans/export';
}

function refreshPlans() {
    window.location.reload();
}

// Real-time updates
function updatePlanStats() {
    fetch('/admin/subscription-plans/stats', {
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    }).then(response => response.json())
      .then(data => {
          // Update statistics cards
          updateStatsCards(data);
      });
}

function updateStatsCards(data) {
    // Update the statistics cards with new data
    if (data.totalPlans !== undefined) {
        document.querySelector('[data-stat="total-plans"]').textContent = data.totalPlans;
    }
    if (data.activePlans !== undefined) {
        document.querySelector('[data-stat="active-plans"]').textContent = data.activePlans;
    }
    if (data.totalSubscribers !== undefined) {
        document.querySelector('[data-stat="total-subscribers"]').textContent = data.totalSubscribers.toLocaleString();
    }
    if (data.totalRevenue !== undefined) {
        document.querySelector('[data-stat="total-revenue"]').textContent = '$' + data.totalRevenue.toLocaleString();
    }
}

// Initialize real-time updates (optional)
// setInterval(updatePlanStats, 60000); // Update every minute
</script>
@endpush
@endsection