@extends('layouts.admin')

@section('title', 'Subscription Plans')

@section('content')
<div class="min-h-screen bg-gray-50">
    <div class="py-6">
        <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <!-- Header -->
            <div class="mb-8">
                <div class="sm:flex sm:items-center sm:justify-between">
                    <div>
                        <h1 class="text-3xl font-bold leading-tight tracking-tight text-gray-900">Subscription Plans</h1>
                        <p class="mt-2 text-sm text-gray-700">Manage subscription plans and pricing</p>
                    </div>
                    <div class="mt-4 sm:mt-0">
                        <button type="button" class="inline-flex items-center rounded-md bg-blue-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600">
                            <svg class="-ml-0.5 mr-1.5 h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                <path d="M10.75 4.75a.75.75 0 00-1.5 0v4.5h-4.5a.75.75 0 000 1.5h4.5v4.5a.75.75 0 001.5 0v-4.5h4.5a.75.75 0 000-1.5h-4.5v-4.5z" />
                            </svg>
                            Add Plan
                        </button>
                    </div>
                </div>
            </div>

            <!-- Plans Grid -->
            <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
                @foreach($plans as $plan)
                    <div class="relative bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
                        <!-- Plan Header -->
                        <div class="p-6">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-900">{{ $plan->name }}</h3>
                                    <p class="text-sm text-gray-500">{{ $plan->description }}</p>
                                </div>
                                <div class="flex items-center space-x-2">
                                    @if($plan->is_popular)
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                            Popular
                                        </span>
                                    @endif
                                    @if($plan->status === 'active')
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            <svg class="w-1.5 h-1.5 mr-1.5" fill="currentColor" viewBox="0 0 8 8">
                                                <circle cx="4" cy="4" r="3" />
                                            </svg>
                                            Active
                                        </span>
                                    @else
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                            <svg class="w-1.5 h-1.5 mr-1.5" fill="currentColor" viewBox="0 0 8 8">
                                                <circle cx="4" cy="4" r="3" />
                                            </svg>
                                            Inactive
                                        </span>
                                    @endif
                                </div>
                            </div>
                            
                            <!-- Pricing -->
                            <div class="mt-4">
                                <div class="flex items-baseline">
                                    @if($plan->price > 0)
                                        <span class="text-3xl font-bold text-gray-900">${{ number_format($plan->price, 2) }}</span>
                                        <span class="ml-1 text-sm text-gray-500">/{{ $plan->billing_cycle }}</span>
                                    @else
                                        <span class="text-3xl font-bold text-gray-900">Free</span>
                                    @endif
                                </div>
                                @if($plan->trial_days > 0)
                                    <p class="text-sm text-gray-500 mt-1">{{ $plan->trial_days }}-day free trial</p>
                                @endif
                            </div>
                        </div>

                        <!-- Features -->
                        <div class="px-6 pb-6">
                            <h4 class="text-sm font-medium text-gray-900 mb-3">Features</h4>
                            @if($plan->features)
                                <ul class="space-y-2">
                                    @foreach($plan->features as $feature)
                                        <li class="flex items-start">
                                            <svg class="w-4 h-4 text-green-500 mt-0.5 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                            <span class="text-sm text-gray-600">{{ $feature }}</span>
                                        </li>
                                    @endforeach
                                </ul>
                            @else
                                <p class="text-sm text-gray-500">No features listed</p>
                            @endif
                        </div>

                        <!-- Limits -->
                        @if($plan->job_posts_limit || $plan->featured_jobs_limit || $plan->applications_limit)
                            <div class="px-6 pb-6">
                                <h4 class="text-sm font-medium text-gray-900 mb-3">Limits</h4>
                                <div class="grid grid-cols-2 gap-4 text-sm">
                                    @if($plan->job_posts_limit)
                                        <div>
                                            <span class="text-gray-500">Job Posts:</span>
                                            <span class="font-medium text-gray-900">{{ $plan->job_posts_limit === -1 ? 'Unlimited' : $plan->job_posts_limit }}</span>
                                        </div>
                                    @endif
                                    @if($plan->featured_jobs_limit)
                                        <div>
                                            <span class="text-gray-500">Featured:</span>
                                            <span class="font-medium text-gray-900">{{ $plan->featured_jobs_limit === -1 ? 'Unlimited' : $plan->featured_jobs_limit }}</span>
                                        </div>
                                    @endif
                                    @if($plan->applications_limit)
                                        <div>
                                            <span class="text-gray-500">Applications:</span>
                                            <span class="font-medium text-gray-900">{{ $plan->applications_limit === -1 ? 'Unlimited' : $plan->applications_limit }}</span>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        @endif

                        <!-- Stats -->
                        <div class="px-6 pb-6">
                            <div class="grid grid-cols-2 gap-4 text-sm">
                                <div>
                                    <span class="text-gray-500">Subscribers:</span>
                                    <span class="font-medium text-gray-900">{{ $plan->subscribers_count ?? 0 }}</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">Revenue:</span>
                                    <span class="font-medium text-gray-900">${{ number_format($plan->total_revenue ?? 0, 2) }}</span>
                                </div>
                            </div>
                        </div>

                        <!-- Actions -->
                        <div class="px-6 py-4 bg-gray-50 rounded-b-lg">
                            <div class="flex items-center justify-between">
                                <div class="text-xs text-gray-500">
                                    Created {{ $plan->created_at->format('M j, Y') }}
                                </div>
                                <div class="flex items-center space-x-2">
                                    <button type="button" class="text-blue-600 hover:text-blue-900 text-sm font-medium" title="Edit Plan">
                                        Edit
                                    </button>
                                    @if($plan->status === 'active')
                                        <form method="POST" action="{{ route('admin.subscription-plans.deactivate', $plan) }}" class="inline">
                                            @csrf
                                            <button type="submit" class="text-yellow-600 hover:text-yellow-900 text-sm font-medium" title="Deactivate Plan">
                                                Deactivate
                                            </button>
                                        </form>
                                    @else
                                        <form method="POST" action="{{ route('admin.subscription-plans.activate', $plan) }}" class="inline">
                                            @csrf
                                            <button type="submit" class="text-green-600 hover:text-green-900 text-sm font-medium" title="Activate Plan">
                                                Activate
                                            </button>
                                        </form>
                                    @endif
                                    <form method="POST" action="{{ route('admin.subscription-plans.destroy', $plan) }}" class="inline" onsubmit="return confirm('Are you sure you want to delete this plan? This action cannot be undone.')">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="text-red-600 hover:text-red-900 text-sm font-medium" title="Delete Plan">
                                            Delete
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>

            @if($plans->isEmpty())
                <div class="text-center py-12">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 8.25h19.5M2.25 9h19.5m-16.5 5.25h6m-6 2.25h3m-3.75 3h15a2.25 2.25 0 002.25-2.25V6.75A2.25 2.25 0 0019.5 4.5h-15a2.25 2.25 0 00-2.25 2.25v10.5A2.25 2.25 0 004.5 19.5z" />
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">No subscription plans</h3>
                    <p class="mt-1 text-sm text-gray-500">Get started by creating a new subscription plan.</p>
                    <div class="mt-6">
                        <button type="button" class="inline-flex items-center rounded-md bg-blue-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600">
                            <svg class="-ml-0.5 mr-1.5 h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                <path d="M10.75 4.75a.75.75 0 00-1.5 0v4.5h-4.5a.75.75 0 000 1.5h4.5v4.5a.75.75 0 001.5 0v-4.5h4.5a.75.75 0 000-1.5h-4.5v-4.5z" />
                            </svg>
                            Add Plan
                        </button>
                    </div>
                </div>
            @endif

            <!-- Plan Comparison Table -->
            @if($plans->isNotEmpty())
                <div class="mt-12">
                    <div class="bg-white shadow rounded-lg overflow-hidden">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h2 class="text-lg font-medium text-gray-900">Plan Comparison</h2>
                        </div>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Plan</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Job Posts</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Featured Jobs</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Applications</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subscribers</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    @foreach($plans as $plan)
                                        <tr class="hover:bg-gray-50">
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="flex items-center">
                                                    <div>
                                                        <div class="text-sm font-medium text-gray-900">{{ $plan->name }}</div>
                                                        <div class="text-sm text-gray-500">{{ Str::limit($plan->description, 50) }}</div>
                                                    </div>
                                                    @if($plan->is_popular)
                                                        <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                            Popular
                                                        </span>
                                                    @endif
                                                </div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                @if($plan->price > 0)
                                                    <div class="font-medium">${{ number_format($plan->price, 2) }}</div>
                                                    <div class="text-gray-500">per {{ $plan->billing_cycle }}</div>
                                                @else
                                                    <div class="font-medium text-green-600">Free</div>
                                                @endif
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                {{ $plan->job_posts_limit === -1 ? 'Unlimited' : ($plan->job_posts_limit ?? 'N/A') }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                {{ $plan->featured_jobs_limit === -1 ? 'Unlimited' : ($plan->featured_jobs_limit ?? 'N/A') }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                {{ $plan->applications_limit === -1 ? 'Unlimited' : ($plan->applications_limit ?? 'N/A') }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                <div class="font-medium">{{ $plan->subscribers_count ?? 0 }}</div>
                                                <div class="text-gray-500">${{ number_format($plan->total_revenue ?? 0, 2) }} revenue</div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                @if($plan->status === 'active')
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                        <svg class="w-1.5 h-1.5 mr-1.5" fill="currentColor" viewBox="0 0 8 8">
                                                            <circle cx="4" cy="4" r="3" />
                                                        </svg>
                                                        Active
                                                    </span>
                                                @else
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                                        <svg class="w-1.5 h-1.5 mr-1.5" fill="currentColor" viewBox="0 0 8 8">
                                                            <circle cx="4" cy="4" r="3" />
                                                        </svg>
                                                        Inactive
                                                    </span>
                                                @endif
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection