<?php $__env->startSection('title', 'Job Management'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid px-6 py-8">
    <!-- Modern Header -->
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8">
        <div class="flex-1 min-w-0">
            <h1 class="text-3xl font-bold text-gray-900 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Job Management
            </h1>
            <p class="mt-2 text-lg text-gray-600">
                Manage and moderate all job postings with advanced filtering and real-time updates
            </p>
        </div>

        <div class="mt-4 flex flex-col sm:flex-row sm:items-center space-y-3 sm:space-y-0 sm:space-x-3 lg:mt-0">
            <!-- Export Jobs -->
            <a href="<?php echo e(route('admin.analytics.export', 'jobs')); ?>" class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200" title="Export all jobs to CSV">
                <svg class="-ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5M16.5 12L12 16.5m0 0L7.5 12m4.5 4.5V3" />
                </svg>
                Export All
            </a>

            <!-- Refresh -->
            <button type="button" onclick="refreshData()" class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200" title="Refresh Data">
                <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99" />
                </svg>
            </button>
        </div>
    </div>

    <!-- Livewire Jobs Table Component -->
    <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('admin.jobs-table');

$__html = app('livewire')->mount($__name, $__params, 'lw-3245255573-0', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
// Wait for DOM to be ready
document.addEventListener('DOMContentLoaded', function() {
    initializeDropdowns();
    initializeModals();
});

// Dropdown functionality
function initializeDropdowns() {
    console.log('Initializing job dropdowns...'); // Debug log

    // Close dropdowns when clicking outside
    document.addEventListener('click', function(event) {
        // Check if click is outside any dropdown button or dropdown menu
        const isDropdownButton = event.target.closest('button[onclick*="toggleJobActions"]');
        const isDropdownMenu = event.target.closest('[id^="jobActions"]');

        if (!isDropdownButton && !isDropdownMenu) {
            const allDropdowns = document.querySelectorAll('[id^="jobActions"]');
            console.log('Closing all job dropdowns, found:', allDropdowns.length); // Debug log
            allDropdowns.forEach(dd => dd.classList.add('hidden'));
        }
    });

    // Also add event delegation for dynamically added buttons
    document.addEventListener('click', function(event) {
        const button = event.target.closest('button[onclick*="toggleJobActions"]');
        if (button) {
            event.preventDefault();
            event.stopPropagation();

            // Extract job ID from onclick attribute
            const onclickAttr = button.getAttribute('onclick');
            const jobIdMatch = onclickAttr.match(/toggleJobActions\((\d+)\)/);
            if (jobIdMatch) {
                const jobId = jobIdMatch[1];
                console.log('Job button clicked for job:', jobId); // Debug log
                window.toggleJobActions(parseInt(jobId));
            }
        }
    });
}

// Global dropdown toggle function - MUST be on window object
window.toggleJobActions = function(jobId) {
    console.log('Toggling dropdown for job:', jobId); // Debug log

    const dropdown = document.getElementById('jobActions' + jobId);
    const allDropdowns = document.querySelectorAll('[id^="jobActions"]');

    if (!dropdown) {
        console.error('Dropdown not found for job:', jobId);
        return;
    }

    // Close all other dropdowns first
    allDropdowns.forEach(dd => {
        if (dd.id !== 'jobActions' + jobId) {
            dd.classList.add('hidden');
        }
    });

    // Toggle current dropdown
    dropdown.classList.toggle('hidden');

    console.log('Dropdown visibility:', !dropdown.classList.contains('hidden')); // Debug log
};

// Modal functions
function initializeModals() {
    window.approveJob = function(jobId) {
        if (confirm('Are you sure you want to approve this job?')) {
            fetch(`/admin/jobs/${jobId}/approve`, {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Accept': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('Job approved successfully!', 'success');
                    if (typeof Livewire !== 'undefined') {
                        Livewire.dispatch('refreshComponent');
                    } else {
                        window.location.reload();
                    }
                } else {
                    showNotification(data.message || 'Error approving job', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('Error approving job', 'error');
            });
        }
    };

    window.rejectJob = function(jobId) {
        if (confirm('Are you sure you want to reject this job?')) {
            fetch(`/admin/jobs/${jobId}/reject`, {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Accept': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('Job rejected successfully!', 'success');
                    if (typeof Livewire !== 'undefined') {
                        Livewire.dispatch('refreshComponent');
                    } else {
                        window.location.reload();
                    }
                } else {
                    showNotification(data.message || 'Error rejecting job', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('Error rejecting job', 'error');
            });
        }
    };

    window.featureJob = function(jobId) {
        if (confirm('Are you sure you want to feature this job?')) {
            fetch(`/admin/jobs/${jobId}/feature`, {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Accept': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('Job featured successfully!', 'success');
                    if (typeof Livewire !== 'undefined') {
                        Livewire.dispatch('refreshComponent');
                    } else {
                        window.location.reload();
                    }
                } else {
                    showNotification(data.message || 'Error featuring job', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('Error featuring job', 'error');
            });
        }
    };
}

// Utility functions
window.showNotification = function(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full ${
        type === 'success' ? 'bg-green-500 text-white' :
        type === 'error' ? 'bg-red-500 text-white' :
        'bg-blue-500 text-white'
    }`;
    notification.textContent = message;

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);

    // Remove after 3 seconds
    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, 3000);
};

window.refreshData = function() {
    if (typeof Livewire !== 'undefined') {
        Livewire.dispatch('refreshComponent');
        showNotification('Data refreshed', 'success');
    } else {
        window.location.reload();
    }
};
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\laravel\rectra\resources\views/admin/jobs/livewire-index.blade.php ENDPATH**/ ?>