<?php

namespace App\Livewire;

use App\Models\Job;
use Livewire\Component;
use Livewire\WithPagination;
use Illuminate\Support\Facades\Auth;

class JobSearch extends Component
{
    use WithPagination;

    public $search = '';
    public $location = '';
    public $jobType = '';
    public $minSalary = '';
    public $remote = false;
    public $featured = false;
    public $sortBy = 'created_at';
    public $sortDirection = 'desc';
    public $perPage = 10;

    protected $queryString = [
        'search' => ['except' => ''],
        'location' => ['except' => ''],
        'jobType' => ['except' => ''],
        'minSalary' => ['except' => ''],
        'remote' => ['except' => false],
        'featured' => ['except' => false],
        'sortBy' => ['except' => 'created_at'],
        'sortDirection' => ['except' => 'desc'],
    ];

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function updatingLocation()
    {
        $this->resetPage();
    }

    public function updatingJobType()
    {
        $this->resetPage();
    }

    public function updatingMinSalary()
    {
        $this->resetPage();
    }

    public function updatingRemote()
    {
        $this->resetPage();
    }

    public function updatingFeatured()
    {
        $this->resetPage();
    }

    public function sortBy($field)
    {
        if ($this->sortBy === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortBy = $field;
            $this->sortDirection = 'asc';
        }
        $this->resetPage();
    }

    public function clearFilters()
    {
        $this->search = '';
        $this->location = '';
        $this->jobType = '';
        $this->minSalary = '';
        $this->remote = false;
        $this->featured = false;
        $this->sortBy = 'created_at';
        $this->sortDirection = 'desc';
        $this->resetPage();
    }

    public function saveSearch()
    {
        if (!Auth::check()) {
            session()->flash('error', 'Please login to save searches.');
            return;
        }

        Auth::user()->savedSearches()->create([
            'name' => 'Search: ' . ($this->search ?: 'All Jobs'),
            'filters' => [
                'search' => $this->search,
                'location' => $this->location,
                'job_type' => $this->jobType,
                'min_salary' => $this->minSalary,
                'remote' => $this->remote,
                'featured' => $this->featured,
            ],
        ]);

        session()->flash('success', 'Search saved successfully!');
    }

    public function getJobsProperty()
    {
        $query = Job::query()->with(['employer.user', 'employer.company'])
            ->where('status', 'active')
            ->where('expires_at', '>', now());

        // Search filter
        if ($this->search) {
            $query->where(function ($q) {
                $q->where('title', 'like', '%' . $this->search . '%')
                  ->orWhere('description', 'like', '%' . $this->search . '%')
                  ->orWhere('requirements', 'like', '%' . $this->search . '%');
            });
        }

        // Location filter
        if ($this->location) {
            $query->where('location', 'like', '%' . $this->location . '%');
        }

        // Job type filter
        if ($this->jobType) {
            $query->where('job_type', $this->jobType);
        }

        // Minimum salary filter
        if ($this->minSalary) {
            $query->where('salary_min', '>=', $this->minSalary);
        }

        // Remote filter
        if ($this->remote) {
            $query->where('remote', true);
        }

        // Featured filter
        if ($this->featured) {
            $query->where('featured', true);
        }

        // Sorting
        $query->orderBy($this->sortBy, $this->sortDirection);

        return $query->paginate($this->perPage);
    }

    public function render()
    {
        return view('livewire.job-search', [
            'jobs' => $this->jobs,
            'jobTypes' => ['full-time', 'part-time', 'contract', 'freelance', 'internship'],
        ]);
    }
}
