<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('company_reviews', function (Blueprint $table) {
            $table->id();
            $table->foreignId('job_seeker_profile_id')->constrained()->onDelete('cascade');
            $table->foreignId('employer_profile_id')->constrained()->onDelete('cascade');
            $table->integer('rating'); // 1-5 stars
            $table->string('title');
            $table->text('body');
            $table->json('pros')->nullable();
            $table->json('cons')->nullable();
            $table->string('employment_status')->nullable(); // Current/Former employee
            $table->string('job_title')->nullable();
            $table->boolean('is_approved')->default(false);
            $table->boolean('is_anonymous')->default(false);
            $table->timestamp('approved_at')->nullable();
            $table->timestamps();
            
            $table->index(['employer_profile_id', 'is_approved']);
            $table->index(['rating', 'is_approved']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('company_reviews');
    }
};