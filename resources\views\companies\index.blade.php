@extends('layouts.app')

@section('title', 'Companies')

@section('content')
<div class="bg-white">
    <!-- Header Section -->
    <div class="bg-gradient-to-r from-blue-600 to-blue-800 text-white py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <h1 class="text-4xl font-bold mb-4">Discover Top Companies</h1>
                <p class="text-xl text-blue-100 mb-8">Find your next career opportunity with leading employers</p>
                
                <!-- Search Form -->
                <form method="GET" action="{{ route('companies.index') }}" class="max-w-2xl mx-auto">
                    <div class="flex flex-col sm:flex-row gap-4">
                        <div class="flex-1">
                            <input type="text" name="search" value="{{ request('search') }}" 
                                   placeholder="Search companies, industries, locations..." 
                                   class="w-full px-4 py-3 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-300">
                        </div>
                        <button type="submit" class="px-8 py-3 bg-blue-500 hover:bg-blue-400 text-white font-semibold rounded-lg transition-colors duration-200">
                            Search
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Filters Section -->
    <div class="bg-gray-50 border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <form method="GET" action="{{ route('companies.index') }}" class="flex flex-wrap gap-4 items-center">
                <input type="hidden" name="search" value="{{ request('search') }}">
                
                <!-- Industry Filter -->
                <div class="flex-1 min-w-48">
                    <select name="industry" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">All Industries</option>
                        @foreach($industries as $industry)
                            <option value="{{ $industry }}" {{ request('industry') == $industry ? 'selected' : '' }}>
                                {{ $industry }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <!-- Location Filter -->
                <div class="flex-1 min-w-48">
                    <input type="text" name="location" value="{{ request('location') }}" 
                           placeholder="Location" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>

                <!-- Sort Filter -->
                <div class="flex-1 min-w-48">
                    <select name="sort" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="name" {{ request('sort') == 'name' ? 'selected' : '' }}>Company Name</option>
                        <option value="jobs" {{ request('sort') == 'jobs' ? 'selected' : '' }}>Most Jobs</option>
                        <option value="newest" {{ request('sort') == 'newest' ? 'selected' : '' }}>Newest</option>
                    </select>
                </div>

                <button type="submit" class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-200">
                    Apply Filters
                </button>
                
                @if(request()->hasAny(['search', 'industry', 'location', 'sort']))
                    <a href="{{ route('companies.index') }}" class="px-6 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors duration-200">
                        Clear All
                    </a>
                @endif
            </form>
        </div>
    </div>

    <!-- Results Section -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Results Count -->
        <div class="mb-6">
            <p class="text-gray-600">
                Showing {{ $companies->firstItem() ?? 0 }} - {{ $companies->lastItem() ?? 0 }} of {{ $companies->total() }} companies
            </p>
        </div>

        @if($companies->count() > 0)
            <!-- Companies Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                @foreach($companies as $company)
                    <div class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 overflow-hidden">
                        <div class="p-6">
                            <!-- Company Logo -->
                            <div class="flex items-center mb-4">
                                @if($company->employerProfile->getFirstMediaUrl('logo'))
                                    <img src="{{ $company->employerProfile->getFirstMediaUrl('logo') }}" 
                                         alt="{{ $company->employerProfile->company_name }} logo" 
                                         class="w-16 h-16 object-contain rounded-lg mr-4">
                                @else
                                    <div class="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center mr-4">
                                        <span class="text-2xl font-bold text-gray-500">
                                            {{ substr($company->employerProfile->company_name, 0, 1) }}
                                        </span>
                                    </div>
                                @endif
                                
                                <div class="flex-1">
                                    <h3 class="text-lg font-semibold text-gray-900 mb-1">
                                        <a href="{{ route('companies.show', $company) }}" class="hover:text-blue-600 transition-colors duration-200">
                                            {{ $company->employerProfile->company_name }}
                                        </a>
                                    </h3>
                                    @if($company->employerProfile->industry)
                                        <p class="text-sm text-gray-600">{{ $company->employerProfile->industry }}</p>
                                    @endif
                                </div>
                            </div>

                            <!-- Company Info -->
                            @if($company->employerProfile->location)
                                <div class="flex items-center text-sm text-gray-600 mb-2">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    </svg>
                                    {{ $company->employerProfile->location }}
                                </div>
                            @endif

                            @if($company->employerProfile->company_size)
                                <div class="flex items-center text-sm text-gray-600 mb-4">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                    </svg>
                                    {{ $company->employerProfile->company_size }} employees
                                </div>
                            @endif

                            <!-- Company Description -->
                            @if($company->employerProfile->company_description)
                                <p class="text-sm text-gray-600 mb-4 line-clamp-3">
                                    {{ Str::limit($company->employerProfile->company_description, 120) }}
                                </p>
                            @endif

                            <!-- Stats -->
                            <div class="flex justify-between items-center pt-4 border-t border-gray-200">
                                <div class="text-sm text-gray-600">
                                    <span class="font-semibold text-blue-600">{{ $company->employerProfile->jobs->count() }}</span> 
                                    {{ Str::plural('job', $company->employerProfile->jobs->count()) }}
                                </div>
                                
                                <a href="{{ route('companies.show', $company) }}" 
                                   class="text-blue-600 hover:text-blue-800 font-medium text-sm transition-colors duration-200">
                                    View Company →
                                </a>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- Pagination -->
            <div class="flex justify-center">
                {{ $companies->appends(request()->query())->links() }}
            </div>
        @else
            <!-- No Results -->
            <div class="text-center py-12">
                <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                </svg>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No companies found</h3>
                <p class="text-gray-600 mb-4">Try adjusting your search criteria or browse all companies.</p>
                <a href="{{ route('companies.index') }}" class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-200">
                    View All Companies
                </a>
            </div>
        @endif
    </div>
</div>
@endsection

@push('styles')
<style>
.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style>
@endpush