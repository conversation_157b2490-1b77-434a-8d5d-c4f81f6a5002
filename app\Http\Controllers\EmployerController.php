<?php

namespace App\Http\Controllers;

use App\Models\Job;
use App\Models\JobApplication;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class EmployerController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('role:employer');
    }

    /**
     * Display the employer dashboard.
     */
    public function dashboard()
    {
        $user = Auth::user();
        $employerProfile = $user->employerProfile;

        if (!$employerProfile) {
            return redirect()->route('profile.edit')
                ->with('warning', 'Please complete your company profile first.');
        }

        // Get statistics
        $stats = $this->getDashboardStats($employerProfile);
        
        // Get recent activities
        $recentJobs = $employerProfile->jobs()
            ->with('media')
            ->latest()
            ->limit(5)
            ->get();

        $recentApplications = JobApplication::whereHas('job', function ($query) use ($employerProfile) {
                $query->where('employer_profile_id', $employerProfile->id);
            })
            ->with(['user.jobSeekerProfile', 'job'])
            ->latest()
            ->limit(10)
            ->get();

        // Get analytics data for charts
        $analyticsData = $this->getAnalyticsData($employerProfile);

        return view('employer.dashboard', compact(
            'stats',
            'recentJobs',
            'recentApplications',
            'analyticsData'
        ));
    }

    /**
     * Display job listings management.
     */
    public function jobs(Request $request)
    {
        $user = Auth::user();
        $employerProfile = $user->employerProfile;

        $query = $employerProfile->jobs()
            ->with(['media', 'applications'])
            ->withCount('applications');

        // Filter by status
        if ($request->filled('status')) {
            if ($request->status === 'active') {
                $query->where('status', 'active')
                      ->where('expires_at', '>', now());
            } elseif ($request->status === 'expired') {
                $query->where(function ($q) {
                    $q->where('status', '!=', 'active')
                      ->orWhere('expires_at', '<=', now());
                });
            } else {
                $query->where('status', $request->status);
            }
        }

        // Filter by job type
        if ($request->filled('type')) {
            $query->where('job_type', $request->type);
        }

        // Search by title or description
        if ($request->filled('search')) {
            $searchTerm = $request->search;
            $query->where(function ($q) use ($searchTerm) {
                $q->where('title', 'like', "%{$searchTerm}%")
                  ->orWhere('description', 'like', "%{$searchTerm}%");
            });
        }

        // Sort options
        $sortBy = $request->get('sort', 'created_at');
        $sortOrder = $request->get('order', 'desc');

        switch ($sortBy) {
            case 'title':
                $query->orderBy('title', $sortOrder);
                break;
            case 'applications':
                $query->orderBy('applications_count', $sortOrder);
                break;
            case 'expires_at':
                $query->orderBy('expires_at', $sortOrder);
                break;
            case 'views':
                $query->orderBy('views_count', $sortOrder);
                break;
            default: // created_at
                $query->orderBy('created_at', $sortOrder);
        }

        $jobs = $query->paginate(12)->withQueryString();

        // Get filter options
        $statuses = ['draft', 'active', 'paused', 'closed'];
        $jobTypes = ['full-time', 'part-time', 'contract', 'freelance', 'internship'];

        // Get counts for status filter
        $statusCounts = [
            'all' => $employerProfile->jobs()->count(),
            'active' => $employerProfile->jobs()
                ->where('status', 'active')
                ->where('expires_at', '>', now())
                ->count(),
            'draft' => $employerProfile->jobs()->where('status', 'draft')->count(),
            'paused' => $employerProfile->jobs()->where('status', 'paused')->count(),
            'closed' => $employerProfile->jobs()->where('status', 'closed')->count(),
            'expired' => $employerProfile->jobs()
                ->where(function ($q) {
                    $q->where('status', '!=', 'active')
                      ->orWhere('expires_at', '<=', now());
                })
                ->count()
        ];

        return view('employer.jobs.index', compact(
            'jobs',
            'statuses',
            'jobTypes',
            'statusCounts'
        ));
    }

    /**
     * Show the form for creating a new job.
     */
    public function createJob()
    {
        $user = Auth::user();
        $employerProfile = $user->employerProfile;

        if (!$employerProfile) {
            return redirect()->route('profile.edit')
                ->with('error', 'Please complete your company profile first.');
        }

        return view('employer.jobs.create');
    }

    /**
     * Display job applications.
     */
    public function applications(Request $request)
    {
        $user = Auth::user();
        $employerProfile = $user->employerProfile;

        $query = JobApplication::whereHas('job', function ($q) use ($employerProfile) {
                $q->where('employer_profile_id', $employerProfile->id);
            })
            ->with(['user.jobSeekerProfile', 'job']);

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by job
        if ($request->filled('job_id')) {
            $query->where('job_id', $request->job_id);
        }

        // Filter by application date
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        // Search by applicant name or email
        if ($request->filled('search')) {
            $searchTerm = $request->search;
            $query->whereHas('user', function ($q) use ($searchTerm) {
                $q->where('name', 'like', "%{$searchTerm}%")
                  ->orWhere('email', 'like', "%{$searchTerm}%");
            });
        }

        // Sort options
        $sortBy = $request->get('sort', 'created_at');
        $sortOrder = $request->get('order', 'desc');

        switch ($sortBy) {
            case 'applicant':
                $query->join('users', 'job_applications.user_id', '=', 'users.id')
                      ->orderBy('users.name', $sortOrder)
                      ->select('job_applications.*');
                break;
            case 'job':
                $query->join('job_listings', 'job_applications.job_id', '=', 'job_listings.id')
                      ->orderBy('job_listings.title', $sortOrder)
                      ->select('job_applications.*');
                break;
            case 'status':
                $query->orderBy('status', $sortOrder);
                break;
            default: // created_at
                $query->orderBy('created_at', $sortOrder);
        }

        $applications = $query->paginate(15)->withQueryString();

        // Get filter options
        $jobs = $employerProfile->jobs()
            ->select('id', 'title')
            ->orderBy('title')
            ->get();

        $statuses = ['pending', 'reviewed', 'shortlisted', 'interviewed', 'offered', 'hired', 'rejected'];

        // Get counts for status filter
        $statusCounts = [
            'all' => JobApplication::whereHas('job', function ($q) use ($employerProfile) {
                $q->where('employer_profile_id', $employerProfile->id);
            })->count(),
            'pending' => JobApplication::whereHas('job', function ($q) use ($employerProfile) {
                $q->where('employer_profile_id', $employerProfile->id);
            })->where('status', 'pending')->count(),
            'reviewed' => JobApplication::whereHas('job', function ($q) use ($employerProfile) {
                $q->where('employer_profile_id', $employerProfile->id);
            })->where('status', 'reviewed')->count(),
            'shortlisted' => JobApplication::whereHas('job', function ($q) use ($employerProfile) {
                $q->where('employer_profile_id', $employerProfile->id);
            })->where('status', 'shortlisted')->count(),
            'hired' => JobApplication::whereHas('job', function ($q) use ($employerProfile) {
                $q->where('employer_profile_id', $employerProfile->id);
            })->where('status', 'hired')->count(),
            'rejected' => JobApplication::whereHas('job', function ($q) use ($employerProfile) {
                $q->where('employer_profile_id', $employerProfile->id);
            })->where('status', 'rejected')->count(),
        ];

        return view('employer.applications.index', compact(
            'applications',
            'jobs',
            'statuses',
            'statusCounts'
        ));
    }

    /**
     * Display the employer profile page.
     */
    public function profile()
    {
        $user = Auth::user();
        $employerProfile = $user->employerProfile;

        if (!$employerProfile) {
            return redirect()->route('profile.edit')
                ->with('warning', 'Please complete your company profile first.');
        }

        return view('employer.profile', compact('employerProfile'));
    }

    /**
     * Display a specific job application.
     */
    public function showApplication(JobApplication $application)
    {
        // Verify the application belongs to this employer
        $user = Auth::user();
        $employerProfile = $user->employerProfile;
        
        if ($application->job->employer_profile_id !== $employerProfile->id) {
            abort(403, 'Unauthorized access to this application.');
        }

        $application->load([
            'user.jobSeekerProfile',
            'job',
            'notes' => function ($query) {
                $query->latest();
            }
        ]);

        // Mark as reviewed if it's pending
        if ($application->status === 'pending') {
            $application->update(['status' => 'reviewed']);
        }

        return view('employer.applications.show', compact('application'));
    }

    /**
     * Update application status.
     */
    public function updateApplicationStatus(Request $request, JobApplication $application)
    {
        // Verify the application belongs to this employer
        $user = Auth::user();
        $employerProfile = $user->employerProfile;
        
        if ($application->job->employer_profile_id !== $employerProfile->id) {
            abort(403, 'Unauthorized access to this application.');
        }

        $request->validate([
            'status' => 'required|in:pending,reviewed,shortlisted,interviewed,offered,hired,rejected',
            'notes' => 'nullable|string|max:1000',
        ]);

        $application->update(['status' => $request->status]);

        // Add note if provided
        if ($request->filled('notes')) {
            $application->notes()->create([
                'user_id' => $user->id,
                'content' => $request->notes,
            ]);
        }

        // Send notification to applicant (implement as needed)
        // $this->sendStatusUpdateNotification($application);

        return back()->with('success', 'Application status updated successfully.');
    }

    /**
     * Bulk update application statuses.
     */
    public function bulkUpdateApplications(Request $request)
    {
        $request->validate([
            'application_ids' => 'required|array',
            'application_ids.*' => 'exists:job_applications,id',
            'status' => 'required|in:reviewed,shortlisted,interviewed,offered,hired,rejected',
        ]);

        $user = Auth::user();
        $employerProfile = $user->employerProfile;
        
        // Verify all applications belong to this employer
        $applications = JobApplication::whereIn('id', $request->application_ids)
            ->whereHas('job', function ($q) use ($employerProfile) {
                $q->where('employer_profile_id', $employerProfile->id);
            })
            ->get();

        if ($applications->count() !== count($request->application_ids)) {
            return back()->with('error', 'Some applications do not belong to your company.');
        }

        // Update all applications
        JobApplication::whereIn('id', $request->application_ids)
            ->update(['status' => $request->status]);

        $message = count($request->application_ids) . ' application(s) updated to ' . $request->status . '.';
        
        return back()->with('success', $message);
    }

    /**
     * Display company analytics.
     */
    public function analytics(Request $request)
    {
        $user = Auth::user();
        $employerProfile = $user->employerProfile;

        $period = $request->get('period', '30'); // days
        $startDate = now()->subDays($period);

        // Job performance analytics
        $jobAnalytics = $this->getJobAnalytics($employerProfile, $startDate);
        
        // Application analytics
        $applicationAnalytics = $this->getApplicationAnalytics($employerProfile, $startDate);
        
        // Company profile analytics
        $profileAnalytics = $this->getProfileAnalytics($employerProfile, $startDate);

        return view('employer.analytics', compact(
            'jobAnalytics',
            'applicationAnalytics',
            'profileAnalytics',
            'period'
        ));
    }

    /**
     * Display subscription management.
     */
    public function subscription()
    {
        $user = Auth::user();
        $subscription = $user->subscription;
        
        // Get available plans
        $plans = \App\Models\SubscriptionPlan::where('is_active', true)
            ->where('user_type', 'employer')
            ->orderBy('price')
            ->get();

        return view('employer.subscription', compact('subscription', 'plans'));
    }

    /**
     * Get dashboard statistics.
     */
    private function getDashboardStats($employerProfile)
    {
        $stats = [
            'total_jobs' => $employerProfile->jobs()->count(),
            'active_jobs' => $employerProfile->jobs()
                ->where('status', 'active')
                ->where('expires_at', '>', now())
                ->count(),
            'total_applications' => JobApplication::whereHas('job', function ($q) use ($employerProfile) {
                $q->where('employer_profile_id', $employerProfile->id);
            })->count(),
            'pending_applications' => JobApplication::whereHas('job', function ($q) use ($employerProfile) {
                $q->where('employer_profile_id', $employerProfile->id);
            })->where('status', 'pending')->count(),
            'total_views' => $employerProfile->jobs()->sum('views_count'),
            'hired_candidates' => JobApplication::whereHas('job', function ($q) use ($employerProfile) {
                $q->where('employer_profile_id', $employerProfile->id);
            })->where('status', 'hired')->count(),
        ];

        // Calculate percentage changes (compared to previous period)
        $previousPeriodStart = now()->subDays(60);
        $currentPeriodStart = now()->subDays(30);

        $stats['applications_change'] = $this->calculatePercentageChange(
            JobApplication::whereHas('job', function ($q) use ($employerProfile) {
                $q->where('employer_profile_id', $employerProfile->id);
            })->whereBetween('created_at', [$previousPeriodStart, $currentPeriodStart])->count(),
            JobApplication::whereHas('job', function ($q) use ($employerProfile) {
                $q->where('employer_profile_id', $employerProfile->id);
            })->where('created_at', '>=', $currentPeriodStart)->count()
        );

        return $stats;
    }

    /**
     * Get analytics data for charts.
     */
    private function getAnalyticsData($employerProfile)
    {
        $last30Days = collect(range(0, 29))->map(function ($i) {
            return now()->subDays($i)->format('Y-m-d');
        })->reverse()->values();

        // Applications per day
        $applicationsPerDay = JobApplication::whereHas('job', function ($q) use ($employerProfile) {
                $q->where('employer_profile_id', $employerProfile->id);
            })
            ->where('created_at', '>=', now()->subDays(30))
            ->selectRaw('DATE(created_at) as date, COUNT(*) as count')
            ->groupBy('date')
            ->pluck('count', 'date');

        // Job views per day
        $viewsPerDay = $employerProfile->jobs()
            ->where('created_at', '>=', now()->subDays(30))
            ->selectRaw('DATE(created_at) as date, SUM(views_count) as views')
            ->groupBy('date')
            ->pluck('views', 'date');

        return [
            'dates' => $last30Days,
            'applications' => $last30Days->map(fn($date) => $applicationsPerDay->get($date, 0)),
            'views' => $last30Days->map(fn($date) => $viewsPerDay->get($date, 0)),
        ];
    }

    /**
     * Get job analytics.
     */
    private function getJobAnalytics($employerProfile, $startDate)
    {
        return [
            'most_viewed_jobs' => $employerProfile->jobs()
                ->where('created_at', '>=', $startDate)
                ->orderBy('views_count', 'desc')
                ->limit(10)
                ->get(['id', 'title', 'views_count']),
            'jobs_by_type' => $employerProfile->jobs()
                ->where('created_at', '>=', $startDate)
                ->selectRaw('type, COUNT(*) as count')
                ->groupBy('type')
                ->pluck('count', 'type'),
            'jobs_by_status' => $employerProfile->jobs()
                ->selectRaw('status, COUNT(*) as count')
                ->groupBy('status')
                ->pluck('count', 'status'),
        ];
    }

    /**
     * Get application analytics.
     */
    private function getApplicationAnalytics($employerProfile, $startDate)
    {
        return [
            'applications_by_status' => JobApplication::whereHas('job', function ($q) use ($employerProfile) {
                    $q->where('employer_profile_id', $employerProfile->id);
                })
                ->where('created_at', '>=', $startDate)
                ->selectRaw('status, COUNT(*) as count')
                ->groupBy('status')
                ->pluck('count', 'status'),
            'top_performing_jobs' => Job::where('employer_profile_id', $employerProfile->id)
                ->withCount(['applications' => function ($q) use ($startDate) {
                    $q->where('created_at', '>=', $startDate);
                }])
                ->orderBy('applications_count', 'desc')
                ->limit(10)
                ->get(['id', 'title', 'applications_count']),
        ];
    }

    /**
     * Get profile analytics.
     */
    private function getProfileAnalytics($employerProfile, $startDate)
    {
        return [
            'profile_views' => $employerProfile->views_count ?? 0,
            'company_followers' => $employerProfile->followers_count ?? 0,
        ];
    }

    /**
     * Calculate percentage change.
     */
    private function calculatePercentageChange($previous, $current)
    {
        if ($previous == 0) {
            return $current > 0 ? 100 : 0;
        }
        
        return round((($current - $previous) / $previous) * 100, 1);
    }

    /**
     * Display a listing of companies.
     */
    public function companies(Request $request)
    {
        $query = User::whereHas('employerProfile')
            ->with(['employerProfile.media', 'employerProfile.jobs'])
            ->whereHas('employerProfile', function ($q) {
                $q->whereNotNull('company_name');
            });

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->whereHas('employerProfile', function ($q) use ($search) {
                $q->where('company_name', 'like', "%{$search}%")
                  ->orWhere('company_description', 'like', "%{$search}%")
                  ->orWhere('industry', 'like', "%{$search}%")
                  ->orWhere('location', 'like', "%{$search}%");
            });
        }

        // Filter by industry
        if ($request->filled('industry')) {
            $query->whereHas('employerProfile', function ($q) use ($request) {
                $q->where('industry', $request->get('industry'));
            });
        }

        // Filter by location
        if ($request->filled('location')) {
            $query->whereHas('employerProfile', function ($q) use ($request) {
                $q->where('location', 'like', '%' . $request->get('location') . '%');
            });
        }

        // Sort options
        $sort = $request->get('sort', 'name');
        switch ($sort) {
            case 'jobs':
                $query->withCount('employerProfile.jobs as jobs_count')
                      ->orderBy('jobs_count', 'desc');
                break;
            case 'newest':
                $query->latest();
                break;
            case 'name':
            default:
                $query->whereHas('employerProfile', function ($q) {
                    $q->orderBy('company_name');
                });
                break;
        }

        $companies = $query->paginate(12);

        // Get filter options
        $industries = User::whereHas('employerProfile')
            ->join('employer_profiles', 'users.id', '=', 'employer_profiles.user_id')
            ->whereNotNull('employer_profiles.industry')
            ->distinct()
            ->pluck('employer_profiles.industry')
            ->sort();

        return view('companies.index', compact('companies', 'industries'));
    }

    /**
     * Display the specified company.
     */
    public function showCompany(User $company)
    {
        $company->load([
            'employerProfile.media',
            'employerProfile.jobs' => function ($query) {
                $query->where('status', 'active')
                      ->where('expires_at', '>', now())
                      ->latest()
                      ->limit(6);
            }
        ]);

        if (!$company->employerProfile) {
            abort(404, 'Company not found.');
        }

        $stats = [
            'total_jobs' => $company->employerProfile->jobs()->count(),
            'active_jobs' => $company->employerProfile->jobs()
                ->where('status', 'active')
                ->where('expires_at', '>', now())
                ->count(),
            'total_applications' => JobApplication::whereHas('job', function ($query) use ($company) {
                $query->where('employer_profile_id', $company->employerProfile->id);
            })->count(),
        ];

        return view('companies.show', compact('company', 'stats'));
    }

    /**
     * Display jobs for the specified company.
     */
    public function companyJobs(User $company, Request $request)
    {
        if (!$company->employerProfile) {
            abort(404, 'Company not found.');
        }

        $query = $company->employerProfile->jobs()
            ->where('status', 'active')
            ->where('expires_at', '>', now());

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('requirements', 'like', "%{$search}%");
            });
        }

        // Filter by job type
        if ($request->filled('type')) {
            $query->where('job_type', $request->get('type'));
        }

        // Filter by location
        if ($request->filled('location')) {
            $query->where('location', 'like', '%' . $request->get('location') . '%');
        }

        // Sort options
        $sort = $request->get('sort', 'latest');
        switch ($sort) {
            case 'salary_high':
                $query->orderBy('salary_max', 'desc');
                break;
            case 'salary_low':
                $query->orderBy('salary_min', 'asc');
                break;
            case 'title':
                $query->orderBy('title');
                break;
            case 'latest':
            default:
                $query->latest();
                break;
        }

        $jobs = $query->paginate(10);

        return view('companies.jobs', compact('company', 'jobs'));
    }

    /**
     * Display reviews for the specified company.
     */
    public function companyReviews(User $company, Request $request)
    {
        if (!$company->employerProfile) {
            abort(404, 'Company not found.');
        }

        $query = $company->employerProfile->companyReviews()
            ->with('user.jobSeekerProfile')
            ->where('is_approved', true);

        // Sort options
        $sort = $request->get('sort', 'latest');
        switch ($sort) {
            case 'rating_high':
                $query->orderBy('overall_rating', 'desc');
                break;
            case 'rating_low':
                $query->orderBy('overall_rating', 'asc');
                break;
            case 'oldest':
                $query->oldest();
                break;
            case 'latest':
            default:
                $query->latest();
                break;
        }

        $reviews = $query->paginate(10);

        // Calculate average ratings
        $averageRatings = $company->employerProfile->companyReviews()
            ->where('is_approved', true)
            ->selectRaw('AVG(overall_rating) as overall, AVG(work_life_balance) as work_life, AVG(compensation) as compensation, AVG(career_opportunities) as career, AVG(management) as management, AVG(culture) as culture')
            ->first();

        $totalReviews = $company->employerProfile->companyReviews()
            ->where('is_approved', true)
            ->count();

        return view('companies.reviews', compact('company', 'reviews', 'averageRatings', 'totalReviews'));
    }
}