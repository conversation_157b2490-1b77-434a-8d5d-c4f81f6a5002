@extends('layouts.admin')

@section('title', 'Course Details - ' . $course->title)

@section('content')
<div class="max-w-6xl mx-auto">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">{{ $course->title }}</h1>
                <p class="mt-2 text-gray-600">Course details and management</p>
                <div class="mt-2 flex items-center space-x-4 text-sm text-gray-500">
                    <span class="flex items-center">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                        {{ $course->instructor->name ?? 'No instructor assigned' }}
                    </span>
                    <span class="flex items-center">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a1.414 1.414 0 01-2.828 0l-7-7A1.414 1.414 0 013 12V7a4 4 0 014-4z"></path>
                        </svg>
                        {{ ucfirst($course->category) }}
                    </span>
                    <span class="flex items-center">
                        @if($course->is_published)
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                Published
                            </span>
                        @else
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                Draft
                            </span>
                        @endif
                    </span>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{{ route('courses.show', $course) }}" target="_blank" class="inline-flex items-center px-4 py-2 border border-blue-300 rounded-md shadow-sm text-sm font-medium text-blue-700 bg-blue-50 hover:bg-blue-100">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-2M7 7l10 10M17 7v4m0 0h-4"></path>
                    </svg>
                    View Public Page
                </a>
                <a href="{{ route('admin.courses.edit', $course) }}" class="inline-flex items-center px-4 py-2 border border-purple-300 rounded-md shadow-sm text-sm font-medium text-purple-700 bg-purple-50 hover:bg-purple-100">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                    Edit Course
                </a>
                <a href="{{ route('admin.courses.index') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Back to Courses
                </a>
            </div>
        </div>
    </div>

    <!-- Course Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Total Enrollments</dt>
                        <dd class="text-lg font-medium text-gray-900">{{ number_format($courseStats['total_enrollments']) }}</dd>
                    </dl>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Completed</dt>
                        <dd class="text-lg font-medium text-gray-900">{{ number_format($courseStats['completed_enrollments']) }}</dd>
                    </dl>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Total Lessons</dt>
                        <dd class="text-lg font-medium text-gray-900">{{ number_format($courseStats['total_lessons']) }}</dd>
                    </dl>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Avg. Progress</dt>
                        <dd class="text-lg font-medium text-gray-900">{{ number_format($courseStats['average_progress'], 1) }}%</dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <!-- Course Content -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Course Information -->
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">Course Information</h2>
                
                @if($course->getFirstMediaUrl('thumbnail'))
                    <div class="mb-6">
                        <img src="{{ $course->getFirstMediaUrl('thumbnail') }}" alt="{{ $course->title }}" class="w-full h-48 object-cover rounded-lg">
                    </div>
                @endif

                <div class="space-y-4">
                    <div>
                        <h3 class="text-sm font-medium text-gray-700">Description</h3>
                        <div class="mt-1 prose max-w-none text-gray-900">
                            {!! nl2br(e($course->description)) !!}
                        </div>
                    </div>

                    @if($course->short_description)
                    <div>
                        <h3 class="text-sm font-medium text-gray-700">Short Description</h3>
                        <p class="mt-1 text-gray-900">{{ $course->short_description }}</p>
                    </div>
                    @endif

                    @if($course->learning_outcomes && count($course->learning_outcomes) > 0)
                    <div>
                        <h3 class="text-sm font-medium text-gray-700">Learning Outcomes</h3>
                        <ul class="mt-1 list-disc list-inside space-y-1 text-gray-900">
                            @foreach($course->learning_outcomes as $outcome)
                                <li>{{ $outcome }}</li>
                            @endforeach
                        </ul>
                    </div>
                    @endif

                    @if($course->prerequisites && count($course->prerequisites) > 0)
                    <div>
                        <h3 class="text-sm font-medium text-gray-700">Prerequisites</h3>
                        <ul class="mt-1 list-disc list-inside space-y-1 text-gray-900">
                            @foreach($course->prerequisites as $prerequisite)
                                <li>{{ $prerequisite }}</li>
                            @endforeach
                        </ul>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Course Lessons -->
            @if($course->lessons->count() > 0)
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">Course Curriculum ({{ $course->lessons->count() }} lessons)</h2>
                <div class="space-y-3">
                    @foreach($course->lessons as $lesson)
                    <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                                <span class="text-sm font-medium text-purple-600">{{ $loop->iteration }}</span>
                            </div>
                            <div>
                                <h3 class="font-medium text-gray-900">{{ $lesson->title }}</h3>
                                @if($lesson->description)
                                    <p class="text-sm text-gray-500">{{ Str::limit($lesson->description, 100) }}</p>
                                @endif
                            </div>
                        </div>
                        <div class="flex items-center space-x-2 text-sm text-gray-500">
                            @if($lesson->duration_minutes)
                                <span>{{ $lesson->duration_minutes }} min</span>
                            @endif
                            @if($lesson->is_published)
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    Published
                                </span>
                            @else
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                    Draft
                                </span>
                            @endif
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
            @endif
        </div>

        <!-- Sidebar -->
        <div class="lg:col-span-1 space-y-6">
            <!-- Course Details -->
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">Course Details</h2>
                <dl class="space-y-3">
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Category</dt>
                        <dd class="text-sm text-gray-900">{{ ucfirst($course->category) }}</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Difficulty Level</dt>
                        <dd class="text-sm text-gray-900">{{ ucfirst($course->difficulty_level) }}</dd>
                    </div>
                    @if($course->duration_hours)
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Duration</dt>
                        <dd class="text-sm text-gray-900">{{ $course->duration_hours }} hours</dd>
                    </div>
                    @endif
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Price</dt>
                        <dd class="text-sm text-gray-900">${{ number_format($course->price, 2) }}</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Created</dt>
                        <dd class="text-sm text-gray-900">{{ $course->created_at->format('M d, Y') }}</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Last Updated</dt>
                        <dd class="text-sm text-gray-900">{{ $course->updated_at->format('M d, Y') }}</dd>
                    </div>
                </dl>
            </div>

            <!-- Recent Enrollments -->
            @if($course->enrollments->count() > 0)
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">Recent Enrollments</h2>
                <div class="space-y-3">
                    @foreach($course->enrollments->take(5) as $enrollment)
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                            <span class="text-xs font-medium text-gray-700">{{ substr($enrollment->user->name, 0, 1) }}</span>
                        </div>
                        <div class="flex-1 min-w-0">
                            <p class="text-sm font-medium text-gray-900 truncate">{{ $enrollment->user->name }}</p>
                            <p class="text-xs text-gray-500">{{ $enrollment->created_at->diffForHumans() }}</p>
                        </div>
                        @if($enrollment->completed_at)
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                Completed
                            </span>
                        @else
                            <span class="text-xs text-gray-500">{{ number_format($enrollment->progress ?? 0) }}%</span>
                        @endif
                    </div>
                    @endforeach
                </div>
                @if($course->enrollments->count() > 5)
                    <div class="mt-4 text-center">
                        <a href="#" class="text-sm text-purple-600 hover:text-purple-500">View all enrollments</a>
                    </div>
                @endif
            </div>
            @endif
        </div>
    </div>
</div>
@endsection
