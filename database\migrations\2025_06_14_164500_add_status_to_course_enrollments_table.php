<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('course_enrollments', function (Blueprint $table) {
            $table->enum('status', ['active', 'completed', 'paused', 'dropped'])->default('active')->after('course_id');
            $table->integer('progress_percentage')->default(0)->after('progress');
            $table->boolean('certificate_issued')->default(false)->after('certificate_path');
            $table->timestamp('enrolled_at')->nullable()->after('last_accessed_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('course_enrollments', function (Blueprint $table) {
            $table->dropColumn(['status', 'progress_percentage', 'certificate_issued', 'enrolled_at']);
        });
    }
};
