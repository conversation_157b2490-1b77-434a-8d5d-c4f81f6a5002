<?php

namespace App\Http\Controllers;

use App\Models\Job;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class SavedJobController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('role:job_seeker');
    }

    /**
     * Display a listing of the user's saved jobs.
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        
        $query = $user->savedJobs()
            ->with(['employerProfile.user', 'media'])
            ->withPivot('created_at');

        // Filter by job status
        if ($request->filled('status')) {
            if ($request->status === 'active') {
                $query->where('status', 'active')
                      ->where('expires_at', '>', now());
            } elseif ($request->status === 'expired') {
                $query->where(function ($q) {
                    $q->where('status', '!=', 'active')
                      ->orWhere('expires_at', '<=', now());
                });
            }
        } else {
            // Default: show all saved jobs
        }

        // Filter by job type
        if ($request->filled('type')) {
            $query->where('job_type', $request->type);
        }

        // Filter by location
        if ($request->filled('location')) {
            $query->where('location', 'like', "%{$request->location}%");
        }

        // Filter by industry
        if ($request->filled('industry')) {
            $query->where('industry', $request->industry);
        }

        // Filter by experience level
        if ($request->filled('experience_level')) {
            $query->where('experience_level', $request->experience_level);
        }

        // Filter by salary range
        if ($request->filled('min_salary')) {
            $query->where('salary_min', '>=', $request->min_salary);
        }
        if ($request->filled('max_salary')) {
            $query->where('salary_max', '<=', $request->max_salary);
        }

        // Search by keywords
        if ($request->filled('search')) {
            $searchTerm = $request->search;
            $query->where(function ($q) use ($searchTerm) {
                $q->where('title', 'like', "%{$searchTerm}%")
                  ->orWhere('description', 'like', "%{$searchTerm}%")
                  ->orWhereHas('employerProfile', function ($eq) use ($searchTerm) {
                      $eq->where('company_name', 'like', "%{$searchTerm}%");
                  });
            });
        }

        // Sort options
        $sortBy = $request->get('sort', 'saved_at');
        $sortOrder = $request->get('order', 'desc');

        switch ($sortBy) {
            case 'title':
                $query->orderBy('title', $sortOrder);
                break;
            case 'company':
                $query->join('employer_profiles', 'job_listings.employer_profile_id', '=', 'employer_profiles.id')
                      ->orderBy('employer_profiles.company_name', $sortOrder)
                      ->select('job_listings.*', 'saved_jobs.created_at as saved_at');
                break;
            case 'salary':
                $query->orderBy('salary_max', $sortOrder);
                break;
            case 'location':
                $query->orderBy('location', $sortOrder);
                break;
            case 'expires_at':
                $query->orderBy('expires_at', $sortOrder);
                break;
            default: // saved_at
                $query->orderBy('saved_jobs.created_at', $sortOrder);
        }

        $savedJobs = $query->paginate(12)->withQueryString();

        // Get filter options
        $allSavedJobs = $user->savedJobs();
        
        $industries = $allSavedJobs->select('industry')
            ->distinct()
            ->whereNotNull('industry')
            ->pluck('industry')
            ->sort();

        $locations = $allSavedJobs->select('location')
            ->distinct()
            ->whereNotNull('location')
            ->pluck('location')
            ->sort();

        $jobTypes = ['full-time', 'part-time', 'contract', 'freelance', 'internship'];
        $experienceLevels = ['entry', 'mid', 'senior', 'lead', 'executive'];

        // Get counts for status filter
        $statusCounts = [
            'all' => $user->savedJobs()->count(),
            'active' => $user->savedJobs()
                ->where('status', 'active')
                ->where('expires_at', '>', now())
                ->count(),
            'expired' => $user->savedJobs()
                ->where(function ($q) {
                    $q->where('status', '!=', 'active')
                      ->orWhere('expires_at', '<=', now());
                })
                ->count()
        ];

        return view('saved-jobs.index', compact(
            'savedJobs',
            'industries',
            'locations',
            'jobTypes',
            'experienceLevels',
            'statusCounts'
        ));
    }

    /**
     * Save a job to the user's saved jobs list.
     */
    public function store(Request $request, Job $job)
    {
        $user = Auth::user();

        // Check if job is already saved
        if ($user->savedJobs()->where('job_id', $job->id)->exists()) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Job is already saved',
                    'action' => 'already_saved'
                ]);
            }
            
            return back()->with('info', 'This job is already in your saved jobs.');
        }

        // Save the job
        $user->savedJobs()->attach($job->id, [
            'created_at' => now(),
            'updated_at' => now()
        ]);

        if ($request->ajax()) {
            return response()->json([
                'success' => true,
                'message' => 'Job saved successfully',
                'action' => 'saved',
                'saved_count' => $user->savedJobs()->count()
            ]);
        }

        return back()->with('success', 'Job saved to your saved jobs list.');
    }

    /**
     * Remove a job from the user's saved jobs list.
     */
    public function destroy(Request $request, Job $job)
    {
        $user = Auth::user();

        // Check if job is saved
        if (!$user->savedJobs()->where('job_id', $job->id)->exists()) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Job is not in your saved jobs',
                    'action' => 'not_saved'
                ]);
            }
            
            return back()->with('info', 'This job is not in your saved jobs.');
        }

        // Remove the job from saved jobs
        $user->savedJobs()->detach($job->id);

        if ($request->ajax()) {
            return response()->json([
                'success' => true,
                'message' => 'Job removed from saved jobs',
                'action' => 'removed',
                'saved_count' => $user->savedJobs()->count()
            ]);
        }

        return back()->with('success', 'Job removed from your saved jobs list.');
    }

    /**
     * Toggle save status of a job.
     */
    public function toggle(Request $request, Job $job)
    {
        $user = Auth::user();
        $isSaved = $user->savedJobs()->where('job_id', $job->id)->exists();

        if ($isSaved) {
            // Remove from saved jobs
            $user->savedJobs()->detach($job->id);
            $action = 'removed';
            $message = 'Job removed from saved jobs';
        } else {
            // Add to saved jobs
            $user->savedJobs()->attach($job->id, [
                'created_at' => now(),
                'updated_at' => now()
            ]);
            $action = 'saved';
            $message = 'Job saved successfully';
        }

        if ($request->ajax()) {
            return response()->json([
                'success' => true,
                'message' => $message,
                'action' => $action,
                'is_saved' => !$isSaved,
                'saved_count' => $user->savedJobs()->count()
            ]);
        }

        return back()->with('success', $message);
    }

    /**
     * Bulk actions on saved jobs.
     */
    public function bulkAction(Request $request)
    {
        $request->validate([
            'action' => 'required|in:remove,apply',
            'job_ids' => 'required|array',
            'job_ids.*' => 'exists:job_listings,id'
        ]);

        $user = Auth::user();
        $jobIds = $request->job_ids;
        $action = $request->action;

        // Verify that all jobs are actually saved by the user
        $savedJobIds = $user->savedJobs()->whereIn('job_id', $jobIds)->pluck('job_id')->toArray();
        
        if (count($savedJobIds) !== count($jobIds)) {
            return back()->with('error', 'Some selected jobs are not in your saved jobs list.');
        }

        switch ($action) {
            case 'remove':
                $user->savedJobs()->detach($jobIds);
                $message = count($jobIds) . ' job(s) removed from your saved jobs.';
                break;
                
            case 'apply':
                // Redirect to bulk application page (you can implement this later)
                return redirect()->route('applications.bulk-create', ['job_ids' => implode(',', $jobIds)]);
                
            default:
                return back()->with('error', 'Invalid action.');
        }

        return back()->with('success', $message);
    }

    /**
     * Export saved jobs list.
     */
    public function export(Request $request)
    {
        $user = Auth::user();
        $format = $request->get('format', 'csv'); // csv, pdf, excel

        $savedJobs = $user->savedJobs()
            ->with(['employerProfile.user'])
            ->withPivot('created_at')
            ->get();

        // Implementation depends on your export library preference
        // This is a placeholder for the export functionality
        
        switch ($format) {
            case 'csv':
                return $this->exportToCsv($savedJobs);
            case 'pdf':
                return $this->exportToPdf($savedJobs);
            case 'excel':
                return $this->exportToExcel($savedJobs);
            default:
                return back()->with('error', 'Invalid export format.');
        }
    }

    /**
     * Export saved jobs to CSV format.
     */
    private function exportToCsv($savedJobs)
    {
        $filename = 'saved-jobs-' . now()->format('Y-m-d') . '.csv';
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename={$filename}",
        ];

        $callback = function () use ($savedJobs) {
            $file = fopen('php://output', 'w');
            fputcsv($file, ['Job Title', 'Company', 'Location', 'Type', 'Salary Range', 'Saved Date', 'Status']);

            foreach ($savedJobs as $job) {
                fputcsv($file, [
                    $job->title,
                    $job->employerProfile->company_name,
                    $job->location,
                    $job->type,
                    $job->salary_min && $job->salary_max ? "$job->salary_min - $job->salary_max" : 'Not specified',
                    $job->pivot->created_at->format('Y-m-d'),
                    $job->status
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Export saved jobs to PDF format.
     */
    private function exportToPdf($savedJobs)
    {
        // Implement PDF export using a library like DomPDF or TCPDF
        // This is a placeholder
        return back()->with('info', 'PDF export feature coming soon.');
    }

    /**
     * Export saved jobs to Excel format.
     */
    private function exportToExcel($savedJobs)
    {
        // Implement Excel export using a library like Laravel Excel
        // This is a placeholder
        return back()->with('info', 'Excel export feature coming soon.');
    }
}