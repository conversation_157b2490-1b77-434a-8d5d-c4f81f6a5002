@extends('layouts.app')

@section('title', $course->title)
@section('description', $course->description)

@section('content')
<div class="bg-white">
    <!-- Course Header -->
    <div class="bg-gradient-to-r from-purple-600 to-indigo-700">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Course Info -->
                <div class="lg:col-span-2">
                    <div class="flex items-center space-x-2 mb-4">
                        <span class="bg-purple-100 text-purple-800 text-sm font-medium px-3 py-1 rounded-full">{{ ucfirst($course->level ?? 'Beginner') }}</span>
                        <span class="bg-white bg-opacity-20 text-white text-sm font-medium px-3 py-1 rounded-full">{{ $course->category ?? 'General' }}</span>
                        @if($course->is_featured)
                            <span class="bg-yellow-400 text-yellow-900 text-sm font-medium px-3 py-1 rounded-full">Featured</span>
                        @endif
                    </div>

                    <h1 class="text-3xl md:text-4xl font-bold text-white mb-4">{{ $course->title }}</h1>
                    <p class="text-xl text-purple-100 mb-6">{{ $course->description }}</p>

                    <!-- Course Stats -->
                    <div class="flex flex-wrap items-center gap-6 text-white">
                        <div class="flex items-center">
                            <div class="flex text-yellow-400 mr-2">
                                @for($i = 1; $i <= 5; $i++)
                                    <svg class="w-5 h-5 {{ $i <= ($course->rating ?? 4.5) ? 'fill-current' : 'text-gray-300' }}" viewBox="0 0 20 20">
                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                    </svg>
                                @endfor
                            </div>
                            <span class="font-medium">{{ number_format($course->rating ?? 4.5, 1) }} ({{ $course->reviews_count ?? 0 }} reviews)</span>
                        </div>

                        <div class="flex items-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"></path>
                            </svg>
                            <span class="font-medium">{{ $course->students_count ?? 0 }} students</span>
                        </div>

                        <div class="flex items-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <span class="font-medium">{{ $course->duration ?? 0 }} hours</span>
                        </div>

                        <div class="flex items-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                            </svg>
                            <span class="font-medium">{{ $course->lessons_count ?? 0 }} lessons</span>
                        </div>
                    </div>
                </div>

                <!-- Course Preview/Enrollment Card -->
                <div class="lg:col-span-1">
                    <div class="bg-white rounded-2xl shadow-2xl p-6 sticky top-4">
                        <!-- Course Thumbnail/Video -->
                        <div class="h-48 bg-gray-200 rounded-xl mb-6 relative overflow-hidden">
                            @if($course->thumbnail)
                                <img src="{{ $course->thumbnail }}" alt="{{ $course->title }}" class="w-full h-full object-cover">
                            @else
                                <div class="w-full h-full bg-gradient-to-br from-purple-500 to-indigo-600 flex items-center justify-center">
                                    <svg class="w-16 h-16 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                            @endif

                            <!-- Play Button for Preview -->
                            @if($course->preview_video)
                                <button class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30 hover:bg-opacity-40 transition-all duration-200" onclick="openPreviewModal()">
                                    <div class="w-16 h-16 bg-white rounded-full flex items-center justify-center">
                                        <svg class="w-8 h-8 text-purple-600 ml-1" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M8 5v10l8-5-8-5z"/>
                                        </svg>
                                    </div>
                                </button>
                            @endif
                        </div>

                        <!-- Price -->
                        <div class="text-center mb-6">
                            @if($course->price > 0)
                                <div class="text-3xl font-bold text-gray-900">${{ $course->price }}</div>
                                @if($course->original_price && $course->original_price > $course->price)
                                    <div class="text-lg text-gray-500 line-through">${{ $course->original_price }}</div>
                                    <div class="text-sm text-green-600 font-medium">Save ${{ $course->original_price - $course->price }}</div>
                                @endif
                            @else
                                <div class="text-3xl font-bold text-green-600">Free</div>
                            @endif
                        </div>

                        <!-- Enrollment Actions -->
                        @auth
                            @if(auth()->user()->courseEnrollments()->where('course_id', $course->id)->exists())
                                <div class="space-y-3">
                                    <a href="{{ route('courses.learn', $course) }}" class="w-full bg-green-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors duration-200 text-center block">
                                        Continue Learning
                                    </a>
                                    <div class="text-sm text-gray-600 text-center">
                                        @php
                                            $enrollment = auth()->user()->courseEnrollments()->where('course_id', $course->id)->first();
                                            $progress = $enrollment ? $enrollment->progress_percentage : 0;
                                        @endphp
                                        Progress: {{ $progress }}% complete
                                    </div>
                                </div>
                            @else
                                <form action="{{ route('courses.enroll', $course) }}" method="POST" class="space-y-3">
                                    @csrf
                                    <button type="submit" class="w-full bg-purple-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-purple-700 transition-colors duration-200">
                                        @if($course->price > 0)
                                            Enroll Now
                                        @else
                                            Enroll for Free
                                        @endif
                                    </button>
                                </form>
                            @endif
                        @else
                            <div class="space-y-3">
                                <a href="{{ route('login') }}" class="w-full bg-purple-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-purple-700 transition-colors duration-200 text-center block">
                                    Login to Enroll
                                </a>
                                <a href="{{ route('register') }}" class="w-full border-2 border-purple-600 text-purple-600 px-6 py-3 rounded-lg font-semibold hover:bg-purple-50 transition-colors duration-200 text-center block">
                                    Create Account
                                </a>
                            </div>
                        @endauth

                        <!-- Course Features -->
                        <div class="mt-6 space-y-3">
                            <div class="flex items-center text-sm text-gray-600">
                                <svg class="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                Lifetime access
                            </div>

                            @if($course->has_certificate)
                                <div class="flex items-center text-sm text-gray-600">
                                    <svg class="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    Certificate of completion
                                </div>
                            @endif

                            <div class="flex items-center text-sm text-gray-600">
                                <svg class="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                                </svg>
                                Mobile and desktop access
                            </div>

                            <div class="flex items-center text-sm text-gray-600">
                                <svg class="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>
                                </svg>
                                Shareable certificate
                            </div>
                        </div>

                        <!-- 30-day money back guarantee -->
                        @if($course->price > 0)
                            <div class="mt-6 p-4 bg-green-50 rounded-lg">
                                <div class="flex items-center text-sm text-green-800">
                                    <svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                                    </svg>
                                    30-day money-back guarantee
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Course Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Content -->
            <div class="lg:col-span-2 space-y-8">
                <!-- Course Description -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                    <h2 class="text-2xl font-bold text-gray-900 mb-4">About This Course</h2>
                    <div class="prose max-w-none text-gray-600">
                        {!! nl2br(e($course->description)) !!}
                    </div>
                </div>

                <!-- What You'll Learn -->
                @if($course->learning_objectives)
                    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                        <h2 class="text-2xl font-bold text-gray-900 mb-4">What You'll Learn</h2>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            @foreach(json_decode($course->learning_objectives, true) ?? [] as $objective)
                                <div class="flex items-start">
                                    <svg class="w-5 h-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    <span class="text-gray-700">{{ $objective }}</span>
                                </div>
                            @endforeach
                        </div>
                    </div>
                @endif

                <!-- Course Curriculum -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">Course Curriculum</h2>

                    <div class="space-y-4">
                        @forelse($course->lessons ?? [] as $index => $lesson)
                            <div class="border border-gray-200 rounded-lg overflow-hidden">
                                <div class="flex items-center justify-between p-4 bg-gray-50 hover:bg-gray-100 transition-colors duration-200 cursor-pointer" onclick="toggleLesson({{ $index }})">
                                    <div class="flex items-center">
                                        <span class="bg-purple-100 text-purple-800 text-sm font-medium px-2 py-1 rounded mr-3">{{ $index + 1 }}</span>
                                        <div>
                                            <h3 class="font-semibold text-gray-900">{{ $lesson->title }}</h3>
                                            <div class="flex items-center text-sm text-gray-600 mt-1">
                                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                </svg>
                                                {{ $lesson->duration ?? 0 }} min

                                                @if($lesson->is_preview)
                                                    <span class="ml-2 bg-green-100 text-green-800 text-xs font-medium px-2 py-1 rounded">Preview</span>
                                                @endif
                                            </div>
                                        </div>
                                    </div>

                                    <div class="flex items-center">
                                        @if($lesson->is_preview)
                                            <button class="text-purple-600 hover:text-purple-700 font-medium text-sm mr-3">
                                                Preview
                                            </button>
                                        @endif

                                        <svg class="w-5 h-5 text-gray-400 transform transition-transform duration-200 lesson-chevron-{{ $index }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                        </svg>
                                    </div>
                                </div>

                                <div class="lesson-content-{{ $index }} hidden p-4 border-t border-gray-200">
                                    <p class="text-gray-600 mb-4">{{ $lesson->description }}</p>

                                    @if($lesson->resources && count($lesson->resources) > 0)
                                        <div class="space-y-2">
                                            <h4 class="font-medium text-gray-900">Resources:</h4>
                                            @foreach($lesson->resources as $resource)
                                                <div class="flex items-center text-sm text-gray-600">
                                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                                    </svg>
                                                    {{ is_array($resource) ? ($resource['name'] ?? $resource['title'] ?? 'Resource') : $resource }}
                                                </div>
                                            @endforeach
                                        </div>
                                    @endif
                                </div>
                            </div>
                        @empty
                            <div class="text-center py-8 text-gray-500">
                                <svg class="w-12 h-12 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                </svg>
                                <p>Course curriculum will be available soon.</p>
                            </div>
                        @endforelse
                    </div>
                </div>

                <!-- Requirements -->
                @if($course->prerequisites && count($course->prerequisites) > 0)
                    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                        <h2 class="text-2xl font-bold text-gray-900 mb-4">Requirements</h2>
                        <ul class="space-y-2">
                            @foreach($course->prerequisites ?? [] as $requirement)
                                <li class="flex items-start">
                                    <svg class="w-5 h-5 text-gray-400 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                    </svg>
                                    <span class="text-gray-700">{{ is_array($requirement) ? ($requirement['name'] ?? $requirement['title'] ?? 'Requirement') : $requirement }}</span>
                                </li>
                            @endforeach
                        </ul>
                    </div>
                @endif

                <!-- Instructor -->
                @if($course->instructor)
                    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                        <h2 class="text-2xl font-bold text-gray-900 mb-6">Your Instructor</h2>

                        <div class="flex items-start space-x-4">
                            <div class="w-16 h-16 bg-gray-300 rounded-full flex-shrink-0 overflow-hidden">
                                @if($course->instructor->avatar)
                                    <img src="{{ $course->instructor->avatar }}" alt="{{ $course->instructor->name }}" class="w-full h-full object-cover">
                                @else
                                    <div class="w-full h-full bg-purple-500 flex items-center justify-center">
                                        <span class="text-white font-semibold text-lg">{{ substr($course->instructor->name, 0, 1) }}</span>
                                    </div>
                                @endif
                            </div>

                            <div class="flex-1">
                                <h3 class="text-xl font-semibold text-gray-900 mb-2">{{ $course->instructor->name }}</h3>
                                <p class="text-gray-600 mb-4">{{ $course->instructor->bio ?? 'Experienced instructor and industry professional.' }}</p>

                                <div class="flex flex-wrap gap-4 text-sm text-gray-600">
                                    <div class="flex items-center">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"></path>
                                        </svg>
                                        {{ $course->instructor->students_count ?? 0 }} students
                                    </div>

                                    <div class="flex items-center">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                        </svg>
                                        {{ $course->instructor->courses_count ?? 1 }} courses
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                @endif
            </div>

            <!-- Sidebar -->
            <div class="lg:col-span-1">
                <!-- Related Courses -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Related Courses</h3>

                    <div class="space-y-4">
                        @for($i = 0; $i < 3; $i++)
                            <div class="flex space-x-3">
                                <div class="w-16 h-12 bg-gradient-to-br from-purple-500 to-indigo-600 rounded flex-shrink-0"></div>
                                <div class="flex-1 min-w-0">
                                    <h4 class="text-sm font-medium text-gray-900 truncate">Advanced Web Development</h4>
                                    <p class="text-xs text-gray-600">$49.99</p>
                                    <div class="flex items-center mt-1">
                                        <div class="flex text-yellow-400">
                                            @for($j = 0; $j < 5; $j++)
                                                <svg class="w-3 h-3 fill-current" viewBox="0 0 20 20">
                                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                                </svg>
                                            @endfor
                                        </div>
                                        <span class="text-xs text-gray-600 ml-1">(4.8)</span>
                                    </div>
                                </div>
                            </div>
                        @endfor
                    </div>

                    <a href="{{ route('courses.index') }}" class="mt-4 text-purple-600 hover:text-purple-700 text-sm font-medium block">
                        View all courses →
                    </a>
                </div>

                <!-- Course Category -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Category</h3>
                    <div class="flex flex-wrap gap-2">
                        <span class="bg-purple-100 text-purple-700 text-sm px-3 py-1 rounded-full">
                            {{ ucfirst($course->category) }}
                        </span>
                        <span class="bg-blue-100 text-blue-700 text-sm px-3 py-1 rounded-full">
                            {{ ucfirst($course->difficulty_level) }}
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Preview Video Modal -->
@if($course->preview_video)
    <div id="previewModal" class="fixed inset-0 bg-black bg-opacity-75 hidden z-50 flex items-center justify-center p-4">
        <div class="bg-white rounded-xl max-w-4xl w-full max-h-full overflow-hidden">
            <div class="flex justify-between items-center p-4 border-b">
                <h3 class="text-lg font-semibold">Course Preview</h3>
                <button onclick="closePreviewModal()" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            <div class="p-4">
                <video controls class="w-full h-auto">
                    <source src="{{ $course->preview_video }}" type="video/mp4">
                    Your browser does not support the video tag.
                </video>
            </div>
        </div>
    </div>
@endif

<script>
    function toggleLesson(index) {
        const content = document.querySelector(`.lesson-content-${index}`);
        const chevron = document.querySelector(`.lesson-chevron-${index}`);

        if (content.classList.contains('hidden')) {
            content.classList.remove('hidden');
            chevron.classList.add('rotate-180');
        } else {
            content.classList.add('hidden');
            chevron.classList.remove('rotate-180');
        }
    }

    function openPreviewModal() {
        document.getElementById('previewModal').classList.remove('hidden');
        document.body.style.overflow = 'hidden';
    }

    function closePreviewModal() {
        document.getElementById('previewModal').classList.add('hidden');
        document.body.style.overflow = 'auto';

        // Pause video when modal is closed
        const video = document.querySelector('#previewModal video');
        if (video) {
            video.pause();
        }
    }

    // Close modal when clicking outside
    document.getElementById('previewModal')?.addEventListener('click', function(e) {
        if (e.target === this) {
            closePreviewModal();
        }
    });

    // Close modal with Escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closePreviewModal();
        }
    });
</script>
@endsection
